import { useState, useEffect } from 'react'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Switch } from '../../components/ui/switch'
import { Button } from '../../components/ui/button'
import { toast } from 'react-hot-toast'
import { Save, Loader2 } from 'lucide-react'
import { useRealtimeSettings } from '../../hooks/use-realtime-settings'
import { useStoreManagement } from '../../store/store-management'
import { useFirebaseAuthStore } from '../../store/firebase-auth'

export function AdvancedSettingsPage() {
  const { hasPermission } = useFirebaseAuthStore()
  const { currentStore } = useStoreManagement()
  const {
    settings,
    loading,
    updateEmail,
    updateAuth,
    updateCompliance,
    initializeSettings
  } = useRealtimeSettings(currentStore?.id || 'default')

  const [emailData, setEmailData] = useState({})
  const [authData, setAuthData] = useState({})
  const [complianceData, setComplianceData] = useState({})
  const [isSaving, setIsSaving] = useState(false)

  const canEdit = hasPermission(['super_admin', 'admin'])

  // Initialize form data when settings load
  useEffect(() => {
    if (settings?.emailConfig) {
      setEmailData(settings.emailConfig)
    }
    if (settings?.auth) {
      setAuthData(settings.auth)
    }
    if (settings?.compliance) {
      setComplianceData(settings.compliance)
    }
  }, [settings?.emailConfig, settings?.auth, settings?.compliance])

  // Initialize settings if they don't exist
  useEffect(() => {
    if (!loading && (!settings?.emailConfig || !settings?.auth || !settings?.compliance) && currentStore?.id) {
      console.log('🔧 Initializing advanced settings for store:', currentStore.id)
      initializeSettings()
    }
  }, [loading, settings?.emailConfig, settings?.auth, settings?.compliance, currentStore?.id, initializeSettings])

  // Handle field changes
  const handleEmailChange = (field: string, value: any) => {
    setEmailData(prev => ({ ...prev, [field]: value }))
  }

  const handleAuthChange = (field: string, value: any) => {
    setAuthData(prev => ({ ...prev, [field]: value }))
  }

  const handleComplianceChange = (field: string, value: any) => {
    setComplianceData(prev => ({ ...prev, [field]: value }))
  }

  // Handle save
  const handleSave = async () => {
    if (!canEdit) return

    setIsSaving(true)
    try {
      await Promise.all([
        updateEmail(emailData),
        updateAuth(authData),
        updateCompliance(complianceData)
      ])
      
      toast.success('Advanced settings saved successfully!')
    } catch (error) {
      console.error('Error saving advanced settings:', error)
      toast.error('Failed to save settings. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Advanced Settings</h1>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            Configure email, authentication, and compliance settings
          </p>
        </div>
        <Button
          onClick={handleSave}
          disabled={isSaving || !canEdit}
          className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
        >
          {isSaving ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          {isSaving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>

      {/* Email Configuration */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Email Configuration</h3>
        
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="fromEmail">From Email</Label>
              <Input
                id="fromEmail"
                type="email"
                value={emailData.fromEmail || ''}
                onChange={(e) => handleEmailChange('fromEmail', e.target.value)}
                disabled={!canEdit}
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <Label htmlFor="fromName">From Name</Label>
              <Input
                id="fromName"
                value={emailData.fromName || ''}
                onChange={(e) => handleEmailChange('fromName', e.target.value)}
                disabled={!canEdit}
                placeholder="Your Store Name"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="smtpHost">SMTP Host</Label>
              <Input
                id="smtpHost"
                value={emailData.smtpHost || ''}
                onChange={(e) => handleEmailChange('smtpHost', e.target.value)}
                disabled={!canEdit}
                placeholder="smtp.gmail.com"
              />
            </div>
            <div>
              <Label htmlFor="smtpPort">SMTP Port</Label>
              <Input
                id="smtpPort"
                type="number"
                value={emailData.smtpPort || '587'}
                onChange={(e) => handleEmailChange('smtpPort', parseInt(e.target.value))}
                disabled={!canEdit}
                placeholder="587"
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="enableEmailNotifications" className="text-sm font-medium">Enable Email Notifications</Label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Send email notifications for orders, updates, etc.
              </p>
            </div>
            <Switch
              id="enableEmailNotifications"
              checked={emailData.enableEmailNotifications || false}
              onCheckedChange={(checked) => handleEmailChange('enableEmailNotifications', checked)}
              disabled={!canEdit}
            />
          </div>
        </div>
      </div>

      {/* Authentication Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Authentication & Security</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="allowRegistration" className="text-sm font-medium">Allow Registration</Label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Allow new users to register accounts
              </p>
            </div>
            <Switch
              id="allowRegistration"
              checked={authData.allowRegistration || false}
              onCheckedChange={(checked) => handleAuthChange('allowRegistration', checked)}
              disabled={!canEdit}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="requireEmailVerification" className="text-sm font-medium">Require Email Verification</Label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Users must verify their email before accessing the account
              </p>
            </div>
            <Switch
              id="requireEmailVerification"
              checked={authData.requireEmailVerification || false}
              onCheckedChange={(checked) => handleAuthChange('requireEmailVerification', checked)}
              disabled={!canEdit}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="enableSocialLogin" className="text-sm font-medium">Enable Social Login</Label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Allow users to login with social media accounts
              </p>
            </div>
            <Switch
              id="enableSocialLogin"
              checked={authData.enableSocialLogin || false}
              onCheckedChange={(checked) => handleAuthChange('enableSocialLogin', checked)}
              disabled={!canEdit}
            />
          </div>

          <div>
            <Label htmlFor="passwordMinLength">Minimum Password Length</Label>
            <Input
              id="passwordMinLength"
              type="number"
              min="6"
              max="50"
              value={authData.passwordMinLength || '8'}
              onChange={(e) => handleAuthChange('passwordMinLength', parseInt(e.target.value))}
              disabled={!canEdit}
              placeholder="8"
            />
            <p className="text-xs text-gray-500 mt-1">
              Minimum number of characters required for passwords (6-50)
            </p>
          </div>
        </div>
      </div>

      {/* Compliance & Legal */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Compliance & Legal</h3>
        
        <div className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <div>
              <Label htmlFor="privacyPolicyUrl">Privacy Policy URL</Label>
              <Input
                id="privacyPolicyUrl"
                type="url"
                value={complianceData.privacyPolicyUrl || ''}
                onChange={(e) => handleComplianceChange('privacyPolicyUrl', e.target.value)}
                disabled={!canEdit}
                placeholder="https://yourstore.com/privacy-policy"
              />
            </div>

            <div>
              <Label htmlFor="termsOfServiceUrl">Terms of Service URL</Label>
              <Input
                id="termsOfServiceUrl"
                type="url"
                value={complianceData.termsOfServiceUrl || ''}
                onChange={(e) => handleComplianceChange('termsOfServiceUrl', e.target.value)}
                disabled={!canEdit}
                placeholder="https://yourstore.com/terms-of-service"
              />
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="gdprEnabled" className="text-sm font-medium">GDPR Compliance</Label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Enable GDPR compliance features for EU customers
                </p>
              </div>
              <Switch
                id="gdprEnabled"
                checked={complianceData.gdprEnabled || false}
                onCheckedChange={(checked) => handleComplianceChange('gdprEnabled', checked)}
                disabled={!canEdit}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="cookieConsentEnabled" className="text-sm font-medium">Cookie Consent Banner</Label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Show cookie consent banner to visitors
                </p>
              </div>
              <Switch
                id="cookieConsentEnabled"
                checked={complianceData.cookieConsentEnabled || false}
                onCheckedChange={(checked) => handleComplianceChange('cookieConsentEnabled', checked)}
                disabled={!canEdit}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
