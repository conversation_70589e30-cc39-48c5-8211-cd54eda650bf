import { Outlet, useLocation, Link } from 'react-router-dom'
import { Badge } from '../ui/badge'
import {
  Store,
  Palette,
  Search,
  Shield,
  Mail,
  CreditCard,
  Truck,
  Calculator,
  FileText,
  Sliders,
  Monitor
} from 'lucide-react'
import { useStoreManagement } from '../../store/store-management'

const settingsNavigation = [
  {
    id: 'general',
    label: 'General',
    icon: Store,
    path: '/admin/settings/general',
    description: 'Basic store information'
  },
  {
    id: 'branding',
    label: 'Branding',
    icon: Palette,
    path: '/admin/settings/branding',
    description: 'Visual identity & theme'
  },
  {
    id: 'seo',
    label: 'SEO',
    icon: Search,
    path: '/admin/settings/seo',
    description: 'Search optimization'
  },
  {
    id: 'auth',
    label: 'Authentication',
    icon: Shield,
    path: '/admin/settings/auth',
    description: 'User access & security'
  },
  {
    id: 'email',
    label: 'Email',
    icon: Mail,
    path: '/admin/settings/email',
    description: 'Email configuration'
  },
  {
    id: 'payment',
    label: 'Payment',
    icon: CreditCard,
    path: '/admin/settings/payment',
    description: 'Payment gateways'
  },
  {
    id: 'shipping',
    label: 'Shipping',
    icon: Truck,
    path: '/admin/settings/shipping',
    description: 'Delivery options'
  },
  {
    id: 'tax',
    label: 'Tax',
    icon: Calculator,
    path: '/admin/settings/tax',
    description: 'Tax configuration'
  },
  {
    id: 'compliance',
    label: 'Legal',
    icon: FileText,
    path: '/admin/settings/compliance',
    description: 'Legal & compliance'
  },
  {
    id: 'behavior',
    label: 'Behavior',
    icon: Sliders,
    path: '/admin/settings/behavior',
    description: 'Storefront features'
  },
  {
    id: 'preferences',
    label: 'Preferences',
    icon: Monitor,
    path: '/admin/settings/preferences',
    description: 'Admin panel settings'
  }
]

export function SettingsLayout() {
  const location = useLocation()
  const { currentStore } = useStoreManagement()

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
            Settings
          </h1>
          <p className="text-gray-500 dark:text-gray-400">
            Configure your store settings and preferences
          </p>
        </div>
        <Badge variant="outline" className="text-xs">
          Store: {currentStore?.name || 'Default'}
        </Badge>
      </div>

      <div className="flex gap-6">
        {/* Settings Navigation Sidebar */}
        <div className="w-64 flex-shrink-0">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                Settings Categories
              </h3>
            </div>
            <nav className="p-2">
              {settingsNavigation.map((item) => {
                const Icon = item.icon
                const isActive = location.pathname === item.path
                
                return (
                  <Link
                    key={item.id}
                    to={item.path}
                    className={`${
                      isActive
                        ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 border-transparent'
                    } group flex items-start gap-3 rounded-lg px-3 py-2.5 text-sm font-medium border transition-colors`}
                  >
                    <Icon className={`${
                      isActive 
                        ? 'text-blue-600 dark:text-blue-400' 
                        : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300'
                    } h-5 w-5 flex-shrink-0 mt-0.5`} />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium">{item.label}</div>
                      <div className={`${
                        isActive 
                          ? 'text-blue-600 dark:text-blue-400' 
                          : 'text-gray-500 dark:text-gray-400'
                      } text-xs mt-0.5`}>
                        {item.description}
                      </div>
                    </div>
                  </Link>
                )
              })}
            </nav>
          </div>
        </div>

        {/* Settings Content */}
        <div className="flex-1 min-w-0">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <Outlet />
          </div>
        </div>
      </div>
    </div>
  )
}
