import { useState, useEffect, useCallback } from 'react'
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage'
import { storage } from '../lib/firebase'
import { toast } from 'react-hot-toast'
import { 
  realtimeSettingsService, 
  CategorizedSettings,
  GeneralSettings,
  BrandingSettings,
  SEOSettings,
  AuthSettings,
  EmailSettings,
  PaymentSettings,
  ShippingSettings,
  TaxSettings,
  ComplianceSettings,
  BehaviorSettings,
  PanelPreferencesSettings
} from '../lib/realtime-settings-service'

interface UseRealtimeSettingsReturn {
  settings: CategorizedSettings | null
  loading: boolean
  saving: boolean
  error: string | null
  updateBranding: (updates: Partial<BrandingSettings>) => Promise<void>
  updateGeneral: (updates: Partial<GeneralSettings>) => Promise<void>
  updateSEO: (updates: Partial<SEOSettings>) => Promise<void>
  updateAuth: (updates: Partial<AuthSettings>) => Promise<void>
  updateEmail: (updates: Partial<EmailSettings>) => Promise<void>
  updatePayment: (updates: Partial<PaymentSettings>) => Promise<void>
  updateShipping: (updates: Partial<ShippingSettings>) => Promise<void>
  updateTax: (updates: Partial<TaxSettings>) => Promise<void>
  updateCompliance: (updates: Partial<ComplianceSettings>) => Promise<void>
  updateBehavior: (updates: Partial<BehaviorSettings>) => Promise<void>
  updatePanelPreferences: (updates: Partial<PanelPreferencesSettings>) => Promise<void>
  uploadFile: (file: File, path: string) => Promise<string>
  initializeSettings: () => Promise<void>
}

export function useRealtimeSettings(storeId: string): UseRealtimeSettingsReturn {
  const [settings, setSettings] = useState<CategorizedSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Subscribe to all settings categories
  useEffect(() => {
    if (!storeId) {
      setLoading(false)
      return
    }

    console.log('🔄 Setting up realtime settings subscription for store:', storeId)
    setLoading(true)
    setError(null)

    const unsubscribe = realtimeSettingsService.subscribeToAllCategories(
      storeId,
      (settingsData, firestoreError) => {
        if (firestoreError) {
          console.error('❌ Settings subscription error:', firestoreError)
          setError(firestoreError.message)
          setLoading(false)
          return
        }

        console.log('✅ Received settings update:', settingsData)
        setSettings(settingsData)
        setError(null)
        setLoading(false)
      }
    )

    return () => {
      console.log('🔧 Cleaning up settings subscription for store:', storeId)
      unsubscribe()
    }
  }, [storeId])

  // Generic update function
  const updateCategory = useCallback(async <T>(
    category: keyof CategorizedSettings,
    updates: Partial<T>
  ): Promise<void> => {
    if (!storeId) {
      throw new Error('Store ID is required')
    }

    setSaving(true)
    try {
      await realtimeSettingsService.updateCategory(storeId, category, updates)
      // Settings will be updated automatically via the subscription
    } catch (error) {
      console.error(`❌ Failed to update ${category} settings:`, error)
      throw error
    } finally {
      setSaving(false)
    }
  }, [storeId])

  // Category-specific update functions
  const updateBranding = useCallback((updates: Partial<BrandingSettings>) => 
    updateCategory('branding', updates), [updateCategory])

  const updateGeneral = useCallback((updates: Partial<GeneralSettings>) => 
    updateCategory('general', updates), [updateCategory])

  const updateSEO = useCallback((updates: Partial<SEOSettings>) => 
    updateCategory('seo', updates), [updateCategory])

  const updateAuth = useCallback((updates: Partial<AuthSettings>) => 
    updateCategory('auth', updates), [updateCategory])

  const updateEmail = useCallback((updates: Partial<EmailSettings>) => 
    updateCategory('emailConfig', updates), [updateCategory])

  const updatePayment = useCallback((updates: Partial<PaymentSettings>) => 
    updateCategory('payment', updates), [updateCategory])

  const updateShipping = useCallback((updates: Partial<ShippingSettings>) => 
    updateCategory('shipping', updates), [updateCategory])

  const updateTax = useCallback((updates: Partial<TaxSettings>) => 
    updateCategory('tax', updates), [updateCategory])

  const updateCompliance = useCallback((updates: Partial<ComplianceSettings>) => 
    updateCategory('compliance', updates), [updateCategory])

  const updateBehavior = useCallback((updates: Partial<BehaviorSettings>) => 
    updateCategory('behavior', updates), [updateCategory])

  const updatePanelPreferences = useCallback((updates: Partial<PanelPreferencesSettings>) => 
    updateCategory('panelPreferences', updates), [updateCategory])

  // File upload function
  const uploadFile = useCallback(async (file: File, path: string): Promise<string> => {
    try {
      const storageRef = ref(storage, `stores/${storeId}/${path}`)
      const snapshot = await uploadBytes(storageRef, file)
      const downloadURL = await getDownloadURL(snapshot.ref)
      
      console.log('✅ File uploaded successfully:', downloadURL)
      return downloadURL
    } catch (error) {
      console.error('❌ File upload failed:', error)
      toast.error('Failed to upload file')
      throw error
    }
  }, [storeId])

  // Initialize default settings
  const initializeSettings = useCallback(async (): Promise<void> => {
    if (!storeId) {
      throw new Error('Store ID is required')
    }

    setSaving(true)
    try {
      await realtimeSettingsService.initializeStoreSettings(storeId)
      // Settings will be updated automatically via the subscription
    } catch (error) {
      console.error('❌ Failed to initialize settings:', error)
      throw error
    } finally {
      setSaving(false)
    }
  }, [storeId])

  return {
    settings,
    loading,
    saving,
    error,
    updateBranding,
    updateGeneral,
    updateSEO,
    updateAuth,
    updateEmail,
    updatePayment,
    updateShipping,
    updateTax,
    updateCompliance,
    updateBehavior,
    updatePanelPreferences,
    uploadFile,
    initializeSettings
  }
}

// Backward compatibility - update the existing hook to use the new service
export function useCategorizedSettings(storeId?: string): UseRealtimeSettingsReturn {
  // Use a default store ID if none provided
  const resolvedStoreId = storeId || 'womanza-jewelry-store'
  
  return useRealtimeSettings(resolvedStoreId)
}
