/**
 * Real-time Settings Service
 * 
 * Provides real-time synchronization of settings data between Firestore and the application
 * Supports both individual category subscriptions and full settings subscriptions
 */

import {
  doc,
  collection,
  onSnapshot,
  setDoc,
  updateDoc,
  serverTimestamp,
  Unsubscribe,
  DocumentSnapshot,
  FirestoreError
} from 'firebase/firestore'
import { db } from './firebase'
import { toast } from 'react-hot-toast'

// Types for settings categories
export interface GeneralSettings {
  storeName: string
  storeSlug: string
  description: string
  currency: string
  locale: string
  timeZone: string
  status: 'active' | 'inactive' | 'maintenance'
  contactEmail?: string
  contactPhone?: string
  address?: string
  createdAt?: any
  updatedAt?: any
}

export interface BrandingSettings {
  logoUrl?: string
  faviconUrl?: string
  bannerUrl?: string
  primaryColor: string
  accentColor: string
  layout: 'boxed' | 'full-width'
  typography: string
  theme: 'light' | 'dark' | 'auto'
  customCSS?: string
  createdAt?: any
  updatedAt?: any
}

export interface SEOSettings {
  metaTitle?: string
  metaDescription?: string
  keywords?: string
  googleAnalyticsId?: string
  facebookPixelId?: string
  gtmId?: string
  sitemapUrl?: string
  robotsTxt?: string
  structuredData?: Record<string, any>
  createdAt?: any
  updatedAt?: any
}

export interface AuthSettings {
  allowPublicSignup: boolean
  requireEmailVerification: boolean
  enableTwoFactor: boolean
  sessionTimeout: number
  maxLoginAttempts: number
  lockoutDuration: number
  passwordPolicy: {
    minLength: number
    requireUppercase: boolean
    requireNumbers: boolean
    requireSpecialChars: boolean
  }
  createdAt?: any
  updatedAt?: any
}

export interface EmailSettings {
  fromEmail?: string
  fromName?: string
  smtpHost?: string
  smtpPort?: number
  smtpUser?: string
  smtpPassword?: string
  orderNotifications: boolean
  marketingEmails: boolean
  templates?: {
    orderConfirmation?: string
    orderShipped?: string
    passwordReset?: string
    welcome?: string
  }
  createdAt?: any
  updatedAt?: any
}

export interface PaymentSettings {
  currency: string
  enableCOD: boolean
  enableStripe: boolean
  enablePayPal: boolean
  stripePublishableKey?: string
  stripeSecretKey?: string
  paypalClientId?: string
  paypalClientSecret?: string
  testMode: boolean
  createdAt?: any
  updatedAt?: any
}

export interface ShippingSettings {
  enabled: boolean
  freeShippingThreshold: number
  defaultCost: number
  zones?: any[]
  methods?: any[]
  createdAt?: any
  updatedAt?: any
}

export interface TaxSettings {
  enabled: boolean
  defaultRate: number
  displayMode: 'inclusive' | 'exclusive'
  regions?: any[]
  createdAt?: any
  updatedAt?: any
}

export interface ComplianceSettings {
  privacyPolicyUrl?: string
  termsOfServiceUrl?: string
  refundPolicyUrl?: string
  gdprEnabled: boolean
  cookieConsent: boolean
  dataRetentionDays?: number
  createdAt?: any
  updatedAt?: any
}

export interface BehaviorSettings {
  allowGuestCheckout: boolean
  enableWishlist: boolean
  enableReviews: boolean
  enableLiveChat: boolean
  enableNewsletter: boolean
  enableSocialSharing: boolean
  maintenanceMode: boolean
  maintenanceMessage?: string
  createdAt?: any
  updatedAt?: any
}

export interface PanelPreferencesSettings {
  itemsPerPage: number
  defaultView: 'grid' | 'list'
  showTooltips: boolean
  autoSaveDrafts: boolean
  compactMode: boolean
  theme: 'light' | 'dark' | 'auto'
  createdAt?: any
  updatedAt?: any
}

// Combined settings interface
export interface CategorizedSettings {
  general?: GeneralSettings
  branding?: BrandingSettings
  seo?: SEOSettings
  auth?: AuthSettings
  emailConfig?: EmailSettings
  payment?: PaymentSettings
  shipping?: ShippingSettings
  tax?: TaxSettings
  compliance?: ComplianceSettings
  behavior?: BehaviorSettings
  panelPreferences?: PanelPreferencesSettings
}

// Settings category type
export type SettingsCategory = keyof CategorizedSettings

// Real-time settings service class
export class RealtimeSettingsService {
  private subscriptions: Map<string, Unsubscribe> = new Map()
  private listeners: Map<string, Set<(data: any) => void>> = new Map()

  /**
   * Subscribe to a specific settings category
   */
  subscribeToCategory<T>(
    storeId: string, 
    category: SettingsCategory, 
    callback: (data: T | null, error?: FirestoreError) => void
  ): Unsubscribe {
    const subscriptionKey = `${storeId}-${category}`
    
    // Clean up existing subscription
    this.unsubscribe(subscriptionKey)
    
    const settingsRef = doc(db, 'stores', storeId, 'config', 'settings')
    
    const unsubscribe = onSnapshot(
      settingsRef,
      (snapshot: DocumentSnapshot) => {
        if (snapshot.exists()) {
          const allSettings = snapshot.data() as CategorizedSettings
          const categoryData = allSettings[category] as T
          console.log(`📡 Settings subscription triggered for category ${category}:`, storeId)
          callback(categoryData, undefined)
        } else {
          console.log(`📡 Settings document not found for store:`, storeId)
          callback(null, undefined)
        }
      },
      (error: FirestoreError) => {
        console.error(`❌ Settings subscription error for ${category}:`, error)
        callback(null, error)
        toast.error(`Failed to load ${category} settings`)
      }
    )
    
    this.subscriptions.set(subscriptionKey, unsubscribe)
    return unsubscribe
  }

  /**
   * Subscribe to all settings categories
   */
  subscribeToAllCategories(
    storeId: string,
    callback: (settings: CategorizedSettings, error?: FirestoreError) => void
  ): Unsubscribe {
    const subscriptionKey = `${storeId}-all-settings`

    // Clean up existing subscription
    this.unsubscribe(subscriptionKey)

    const settingsRef = doc(db, 'stores', storeId, 'config', 'settings')

    const unsubscribe = onSnapshot(
      settingsRef,
      (snapshot: DocumentSnapshot) => {
        if (snapshot.exists()) {
          const allSettings = snapshot.data() as CategorizedSettings
          console.log(`📡 All settings updated for store:`, storeId)
          callback(allSettings, undefined)
        } else {
          console.log(`📡 Settings document not found for store:`, storeId)
          callback({}, undefined)
        }
      },
      (error: FirestoreError) => {
        console.error(`❌ All settings subscription error:`, error)
        callback({}, error)
        toast.error(`Failed to load settings`)
      }
    )

    this.subscriptions.set(subscriptionKey, unsubscribe)

    // Return cleanup function
    return () => {
      this.unsubscribe(subscriptionKey)
    }
  }

  /**
   * Update a specific settings category
   */
  async updateCategory<T>(
    storeId: string,
    category: SettingsCategory,
    data: Partial<T>
  ): Promise<void> {
    try {
      const settingsRef = doc(db, 'stores', storeId, 'config', 'settings')

      const updateData = {
        [category]: {
          ...data,
          updatedAt: serverTimestamp()
        }
      }

      await setDoc(settingsRef, updateData, { merge: true })
      console.log(`✅ Updated ${category} settings for store:`, storeId)

      // If updating general settings with store name, also update the main store document
      if (category === 'general' && data && typeof data === 'object') {
        const generalData = data as Partial<GeneralSettings>
        const storeUpdates: any = {}

        if (generalData.storeName) {
          storeUpdates.name = generalData.storeName
        }
        if (generalData.storeSlug) {
          storeUpdates.slug = generalData.storeSlug
        }
        if (generalData.description) {
          storeUpdates.description = generalData.description
        }
        if (generalData.currency) {
          storeUpdates.currency = generalData.currency
        }
        if (generalData.contactEmail) {
          storeUpdates.contactEmail = generalData.contactEmail
        }
        if (generalData.contactPhone) {
          storeUpdates.contactPhone = generalData.contactPhone
        }

        if (Object.keys(storeUpdates).length > 0) {
          const storeRef = doc(db, 'stores', storeId)
          await setDoc(storeRef, {
            ...storeUpdates,
            updatedAt: serverTimestamp()
          }, { merge: true })
          console.log(`✅ Synchronized store document with general settings for store:`, storeId)
        }
      }

    } catch (error) {
      console.error(`❌ Failed to update ${category} settings:`, error)
      toast.error(`Failed to update ${category} settings`)
      throw error
    }
  }

  /**
   * Initialize default settings for a store
   */
  async initializeStoreSettings(storeId: string): Promise<void> {
    const defaultSettings: CategorizedSettings = {
      general: {
        storeName: 'My Store',
        storeSlug: 'my-store',
        description: 'Your online store powered by Womanza',
        currency: 'PKR',
        locale: 'en-PK',
        timeZone: 'Asia/Karachi',
        status: 'active'
      },
      branding: {
        primaryColor: '#3B82F6',
        accentColor: '#8B5CF6',
        layout: 'full-width',
        typography: 'Inter',
        theme: 'auto'
      },
      seo: {
        sitemapUrl: '/sitemap.xml',
        robotsTxt: 'User-agent: *\nAllow: /'
      },
      auth: {
        allowPublicSignup: true,
        requireEmailVerification: false,
        enableTwoFactor: false,
        sessionTimeout: 1440,
        maxLoginAttempts: 5,
        lockoutDuration: 30,
        passwordPolicy: {
          minLength: 8,
          requireUppercase: false,
          requireNumbers: false,
          requireSpecialChars: false
        }
      },
      emailConfig: {
        orderNotifications: true,
        marketingEmails: false
      },
      payment: {
        currency: 'PKR',
        enableCOD: true,
        enableStripe: false,
        enablePayPal: false,
        testMode: true
      },
      shipping: {
        enabled: true,
        freeShippingThreshold: 0,
        defaultCost: 0
      },
      tax: {
        enabled: false,
        defaultRate: 0,
        displayMode: 'inclusive'
      },
      compliance: {
        privacyPolicyUrl: '/privacy-policy',
        termsOfServiceUrl: '/terms-of-service',
        refundPolicyUrl: '/refund-policy',
        gdprEnabled: false,
        cookieConsent: false
      },
      behavior: {
        allowGuestCheckout: true,
        enableWishlist: true,
        enableReviews: true,
        enableLiveChat: false,
        enableNewsletter: true,
        enableSocialSharing: true,
        maintenanceMode: false
      },
      panelPreferences: {
        itemsPerPage: 25,
        defaultView: 'grid',
        showTooltips: true,
        autoSaveDrafts: true,
        compactMode: false,
        theme: 'auto'
      }
    }

    try {
      const settingsRef = doc(db, 'stores', storeId, 'config', 'settings')

      // Add timestamps to all categories
      const settingsWithTimestamps: CategorizedSettings = {}
      for (const [category, data] of Object.entries(defaultSettings)) {
        settingsWithTimestamps[category as SettingsCategory] = {
          ...data,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        } as any
      }

      await setDoc(settingsRef, settingsWithTimestamps)

      console.log(`✅ Initialized default settings for store: ${storeId}`)
      toast.success('Store settings initialized successfully')

    } catch (error) {
      console.error('❌ Failed to initialize store settings:', error)
      toast.error('Failed to initialize store settings')
      throw error
    }
  }

  /**
   * Unsubscribe from a specific subscription
   */
  private unsubscribe(key: string): void {
    const unsubscribe = this.subscriptions.get(key)
    if (unsubscribe) {
      unsubscribe()
      this.subscriptions.delete(key)
    }
  }

  /**
   * Clean up all subscriptions
   */
  cleanup(): void {
    this.subscriptions.forEach(unsubscribe => unsubscribe())
    this.subscriptions.clear()
    this.listeners.clear()
  }
}

// Export singleton instance
export const realtimeSettingsService = new RealtimeSettingsService()
