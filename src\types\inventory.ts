// Inventory Management Types for WOMANZA Admin Panel

export interface InventoryItem {
  id: string
  productId: string
  variantId?: string
  name: string
  sku: string
  currentStock: number
  lowStockThreshold: number
  trackInventory: boolean
  isOutOfStock: boolean
  lastUpdated: string
  price: number
  image?: string
  category?: string
  unit: 'gm' | 'ml' | 'pcs'
  status: 'active' | 'inactive'
}

export interface InventoryLog {
  id: string
  productId: string
  variantId?: string
  action: 'restock' | 'sale' | 'adjustment' | 'return' | 'damage' | 'transfer'
  quantity: number
  previousStock: number
  newStock: number
  userId: string
  userName: string
  timestamp: string
  note?: string
  orderId?: string
  reason?: string
}

export interface InventoryAdjustment {
  productId: string
  variantId?: string
  action: 'increase' | 'decrease' | 'set'
  quantity: number
  note?: string
  reason: 'restock' | 'damage' | 'theft' | 'correction' | 'return' | 'transfer' | 'other'
}

export interface InventoryStats {
  totalProducts: number
  totalVariants: number
  lowStockItems: number
  outOfStockItems: number
  totalValue: number
  recentAdjustments: number
}

export interface LowStockAlert {
  id: string
  productId: string
  variantId?: string
  productName: string
  variantName?: string
  sku: string
  currentStock: number
  threshold: number
  severity: 'warning' | 'critical'
  createdAt: string
  acknowledged: boolean
}

export interface BulkInventoryUpdate {
  items: {
    productId: string
    variantId?: string
    action: 'increase' | 'decrease' | 'set'
    quantity: number
  }[]
  reason: string
  note?: string
}

export interface InventoryFilter {
  status: 'all' | 'in_stock' | 'low_stock' | 'out_of_stock'
  category: string
  trackInventory: boolean | null
  search: string
}

export interface InventorySort {
  field: 'name' | 'sku' | 'stock' | 'lastUpdated' | 'value'
  direction: 'asc' | 'desc'
}

export interface StockMovement {
  date: string
  in: number
  out: number
  net: number
}

export interface InventoryReport {
  period: string
  totalMovements: number
  stockIn: number
  stockOut: number
  adjustments: number
  topMovingProducts: Array<{
    productId: string
    name: string
    movements: number
    value: number
  }>
  lowStockTrends: Array<{
    date: string
    count: number
  }>
}

// Enhanced Product interface with inventory fields
export interface ProductWithInventory {
  id: string
  name: string
  slug: string
  sku: string
  description?: string
  categoryId: string
  categoryName?: string
  images: string[]
  price: number
  compareAtPrice?: number
  costPrice?: number
  
  // Inventory specific fields
  inventoryQuantity: number
  lowStockThreshold: number
  trackInventory: boolean
  isOutOfStock: boolean
  hasVariants: boolean
  totalStock: number // calculated from variants if applicable
  
  // Variant inventory (if hasVariants = true)
  variants?: Array<{
    id: string
    name: string
    sku: string
    stock: number
    price: number
    trackInventory: boolean
    lowStockThreshold: number
    isOutOfStock: boolean
    attributes: Record<string, string>
  }>
  
  status: 'draft' | 'active' | 'archived'
  createdAt: string
  updatedAt: string
  lastStockUpdate?: string
}

// Form interfaces
export interface InventoryAdjustmentForm {
  items: Array<{
    productId: string
    variantId?: string
    action: 'increase' | 'decrease' | 'set'
    quantity: number
  }>
  reason: string
  note?: string
}

export interface BulkInventoryUpdate {
  items: Array<{
    productId: string
    variantId?: string
    newStock: number
    lowStockThreshold?: number
  }>
  reason: string
  note?: string
}

// API Response types
export interface InventoryResponse {
  items: InventoryItem[]
  stats: InventoryStats
  alerts: LowStockAlert[]
  totalCount: number
  hasMore: boolean
}

export interface InventoryLogResponse {
  logs: InventoryLog[]
  totalCount: number
  hasMore: boolean
}

// Notification types
export interface InventoryNotification {
  id: string
  type: 'low_stock' | 'out_of_stock' | 'restock_needed'
  productId: string
  variantId?: string
  message: string
  severity: 'info' | 'warning' | 'error'
  createdAt: string
  read: boolean
  actionRequired: boolean
}

// Export/Import types
export interface InventoryExportData {
  products: ProductWithInventory[]
  logs: InventoryLog[]
  alerts: LowStockAlert[]
  exportedAt: string
  exportedBy: string
}

export interface InventoryImportRow {
  sku: string
  productName?: string
  variantName?: string
  currentStock: number
  lowStockThreshold?: number
  trackInventory?: boolean
  note?: string
}
