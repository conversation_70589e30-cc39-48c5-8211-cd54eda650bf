#!/usr/bin/env node

/**
 * Womanza Admin Panel - User Role Checker and Fixer
 * 
 * This script checks and fixes user roles in Firestore.
 */

import { initializeApp } from 'firebase/app'
import { getFirestore, doc, setDoc, getDoc, serverTimestamp } from 'firebase/firestore'
import { getAuth, signInWithEmailAndPassword } from 'firebase/auth'
import readline from 'readline'

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAxCHZm5Sywq6m8GDfy8VhgInuVG8V5jE4",
  authDomain: "womanza-store.firebaseapp.com",
  projectId: "womanza-store",
  storageBucket: "womanza-store.firebasestorage.app",
  messagingSenderId: "133271608005",
  appId: "1:133271608005:web:576cdefabd87ea7e886eac"
}

// Initialize Firebase
const app = initializeApp(firebaseConfig)
const db = getFirestore(app)
const auth = getAuth(app)

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

// Utility function to prompt user input
function prompt(question) {
  return new Promise((resolve) => {
    rl.question(question, resolve)
  })
}

async function checkAndFixUserRole(email, targetRole = 'app_admin') {
  try {
    const user = auth.currentUser
    if (!user) {
      throw new Error('No authenticated user')
    }

    console.log(`\n🔍 Checking user role for: ${email}`)
    console.log(`   UID: ${user.uid}`)

    // Check if user document exists
    const userRef = doc(db, 'users', user.uid)
    const userSnap = await getDoc(userRef)

    if (userSnap.exists()) {
      const userData = userSnap.data()
      console.log(`   Current role: ${userData.role || 'undefined'}`)
      
      if (userData.role === targetRole) {
        console.log(`✅ User already has the correct role: ${targetRole}`)
        return true
      } else {
        console.log(`⚠️  User has incorrect role. Updating to: ${targetRole}`)
        
        // Update user role
        await setDoc(userRef, {
          ...userData,
          role: targetRole,
          updatedAt: serverTimestamp()
        }, { merge: true })
        
        console.log(`✅ User role updated to: ${targetRole}`)
        return true
      }
    } else {
      console.log(`❌ User document does not exist. Creating new user document...`)
      
      // Create user document
      const userData = {
        uid: user.uid,
        email: email,
        name: 'App Admin',
        role: targetRole,
        active: true,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        permissions: {
          manageStores: true,
          manageUsers: true,
          approveRegistrations: true,
          viewAnalytics: true,
          systemSettings: true
        }
      }
      
      await setDoc(userRef, userData)
      console.log(`✅ User document created with role: ${targetRole}`)
      return true
    }

  } catch (error) {
    console.error(`❌ Error checking/fixing user role:`, error.message)
    return false
  }
}

async function main() {
  console.log('🚀 Womanza Admin Panel - User Role Checker')
  console.log('============================================================\n')

  try {
    // Get admin credentials
    const email = await prompt('Enter admin email: ')
    const password = await prompt('Enter admin password: ')

    console.log('\n🔐 Authenticating...')
    await signInWithEmailAndPassword(auth, email, password)
    console.log('✅ Authentication successful')

    // Check and fix user role
    const targetRole = await prompt('\nEnter target role (default: app_admin): ') || 'app_admin'
    
    const success = await checkAndFixUserRole(email, targetRole)

    if (success) {
      console.log('\n🎉 User role check/fix completed successfully!')
      console.log('\n📋 Next steps:')
      console.log('1. Try creating the store again: npm run create:store')
      console.log('2. Initialize settings: npm run init:settings')
    } else {
      console.log('\n⚠️  User role check/fix failed.')
    }

  } catch (error) {
    console.error('\n❌ Script failed:', error.message)
    
    if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
      console.log('\n💡 Authentication failed. Please check your credentials.')
    }
  } finally {
    rl.close()
    process.exit(0)
  }
}

// Run the script
main().catch(console.error)
