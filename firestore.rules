rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function getUserData() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data;
    }

    function isAppAdmin() {
      return isAuthenticated() && getUserData().role == 'app_admin';
    }

    function hasStoreAccess(storeId) {
      return isAuthenticated() && (
        isAppAdmin() ||
        storeId in getUserData().storeIds ||
        getUserData().primaryStoreId == storeId ||
        getUserData().storeId == storeId
      );
    }

    function canManageStore(storeId) {
      return isAuthenticated() && (
        isAppAdmin() ||
        (getUserData().role in ['super_admin', 'admin'] && (
          storeId in getUserData().storeIds ||
          getUserData().primaryStoreId == storeId ||
          getUserData().storeId == storeId
        ))
      );
    }

    function canEditStore(storeId) {
      return isAuthenticated() && (
        isAppAdmin() ||
        (getUserData().role in ['super_admin', 'admin', 'editor'] && (
          storeId in getUserData().storeIds ||
          getUserData().primaryStoreId == storeId ||
          getUserData().storeId == storeId
        ))
      );
    }

    // Users collection
    match /users/{userId} {
      allow read: if isAuthenticated() && (request.auth.uid == userId || isAppAdmin());
      allow write: if isAuthenticated() && (request.auth.uid == userId || isAppAdmin());
    }

    // Stores collection
    match /stores/{storeId} {
      allow read: if hasStoreAccess(storeId);
      allow create: if isAppAdmin();
      allow update: if canManageStore(storeId);
      allow delete: if isAppAdmin();

      // Products subcollection
      match /products/{productId} {
        allow read: if hasStoreAccess(storeId);
        allow create, update, delete: if canEditStore(storeId);
      }

      // Orders subcollection
      match /orders/{orderId} {
        allow read: if hasStoreAccess(storeId);
        allow create, update: if canEditStore(storeId);
        allow delete: if canManageStore(storeId);
      }

      // Categories subcollection
      match /categories/{categoryId} {
        allow read: if hasStoreAccess(storeId);
        allow create, update: if canEditStore(storeId);
        allow delete: if canManageStore(storeId);
      }

      // Customers subcollection
      match /customers/{customerId} {
        allow read: if hasStoreAccess(storeId);
        allow create, update: if canEditStore(storeId);
        allow delete: if canManageStore(storeId);
      }

      // Notifications subcollection
      match /notifications/{notificationId} {
        allow read: if hasStoreAccess(storeId);
        allow create, update: if canEditStore(storeId);
        allow delete: if canManageStore(storeId);
      }

      // Settings subcollection - categorized settings (legacy)
      match /settings/{settingCategory} {
        // Allow read access to all users with store access
        allow read: if hasStoreAccess(storeId);

        // Allow write access to editors and above
        allow write: if canEditStore(storeId);

        // Validate setting category names
        allow write: if settingCategory in [
          'general',
          'branding',
          'seo',
          'auth',
          'email-config',
          'payment',
          'shipping',
          'tax',
          'compliance',
          'behavior',
          'panel-preferences'
        ];

        // Validate that updates include updatedAt timestamp
        allow write: if request.resource.data.keys().hasAll(['updatedAt']);
      }

      // Config subcollection - new settings structure
      match /config/{configDoc} {
        // Allow read access to all users with store access
        allow read: if hasStoreAccess(storeId);

        // Allow write access to editors and above
        allow write: if canEditStore(storeId);

        // Validate config document names
        allow write: if configDoc in ['settings', 'features', 'integrations'];
      }
    }

    // Pending store requests - Allow unauthenticated creation for registration
    match /pending_store_requests/{requestId} {
      allow read: if isAuthenticated() && (
        isAppAdmin() ||
        request.auth.uid == resource.data.owner.uid
      );
      allow create: if true; // Allow anyone to create store registration requests
      allow update, delete: if isAppAdmin();
    }

    // Global customers collection (fallback for legacy structure)
    match /customers/{customerId} {
      allow read, write: if isAuthenticated() && (
        isAppAdmin() ||
        getUserData().role in ['super_admin', 'admin', 'editor', 'viewer']
      );
    }

    // Global categories collection (fallback for legacy structure)
    match /categories/{categoryId} {
      allow read, write: if isAuthenticated() && (
        isAppAdmin() ||
        getUserData().role in ['super_admin', 'admin', 'editor', 'viewer']
      );
    }

    // App admins collection
    match /app_admins/{adminId} {
      allow read, write: if isAuthenticated() && (
        isAppAdmin() ||
        request.auth.uid == adminId
      );
    }

  }
}
