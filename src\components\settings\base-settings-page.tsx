import React, { useState, useEffect, ReactNode } from 'react'
import { Button } from '../ui/button'
import { toast } from 'react-hot-toast'
import {
  Save,
  AlertTriangle,
  X,
  Loader2
} from 'lucide-react'
import { useFirebaseAuthStore } from '../../store/firebase-auth'
import { useStoreManagement } from '../../store/store-management'

interface BaseSettingsPageProps {
  title: string
  description: string
  children: ReactNode
  onSave: (data: any) => Promise<void>
  initialData?: any
  isLoading?: boolean
}

export function BaseSettingsPage({
  title,
  description,
  children,
  onSave,
  initialData = {},
  isLoading = false
}: BaseSettingsPageProps) {
  const { hasPermission } = useFirebaseAuthStore()
  const { currentStore } = useStoreManagement()
  const [formData, setFormData] = useState(initialData)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  const canEdit = hasPermission(['settings:write'])

  // Update form data when initial data changes
  useEffect(() => {
    setFormData(initialData)
    setHasUnsavedChanges(false)
  }, [initialData])

  // Handle field changes
  const handleFieldChange = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: value
    }))
    setHasUnsavedChanges(true)
  }

  // Handle save
  const handleSave = async () => {
    if (!hasUnsavedChanges || !canEdit) return

    setIsSaving(true)
    try {
      await onSave(formData)
      setHasUnsavedChanges(false)
      toast.success('Settings saved successfully!')
    } catch (error) {
      console.error('Error saving settings:', error)
      toast.error('Failed to save settings. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  // Handle discard
  const handleDiscard = () => {
    setFormData(initialData)
    setHasUnsavedChanges(false)
    toast.success('Changes discarded')
  }

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              {title}
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {description}
            </p>
          </div>
          
          {/* Action Buttons */}
          <div className="flex items-center gap-3">
            {hasUnsavedChanges && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDiscard}
                  disabled={isSaving || !canEdit}
                  className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
                >
                  <X className="h-4 w-4 mr-2" />
                  Discard
                </Button>
                <Button
                  size="sm"
                  onClick={handleSave}
                  disabled={isSaving || !canEdit}
                  className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
                >
                  {isSaving ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  {isSaving ? 'Saving...' : 'Save Changes'}
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Unsaved Changes Warning */}
        {hasUnsavedChanges && (
          <div className="mt-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-700 rounded-lg p-3">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
              <span className="text-sm font-medium text-amber-800 dark:text-amber-200">
                You have unsaved changes
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 p-6 overflow-y-auto">
        {React.cloneElement(children as React.ReactElement, {
          formData,
          onFieldChange: handleFieldChange,
          canEdit,
          currentStore
        })}
      </div>
    </div>
  )
}
