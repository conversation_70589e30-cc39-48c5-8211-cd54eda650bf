import { useState, useEffect, useCallback } from 'react'
import { settingsService, type StoreSettings } from '../lib/settings-service'
import { useStoreManagement } from '../store/store-management'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { toast } from 'react-hot-toast'

interface UseStoreSettingsReturn {
  settings: StoreSettings | null
  loading: boolean
  saving: boolean
  error: string | null
  updateSettings: (updates: Partial<StoreSettings>) => Promise<void>
  refreshSettings: () => Promise<void>
  initializeSettings: () => Promise<void>
}

export function useStoreSettings(storeId?: string): UseStoreSettingsReturn {
  const { currentStore } = useStoreManagement()
  const { user } = useFirebaseAuthStore()
  const [settings, setSettings] = useState<StoreSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Determine store ID
  const resolvedStoreId = storeId || currentStore?.id || user?.primaryStoreId || user?.storeId

  // Initialize settings for the store
  const initializeSettings = useCallback(async () => {
    if (!resolvedStoreId) return

    try {
      await settingsService.initializeSettings(resolvedStoreId, currentStore?.name)
      console.log('✅ Settings initialized for store:', resolvedStoreId)
    } catch (error) {
      console.error('❌ Failed to initialize settings:', error)
      setError('Failed to initialize settings')
    }
  }, [resolvedStoreId, currentStore?.name])

  // Refresh settings manually
  const refreshSettings = useCallback(async () => {
    if (!resolvedStoreId) return

    try {
      setLoading(true)
      setError(null)
      const newSettings = await settingsService.getSettings(resolvedStoreId)
      setSettings(newSettings)
    } catch (error) {
      console.error('❌ Failed to refresh settings:', error)
      setError('Failed to load settings')
    } finally {
      setLoading(false)
    }
  }, [resolvedStoreId])

  // Update settings
  const updateSettings = useCallback(async (updates: Partial<StoreSettings>) => {
    if (!resolvedStoreId || !settings) return

    try {
      setSaving(true)
      setError(null)

      // Optimistic update
      const updatedSettings = { ...settings, ...updates }
      setSettings(updatedSettings)

      // Save to Firebase with audit logging
      await settingsService.updateSettings(resolvedStoreId, updates)

      // Log the change for audit trail
      if (user?.id && user?.email) {
        await settingsService.logSettingsChange(
          resolvedStoreId,
          updates,
          user.id,
          user.email
        )
      }

      console.log('✅ Settings updated successfully')
      toast.success('Settings updated successfully!')
    } catch (error) {
      console.error('❌ Failed to update settings:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to update settings'
      setError(errorMessage)
      toast.error(errorMessage)

      // Revert optimistic update on error
      await refreshSettings()
    } finally {
      setSaving(false)
    }
  }, [resolvedStoreId, settings, refreshSettings, user])

  // Set up real-time subscription
  useEffect(() => {
    if (!resolvedStoreId) {
      setLoading(false)
      return
    }

    console.log('🔧 useStoreSettings: Setting up subscription for store:', resolvedStoreId)
    setLoading(true)
    setError(null)

    const unsubscribe = settingsService.subscribeToSettings(
      resolvedStoreId,
      (newSettings) => {
        console.log('✅ useStoreSettings: Received settings update:', newSettings)
        setSettings(newSettings)
        setLoading(false)
        setError(null)
      },
      (error) => {
        console.error('❌ useStoreSettings: Subscription error:', error)
        setError('Failed to load settings')
        setLoading(false)
      }
    )

    return () => {
      console.log('🔧 useStoreSettings: Cleaning up subscription')
      unsubscribe()
    }
  }, [resolvedStoreId])

  // Initialize settings on first load
  useEffect(() => {
    if (resolvedStoreId && !settings && !loading) {
      initializeSettings()
    }
  }, [resolvedStoreId, settings, loading, initializeSettings])

  return {
    settings,
    loading,
    saving,
    error,
    updateSettings,
    refreshSettings,
    initializeSettings
  }
}

// Hook for getting settings of a specific store (read-only)
export function useStoreSettingsReadOnly(storeId: string) {
  const [settings, setSettings] = useState<StoreSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!storeId) {
      setLoading(false)
      return
    }

    console.log('🔧 useStoreSettingsReadOnly: Loading settings for store:', storeId)
    setLoading(true)
    setError(null)

    const loadSettings = async () => {
      try {
        const storeSettings = await settingsService.getSettings(storeId)
        setSettings(storeSettings)
      } catch (error) {
        console.error('❌ useStoreSettingsReadOnly: Failed to load settings:', error)
        setError('Failed to load settings')
      } finally {
        setLoading(false)
      }
    }

    loadSettings()
  }, [storeId])

  return { settings, loading, error }
}

// Hook for getting multiple store settings
export function useMultipleStoreSettings(storeIds: string[]) {
  const [settingsMap, setSettingsMap] = useState<Record<string, StoreSettings>>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!storeIds.length) {
      setLoading(false)
      return
    }

    console.log('🔧 useMultipleStoreSettings: Loading settings for stores:', storeIds)
    setLoading(true)
    setError(null)

    const loadAllSettings = async () => {
      try {
        const settingsPromises = storeIds.map(async (storeId) => {
          const settings = await settingsService.getSettings(storeId)
          return { storeId, settings }
        })

        const results = await Promise.all(settingsPromises)
        const newSettingsMap: Record<string, StoreSettings> = {}
        
        results.forEach(({ storeId, settings }) => {
          newSettingsMap[storeId] = settings
        })

        setSettingsMap(newSettingsMap)
      } catch (error) {
        console.error('❌ useMultipleStoreSettings: Failed to load settings:', error)
        setError('Failed to load store settings')
      } finally {
        setLoading(false)
      }
    }

    loadAllSettings()
  }, [storeIds.join(',')])

  return { settingsMap, loading, error }
}
