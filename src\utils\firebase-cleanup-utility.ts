import {
  collection,
  doc,
  getDocs,
  getDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  writeBatch,
  serverTimestamp
} from 'firebase/firestore'
import { db, isFirebaseConfigured } from '../lib/firebase'
import { settingsService } from '../lib/settings-service'

interface CleanupResult {
  duplicatesFound: number
  duplicatesRemoved: number
  settingsInitialized: number
  storesSynced: number
  errors: string[]
}

interface StoreInfo {
  id: string
  name: string
  hasSettings: boolean
  hasUsers: boolean
  fieldCount: number
  data: any
}

export class FirebaseCleanupUtility {
  private dryRun: boolean = true

  constructor(dryRun: boolean = true) {
    this.dryRun = dryRun
  }

  /**
   * Analyze all stores and find issues
   */
  async analyzeStores(): Promise<{
    stores: StoreInfo[]
    duplicates: Array<{ name: string; primary: StoreInfo; duplicates: StoreInfo[] }>
    issues: string[]
  }> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    console.log('🔍 Analyzing store structure...')
    
    const stores: StoreInfo[] = []
    const duplicatesByName = new Map<string, StoreInfo[]>()
    const issues: string[] = []

    try {
      const storesSnapshot = await getDocs(collection(db, 'stores'))

      for (const storeDoc of storesSnapshot.docs) {
        const storeData = storeDoc.data()
        const storeId = storeDoc.id
        
        // Check if store has settings
        const settingsDoc = await getDoc(doc(db, 'stores', storeId, 'settings', 'general'))
        const hasSettings = settingsDoc.exists()
        
        // Check if store has users
        const usersSnapshot = await getDocs(collection(db, 'stores', storeId, 'users'))
        const hasUsers = !usersSnapshot.empty

        const store: StoreInfo = {
          id: storeId,
          name: storeData.name || 'Unnamed Store',
          hasSettings,
          hasUsers,
          fieldCount: Object.keys(storeData).length,
          data: storeData
        }

        stores.push(store)

        // Check for issues
        if (!hasSettings) {
          issues.push(`Store ${storeId} missing settings document`)
        }
        
        if (!storeData.name) {
          issues.push(`Store ${storeId} missing name`)
        }

        if (!storeData.currency) {
          issues.push(`Store ${storeId} missing currency`)
        }

        // Group by name for duplicate detection
        const storeName = store.name.toLowerCase().trim()
        if (!duplicatesByName.has(storeName)) {
          duplicatesByName.set(storeName, [])
        }
        duplicatesByName.get(storeName)!.push(store)
      }

      // Find duplicates
      const duplicates = []
      for (const [name, storeList] of duplicatesByName.entries()) {
        if (storeList.length > 1) {
          // Sort by priority: settings > users > field count
          storeList.sort((a, b) => {
            if (a.hasSettings && !b.hasSettings) return -1
            if (!a.hasSettings && b.hasSettings) return 1
            if (a.hasUsers && !b.hasUsers) return -1
            if (!a.hasUsers && b.hasUsers) return 1
            return b.fieldCount - a.fieldCount
          })

          duplicates.push({
            name,
            primary: storeList[0],
            duplicates: storeList.slice(1)
          })
        }
      }

      console.log(`✅ Analysis complete: ${stores.length} stores, ${duplicates.length} duplicate groups, ${issues.length} issues`)
      
      return { stores, duplicates, issues }
    } catch (error) {
      console.error('❌ Error analyzing stores:', error)
      throw new Error(`Failed to analyze stores: ${error.message}`)
    }
  }

  /**
   * Clean up duplicate stores
   */
  async cleanupDuplicates(duplicates: Array<{ name: string; primary: StoreInfo; duplicates: StoreInfo[] }>): Promise<number> {
    if (duplicates.length === 0) {
      console.log('ℹ️ No duplicates found to clean up')
      return 0
    }

    console.log(`🧹 Cleaning up ${duplicates.length} duplicate groups...`)
    let removedCount = 0

    for (const duplicate of duplicates) {
      try {
        console.log(`Processing: ${duplicate.name}`)
        console.log(`  Primary: ${duplicate.primary.id}`)
        console.log(`  Removing: ${duplicate.duplicates.map(d => d.id).join(', ')}`)

        if (!this.dryRun) {
          const batch = writeBatch(db)
          
          // Merge data into primary store
          let mergedData = { ...duplicate.primary.data }
          
          for (const dup of duplicate.duplicates) {
            // Merge non-conflicting data
            for (const [key, value] of Object.entries(dup.data)) {
              if (!mergedData[key] && value) {
                mergedData[key] = value
              }
            }
            
            // Delete duplicate store
            batch.delete(doc(db, 'stores', dup.id))
            removedCount++
          }

          // Update primary store with merged data
          batch.update(doc(db, 'stores', duplicate.primary.id), {
            ...mergedData,
            updated_at: serverTimestamp()
          })

          await batch.commit()
          console.log(`✅ Merged ${duplicate.duplicates.length} duplicates into ${duplicate.primary.id}`)
        } else {
          removedCount += duplicate.duplicates.length
        }
      } catch (error) {
        console.error(`❌ Error processing duplicate ${duplicate.name}:`, error)
        throw error
      }
    }

    return removedCount
  }

  /**
   * Initialize settings for stores that don't have them
   */
  async initializeSettings(stores: StoreInfo[]): Promise<number> {
    console.log('⚙️ Initializing missing settings...')
    let initializedCount = 0

    for (const store of stores) {
      if (!store.hasSettings) {
        try {
          console.log(`Initializing settings for: ${store.id}`)
          
          if (!this.dryRun) {
            await settingsService.initializeSettings(store.id, store.name)
            console.log(`✅ Settings initialized for: ${store.id}`)
          }
          initializedCount++
        } catch (error) {
          console.error(`❌ Error initializing settings for ${store.id}:`, error)
          throw error
        }
      }
    }

    return initializedCount
  }

  /**
   * Sync main store documents with settings
   */
  async syncStoreDocuments(stores: StoreInfo[]): Promise<number> {
    console.log('🔄 Syncing store documents with settings...')
    let syncedCount = 0

    for (const store of stores) {
      try {
        const settingsDoc = await getDoc(doc(db, 'stores', store.id, 'settings', 'general'))
        
        if (settingsDoc.exists()) {
          const settingsData = settingsDoc.data()
          
          // Check if sync is needed
          const needsSync = 
            store.data.name !== settingsData.name ||
            store.data.currency !== settingsData.currency ||
            store.data.businessAddress !== settingsData.business_address ||
            store.data.contactPhone !== settingsData.store_phone

          if (needsSync) {
            console.log(`Syncing store document: ${store.id}`)
            
            if (!this.dryRun) {
              const storeRef = doc(db, 'stores', store.id)
              
              // Sync key fields from settings to main store document
              const syncFields = {
                name: settingsData.name,
                currency: settingsData.currency,
                businessAddress: settingsData.business_address,
                contactPhone: settingsData.store_phone,
                primaryColor: settingsData.primary_color,
                category: settingsData.category,
                businessType: settingsData.business_type,
                country: settingsData.country,
                updated_at: serverTimestamp()
              }

              await updateDoc(storeRef, syncFields)
              console.log(`✅ Synced store document: ${store.id}`)
            }
            syncedCount++
          }
        }
      } catch (error) {
        console.error(`❌ Error syncing store document ${store.id}:`, error)
        throw error
      }
    }

    return syncedCount
  }

  /**
   * Run complete cleanup process
   */
  async runCleanup(): Promise<CleanupResult> {
    const result: CleanupResult = {
      duplicatesFound: 0,
      duplicatesRemoved: 0,
      settingsInitialized: 0,
      storesSynced: 0,
      errors: []
    }

    try {
      console.log(`🚀 Starting Firebase cleanup (${this.dryRun ? 'DRY RUN' : 'LIVE'})...`)

      // Step 1: Analyze current structure
      const { stores, duplicates, issues } = await this.analyzeStores()
      result.duplicatesFound = duplicates.length

      // Log issues
      if (issues.length > 0) {
        console.log('⚠️ Issues found:')
        issues.forEach(issue => console.log(`  - ${issue}`))
      }

      // Step 2: Clean up duplicates
      if (duplicates.length > 0) {
        result.duplicatesRemoved = await this.cleanupDuplicates(duplicates)
      }

      // Step 3: Initialize missing settings
      result.settingsInitialized = await this.initializeSettings(stores)

      // Step 4: Sync store documents
      result.storesSynced = await this.syncStoreDocuments(stores)

      console.log('🎉 Cleanup completed successfully!')
      console.log(`📊 Results:`)
      console.log(`  - Duplicates found: ${result.duplicatesFound}`)
      console.log(`  - Duplicates removed: ${result.duplicatesRemoved}`)
      console.log(`  - Settings initialized: ${result.settingsInitialized}`)
      console.log(`  - Stores synced: ${result.storesSynced}`)

      if (this.dryRun) {
        console.log('ℹ️ This was a dry run. No changes were made.')
      }

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.error('❌ Cleanup failed:', errorMessage)
      result.errors.push(errorMessage)
      return result
    }
  }
}

// Export utility functions
export const analyzeFirebaseStores = async () => {
  const utility = new FirebaseCleanupUtility(true)
  return await utility.analyzeStores()
}

export const cleanupFirebaseStores = async (dryRun: boolean = true) => {
  const utility = new FirebaseCleanupUtility(dryRun)
  return await utility.runCleanup()
}
