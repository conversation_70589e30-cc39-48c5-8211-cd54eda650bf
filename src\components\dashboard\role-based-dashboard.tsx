import { useFirebaseAuthStore } from '../../store/firebase-auth'
import { usePermissions } from '../../hooks/usePermissions'
import { SuperAdminDashboard } from './super-admin-dashboard'
import { AdminDashboard } from './admin-dashboard'
import { EditorDashboard } from './editor-dashboard'
import { ViewerDashboard } from './viewer-dashboard'

export function RoleBasedDashboard() {
  const { user } = useFirebaseAuthStore()
  const { isSuperAdmin } = usePermissions()

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-gray-500">Please log in to access the dashboard</p>
        </div>
      </div>
    )
  }

  // Render dashboard based on user role
  switch (user.role) {
    case 'super_admin':
      return <SuperAdminDashboard />
    
    case 'admin':
      return <AdminDashboard />
    
    case 'editor':
      return <EditorDashboard />
    
    case 'viewer':
      return <ViewerDashboard />
    
    default:
      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <p className="text-gray-500">Invalid user role: {user.role}</p>
            <p className="text-sm text-gray-400 mt-2">Please contact your administrator</p>
          </div>
        </div>
      )
  }
}
