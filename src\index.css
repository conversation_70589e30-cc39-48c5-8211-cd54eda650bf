/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar styles */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide:hover {
  scrollbar-width: thin;
  -ms-overflow-style: auto;
}

.scrollbar-hide:hover::-webkit-scrollbar {
  display: block;
  width: 6px;
}

.scrollbar-hide:hover::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.scrollbar-hide:hover::-webkit-scrollbar-thumb {
  background: rgba(203, 213, 225, 0.5);
  border-radius: 3px;
}

.scrollbar-hide:hover::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.7);
}

/* Enhanced Dark mode styles */
.dark {
  color-scheme: dark;
}

.dark body {
  background-color: #0f172a;
  color: #f1f5f9;
}

/* Enhanced text visibility in dark mode */
.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
  color: #f8fafc !important;
}

.dark p, .dark span, .dark div {
  color: #e2e8f0;
}

.dark .text-gray-600 {
  color: #cbd5e1 !important;
}

.dark .text-gray-700 {
  color: #e2e8f0 !important;
}

.dark .text-gray-800 {
  color: #f1f5f9 !important;
}

.dark .text-gray-900 {
  color: #f8fafc !important;
}

/* Enhanced card and component visibility */
.dark .bg-white {
  background-color: #1e293b !important;
  border-color: #334155 !important;
}

.dark .bg-gray-50 {
  background-color: #0f172a !important;
}

.dark .bg-gray-100 {
  background-color: #1e293b !important;
}

.dark .border-gray-200 {
  border-color: #334155 !important;
}

.dark .border-gray-300 {
  border-color: #475569 !important;
}

/* Enhanced input and form visibility */
.dark input, .dark textarea, .dark select {
  background-color: #1e293b !important;
  border-color: #475569 !important;
  color: #f1f5f9 !important;
}

.dark input::placeholder, .dark textarea::placeholder {
  color: #94a3b8 !important;
}

/* Enhanced table visibility */
.dark table {
  background-color: #1e293b !important;
}

.dark th {
  background-color: #0f172a !important;
  color: #f1f5f9 !important;
  border-color: #334155 !important;
}

.dark td {
  border-color: #334155 !important;
  color: #e2e8f0 !important;
}

/* Enhanced button visibility */
.dark .bg-gray-200 {
  background-color: #374151 !important;
  color: #f9fafb !important;
}

.dark .hover\:bg-gray-100:hover {
  background-color: #374151 !important;
}

/* Background colors */
.dark .bg-white {
  background-color: #1e293b !important;
}

.dark .bg-gray-50 {
  background-color: #1e293b !important;
}

.dark .bg-gray-100 {
  background-color: #334155 !important;
}

.dark .bg-gray-200 {
  background-color: #475569 !important;
}

.dark .bg-slate-50 {
  background-color: #1e293b !important;
}

.dark .bg-slate-100 {
  background-color: #334155 !important;
}

.dark .bg-blue-50 {
  background-color: #1e3a8a !important;
}

.dark .bg-green-50 {
  background-color: #14532d !important;
}

.dark .bg-red-50 {
  background-color: #7f1d1d !important;
}

.dark .bg-yellow-50 {
  background-color: #713f12 !important;
}

/* Text colors */
.dark .text-gray-900 {
  color: #f1f5f9 !important;
}

.dark .text-gray-800 {
  color: #e2e8f0 !important;
}

.dark .text-gray-700 {
  color: #cbd5e1 !important;
}

.dark .text-gray-600 {
  color: #94a3b8 !important;
}

.dark .text-gray-500 {
  color: #64748b !important;
}

.dark .text-gray-400 {
  color: #475569 !important;
}

.dark .text-slate-900 {
  color: #f1f5f9 !important;
}

.dark .text-slate-800 {
  color: #e2e8f0 !important;
}

.dark .text-slate-700 {
  color: #cbd5e1 !important;
}

.dark .text-slate-600 {
  color: #94a3b8 !important;
}

.dark .text-green-700 {
  color: #bbf7d0 !important;
}

.dark .text-blue-700 {
  color: #bfdbfe !important;
}

.dark .text-red-700 {
  color: #fecaca !important;
}

.dark .text-yellow-700 {
  color: #fef3c7 !important;
}

/* Border colors */
.dark .border-gray-200 {
  border-color: #334155 !important;
}

.dark .border-gray-300 {
  border-color: #475569 !important;
}

.dark .border-slate-200 {
  border-color: #334155 !important;
}

.dark .border-slate-300 {
  border-color: #475569 !important;
}

/* Ring colors */
.dark .ring-gray-200 {
  --tw-ring-color: #334155 !important;
}

.dark .ring-gray-300 {
  --tw-ring-color: #475569 !important;
}

/* Hover states */
.dark .hover\\:bg-gray-50:hover {
  background-color: #334155 !important;
}

.dark .hover\\:bg-gray-100:hover {
  background-color: #475569 !important;
}

.dark .hover\\:text-gray-900:hover {
  color: #f1f5f9 !important;
}

/* Focus states */
.dark .focus\\:ring-gray-200:focus {
  --tw-ring-color: #334155 !important;
}

/* Placeholder text */
.dark .placeholder\\:text-gray-400::placeholder {
  color: #64748b !important;
}

.dark .placeholder\\:text-gray-500::placeholder {
  color: #475569 !important;
}

/* Shadow adjustments for dark mode */
.dark .shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3) !important;
}

.dark .shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2) !important;
}

.dark .shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
}

/* Card and panel backgrounds */
.dark .bg-card {
  background-color: #1e293b !important;
}

/* Input and form elements */
.dark input[type="text"],
.dark input[type="email"],
.dark input[type="password"],
.dark input[type="number"],
.dark input[type="search"],
.dark textarea,
.dark select {
  background-color: #334155 !important;
  border-color: #475569 !important;
  color: #f1f5f9 !important;
}

.dark input[type="text"]:focus,
.dark input[type="email"]:focus,
.dark input[type="password"]:focus,
.dark input[type="number"]:focus,
.dark input[type="search"]:focus,
.dark textarea:focus,
.dark select:focus {
  border-color: #3b82f6 !important;
  --tw-ring-color: #3b82f6 !important;
}

/* Button variants for dark mode */
.dark button[data-variant="ghost"]:hover {
  background-color: #334155 !important;
}

.dark button[data-variant="outline"] {
  border-color: #475569 !important;
  color: #f1f5f9 !important;
}

.dark button[data-variant="outline"]:hover {
  background-color: #334155 !important;
}

/* Layout Optimizations */
.page-container {
  @apply p-3 space-y-3;
}

.page-header {
  @apply mb-4 pb-3;
}

.page-title {
  @apply text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-100 mb-1;
}

.page-subtitle {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.stats-grid-optimized {
  @apply grid gap-3 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6;
}

.card-optimized {
  @apply p-4 space-y-3;
}

.theme-dropdown {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg;
}

.theme-dropdown-item {
  @apply hover:bg-gray-50 dark:hover:bg-gray-700;
}

.modal-optimized {
  @apply max-w-4xl w-full max-h-[85vh] overflow-y-auto;
}

.modal-content-optimized {
  @apply p-4;
}

.theme-modal {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg;
}

.theme-modal-overlay {
  @apply bg-black bg-opacity-50;
}

@layer base {
  * {
    box-sizing: border-box;
  }

  body {
    font-family: 'Inter', system-ui, sans-serif;
    line-height: 1.5;
    margin: 0;
    padding: 0;
  }

  h1, h2, h3, h4, h5, h6 {
    margin: 0;
    font-weight: 600;
  }

  p {
    margin: 0;
  }

  button {
    font-family: inherit;
  }
}
