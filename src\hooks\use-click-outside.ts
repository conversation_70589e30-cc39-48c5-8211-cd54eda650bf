import { useEffect, useRef, RefObject, useState } from 'react'

/**
 * Hook that handles click outside events for dropdowns, modals, etc.
 * @param handler - Function to call when clicking outside
 * @param enabled - Whether the hook is enabled (default: true)
 * @returns ref - Ref to attach to the element
 */
export function useClickOutside<T extends HTMLElement = HTMLElement>(
  handler: () => void,
  enabled: boolean = true
): RefObject<T> {
  const ref = useRef<T>(null)

  useEffect(() => {
    if (!enabled) return

    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        handler()
      }
    }

    // Add event listener
    document.addEventListener('mousedown', handleClickOutside)
    
    // Cleanup
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [handler, enabled])

  return ref
}

/**
 * Hook that handles escape key press for modals, dropdowns, etc.
 * @param handler - Function to call when escape is pressed
 * @param enabled - Whether the hook is enabled (default: true)
 */
export function useEscapeKey(handler: () => void, enabled: boolean = true): void {
  useEffect(() => {
    if (!enabled) return

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        handler()
      }
    }

    // Add event listener
    document.addEventListener('keydown', handleEscapeKey)
    
    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleEscapeKey)
    }
  }, [handler, enabled])
}

/**
 * Combined hook for both click outside and escape key
 * @param handler - Function to call when clicking outside or pressing escape
 * @param enabled - Whether the hook is enabled (default: true)
 * @returns ref - Ref to attach to the element
 */
export function useAutoClose<T extends HTMLElement = HTMLElement>(
  handler: () => void,
  enabled: boolean = true
): RefObject<T> {
  const ref = useClickOutside<T>(handler, enabled)
  useEscapeKey(handler, enabled)
  
  return ref
}

/**
 * Hook for focus trap functionality (useful for modals)
 * @param enabled - Whether the focus trap is enabled
 * @returns ref - Ref to attach to the container element
 */
export function useFocusTrap<T extends HTMLElement = HTMLElement>(
  enabled: boolean = true
): RefObject<T> {
  const ref = useRef<T>(null)

  useEffect(() => {
    if (!enabled || !ref.current) return

    const container = ref.current
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
    
    const firstElement = focusableElements[0] as HTMLElement
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement

    const handleTabKey = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return

      if (event.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          event.preventDefault()
          lastElement?.focus()
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          event.preventDefault()
          firstElement?.focus()
        }
      }
    }

    // Focus first element when trap is enabled
    firstElement?.focus()

    // Add event listener
    container.addEventListener('keydown', handleTabKey)
    
    // Cleanup
    return () => {
      container.removeEventListener('keydown', handleTabKey)
    }
  }, [enabled])

  return ref
}

/**
 * Hook for keyboard navigation in lists/menus
 * @param items - Array of items
 * @param onSelect - Function to call when an item is selected
 * @param enabled - Whether keyboard navigation is enabled
 * @returns current selected index and ref
 */
export function useKeyboardNavigation<T>(
  items: T[],
  onSelect: (item: T, index: number) => void,
  enabled: boolean = true
): [number, RefObject<HTMLElement>, (index: number) => void] {
  const ref = useRef<HTMLElement>(null)
  const [selectedIndex, setSelectedIndex] = useState(-1)

  useEffect(() => {
    if (!enabled || !ref.current) return

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault()
          setSelectedIndex(prev => (prev + 1) % items.length)
          break
        case 'ArrowUp':
          event.preventDefault()
          setSelectedIndex(prev => (prev - 1 + items.length) % items.length)
          break
        case 'Enter':
        case ' ':
          event.preventDefault()
          if (selectedIndex >= 0 && selectedIndex < items.length) {
            onSelect(items[selectedIndex], selectedIndex)
          }
          break
        case 'Home':
          event.preventDefault()
          setSelectedIndex(0)
          break
        case 'End':
          event.preventDefault()
          setSelectedIndex(items.length - 1)
          break
      }
    }

    const container = ref.current
    container.addEventListener('keydown', handleKeyDown)
    
    return () => {
      container.removeEventListener('keydown', handleKeyDown)
    }
  }, [items, onSelect, selectedIndex, enabled])

  return [selectedIndex, ref, setSelectedIndex]
}


