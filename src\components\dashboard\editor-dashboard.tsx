import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { useDashboardData } from '../../hooks/useDashboardData'
import { useStoreManagement } from '../../store/store-management'
import { cn } from '../../lib/utils'
import {
  Package,
  ShoppingCart,
  Users,
  RefreshCw,
  ArrowUpRight,
  ArrowDownRight,
  Zap,
  FolderTree,
  Edit
} from 'lucide-react'
import { RealTimeIndicator } from '../ui/real-time-indicator'
import { formatCurrency, formatNumber, formatPercentage, getStatusColor, getStatusLabel } from '../../lib/utils'
import { Link } from 'react-router-dom'

export function EditorDashboard() {
  const { stats, recentOrders, topProducts, loading, error, refetch } = useDashboardData()
  const { currentStore } = useStoreManagement()

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-500 mb-2">Error loading dashboard data</p>
          <Button onClick={refetch} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            Editor Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Edit products, categories, orders, and customers
          </p>
        </div>
        <div className="flex items-center gap-3">
          <RealTimeIndicator />
          <Button onClick={refetch} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>



      {/* Limited Stats Cards - No Revenue/Analytics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats?.totalOrders || 0)}</div>
            <p className="text-xs text-muted-foreground">
              Total orders in system
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats?.totalProducts || 0)}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.lowStockCount ? (
                <span className="text-orange-600">
                  {stats.lowStockCount} low stock items
                </span>
              ) : (
                "All items in stock"
              )}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats?.totalCustomers || 0)}</div>
            <p className="text-xs text-muted-foreground">
              Total customers
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Orders and Products - View Only */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              Recent Orders
            </CardTitle>
            <CardDescription>Latest customer orders (partial access)</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentOrders?.length > 0 ? (
                recentOrders.slice(0, 5).map((order) => (
                  <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">#{order.orderNumber}</p>
                      <p className="text-sm text-gray-500">{order.customerEmail}</p>
                    </div>
                    <div className="text-right">
                      <Badge variant="secondary" className={getStatusColor(order.status)}>
                        {getStatusLabel(order.status)}
                      </Badge>
                      <p className="text-xs text-gray-400 mt-1">View only</p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">No recent orders</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Products to Edit
            </CardTitle>
            <CardDescription>Products you can edit</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topProducts?.length > 0 ? (
                topProducts.slice(0, 5).map((product) => (
                  <div key={product.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">{product.name}</p>
                      <p className="text-sm text-gray-500">SKU: {product.sku}</p>
                    </div>
                    <div className="text-right">
                      <Link to={`/admin/products/${product.id}/edit`}>
                        <Button variant="outline" size="sm">
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">No products available</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Editor Limitations Notice */}
      <Card className="border-orange-200 bg-orange-50 dark:bg-orange-900/20">
        <CardHeader>
          <CardTitle className="text-orange-800 dark:text-orange-200 flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Editor Access Level
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-orange-700 dark:text-orange-300 space-y-2">
            <p className="font-medium">You have editor access with the following permissions:</p>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>✅ Edit products and categories</li>
              <li>✅ View orders (cannot fulfill or cancel)</li>
              <li>✅ View customers (cannot segment or assign groups)</li>
              <li>❌ No access to analytics or revenue data</li>
              <li>❌ No access to marketing or promotions</li>
              <li>❌ No access to staff management or store settings</li>
            </ul>
            <p className="text-sm mt-3">
              Contact your store administrator for additional permissions.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
