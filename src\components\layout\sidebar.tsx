import { Link, useLocation } from 'react-router-dom'
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  BarChart3,
  Settings,
  UserCog,
  Palette,
  LogOut,
  ShoppingBag,
  ChevronDown,
  ChevronRight,
  Zap,
  TrendingUp,
  Bell,
  Star,
  Activity,
  Plus,
  Edit,
  FolderTree,
  FileText,
  RotateCcw,
  UserCheck,
  Target,
  Warehouse,
  Truck,
  Receipt,
  Shield,
  CreditCard,
  AlertTriangle,
  ScrollText,
  Globe,
  Database,
  HelpCircle,
  MessageCircle,
  Ticket,
  Store,
  Gift,
  Percent,
  Mail
} from 'lucide-react'
import { useFirebaseAuthStore } from '../../store/firebase-auth'
import { useStoreManagement } from '../../store/store-management'
import { useProductsStore } from '../../store/products-store'
import { useOrdersStore } from '../../store/orders-store'
import { useCustomersStore } from '../../store/customers-store'
import { useStoreName } from '../../hooks/use-store-name'
// Store selector removed - individual store mode
// import { StoreSelector } from '../store/store-selector'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { cn } from '../../lib/utils'
import { useState, useEffect } from 'react'

interface SubItem {
  name: string
  href: string
  icon: any
  roles?: string[] // Optional role restriction for sub-items
}

interface NavigationItem {
  name: string
  href?: string
  icon: any
  roles: string[]
  badge?: { text: string; color: string } | null
  description: string
  subItems?: SubItem[]
}

// Dynamic navigation function
const getNavigation = (productsCount: number, ordersCount: number, customersCount: number): NavigationItem[] => [
  {
    name: 'App Administration',
    icon: Database,
    roles: ['app_admin'],
    badge: null,
    description: 'Manage stores and requests',
    subItems: [
      { name: 'Store Requests', href: '/app-admin', icon: Store },
      { name: 'All Stores', href: '/app-admin', icon: Database }
    ]
  },
  {
    name: 'Dashboard',
    href: '/admin/dashboard',
    icon: LayoutDashboard,
    roles: ['super_admin', 'admin', 'editor', 'viewer'],
    badge: null,
    description: 'Overview and analytics'
  },
  {
    name: 'Products',
    icon: Package,
    roles: ['super_admin', 'admin', 'editor', 'viewer'],
    badge: productsCount > 0 ? { text: productsCount.toString(), color: 'blue' } : null,
    description: 'Manage inventory',
    subItems: [
      { name: 'Product List', href: '/admin/products', icon: Package },
      { name: 'Add Product', href: '/admin/add-product', icon: Plus },
      { name: 'Categories', href: '/admin/categories', icon: FolderTree }
    ]
  },
  {
    name: 'Orders',
    icon: ShoppingCart,
    roles: ['super_admin', 'admin', 'editor', 'viewer'],
    badge: ordersCount > 0 ? { text: ordersCount.toString(), color: 'green' } : null,
    description: 'Process orders',
    subItems: [
      { name: 'Order List', href: '/admin/orders', icon: ShoppingCart },
      { name: 'Order Detail', href: '/admin/orders/detail', icon: FileText },
      { name: 'Returns & Refunds', href: '/admin/orders/returns', icon: RotateCcw }
    ]
  },
  {
    name: 'Customers',
    icon: Users,
    roles: ['super_admin', 'admin', 'editor', 'viewer'],
    badge: customersCount > 0 ? { text: customersCount > 999 ? `${(customersCount/1000).toFixed(1)}k` : customersCount.toString(), color: 'purple' } : null,
    description: 'Customer management',
    subItems: [
      { name: 'Customer List', href: '/admin/customers', icon: Users },
      { name: 'Customer Groups', href: '/admin/customers/groups', icon: UserCheck },
      { name: 'Customer Insights', href: '/admin/customers/insights', icon: Target }
    ]
  },

  {
    name: 'Inventory',
    icon: Warehouse,
    roles: ['super_admin', 'admin', 'editor'],
    badge: null,
    description: 'Inventory management',
    subItems: [
      { name: 'Dashboard', href: '/admin/inventory', icon: Warehouse },
      { name: 'Bulk Adjustments', href: '/admin/inventory/bulk-adjust', icon: Edit },
      { name: 'Stock Reports', href: '/admin/inventory/reports', icon: BarChart3 }
    ]
  },
  {
    name: 'Staff Management',
    icon: UserCog,
    roles: ['super_admin'], // Only Super Admin can manage staff
    badge: null,
    description: 'Team management',
    subItems: [
      { name: 'Users', href: '/admin/users', icon: UserCog },
      { name: 'Roles & Permissions', href: '/admin/roles-permissions', icon: Shield },
      { name: 'Activity Logs', href: '/admin/activity-logs', icon: ScrollText }
    ]
  },
  {
    name: 'Marketing',
    href: '/admin/marketing',
    icon: Target,
    roles: ['super_admin', 'admin'], // Editors cannot access marketing per requirements
    badge: null,
    description: 'Promotions, coupons, and campaigns',
    subItems: []
  },

  {
    name: 'Settings',
    href: '/admin/settings',
    icon: Settings,
    roles: ['super_admin'], // Only Super Admin can access store settings
    badge: null,
    description: 'Store & system configuration',
    subItems: [
      {
        name: 'General',
        href: '/admin/settings/general',
        description: 'Basic store information',
        icon: Store
      },
      {
        name: 'Appearance',
        href: '/admin/settings/appearance',
        description: 'Branding & theme settings',
        icon: Palette
      },
      {
        name: 'Commerce',
        href: '/admin/settings/commerce',
        description: 'Payment, shipping & tax',
        icon: CreditCard
      },
      {
        name: 'Advanced',
        href: '/admin/settings/advanced',
        description: 'SEO, email & compliance',
        icon: Settings
      }
    ]
  },
  {
    name: 'Support & Help',
    icon: HelpCircle,
    roles: ['super_admin', 'admin', 'editor', 'viewer'],
    badge: null,
    description: 'Get assistance',
    subItems: [
      { name: 'Knowledge Base', href: '/admin/support/knowledge-base', icon: HelpCircle },
      { name: 'Support Chat', href: '/admin/support/chat', icon: MessageCircle },
      { name: 'Submit Ticket', href: '/admin/support/ticket', icon: Ticket }
    ]
  }
]

export function Sidebar() {
  const location = useLocation()
  const { user, signOut } = useFirebaseAuthStore()
  const { currentStore, fetchCurrentStore } = useStoreManagement()
  const { products } = useProductsStore()
  const { orders } = useOrdersStore()
  const { customers } = useCustomersStore()
  const { storeName, loading: storeNameLoading } = useStoreName()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  // Load store when component mounts and user is available
  useEffect(() => {
    if (user && !currentStore) {
      fetchCurrentStore()
    }
  }, [user, currentStore, fetchCurrentStore])

  // Get dynamic navigation with real counts
  const navigation = getNavigation(
    products.length,
    orders.length,
    customers.length
  )

  const userRole = user?.role || 'viewer'

  const filteredNavigation = navigation.filter(item =>
    item.roles.includes(userRole)
  )

  // Fallback: if no items are filtered and user exists, show all items for super_admin
  const finalNavigation = filteredNavigation.length === 0 && user ?
    navigation.filter(item => item.roles.includes('super_admin')) :
    filteredNavigation

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev => {
      if (prev.includes(itemName)) {
        // If clicking on already expanded item, collapse it
        return prev.filter(name => name !== itemName)
      } else {
        // If expanding a new item, collapse all others and expand this one
        return [itemName]
      }
    })
  }

  const isItemActive = (href: string) => {
    return location.pathname === href
  }

  const isParentActive = (subItems?: any[]) => {
    return subItems ? subItems.some(subItem => location.pathname === subItem.href) : false
  }

  // Auto-expand the section containing the current active page
  useEffect(() => {
    const activeSection = navigation?.find(item =>
      item.roles?.includes(userRole) &&
      item.subItems && item.subItems.some(subItem => location.pathname === subItem.href)
    )
    if (activeSection) {
      setExpandedItems(prev => {
        if (!prev.includes(activeSection.name)) {
          return [activeSection.name]
        }
        return prev
      })
    }
  }, [location.pathname, userRole])

  const handleSignOut = async () => {
    await signOut()
  }

  const getBadgeColor = (color: string) => {
    switch (color) {
      case 'blue': return 'bg-accent-100 text-accent-800'
      case 'green': return 'bg-success-100 text-success-800'
      case 'purple': return 'bg-primary-100 text-primary-800'
      case 'orange': return 'bg-warning-100 text-warning-800'
      default: return 'bg-primary-100 text-primary-800'
    }
  }

  return (
    <div className={cn(
      "flex flex-col bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 shadow-sm transition-all duration-300",
      isCollapsed ? "w-16" : "w-64"
    )}>
      {/* Enhanced Logo */}
      <div className="relative">
        <div className="flex items-center gap-3 px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
          <div className="w-10 h-10 bg-accent-600 rounded-lg flex items-center justify-center shadow-sm">
            <ShoppingBag className="h-6 w-6 text-white" />
          </div>
          {!isCollapsed && (
            <div className="flex-1">
              <h1 className="text-xl font-bold text-primary-800 dark:text-white">
                W Admin Panel
              </h1>
              <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">Admin Panel v2.1</p>
            </div>
          )}
        </div>

        {/* Toggle Button - Always visible */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className={cn(
            "absolute top-1/2 -translate-y-1/2 p-1 h-8 w-8 bg-white border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 z-10",
            isCollapsed ? "-right-3" : "right-2"
          )}
        >
          <ChevronRight className={cn(
            "h-4 w-4 transition-transform duration-200",
            isCollapsed ? "rotate-0" : "rotate-180"
          )} />
        </Button>
      </div>

      {/* Enhanced User Info */}
      {!isCollapsed && (
        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-gray-800">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center ring-2 ring-white shadow-sm">
              <span className="text-sm font-medium text-white">
                {(storeName || user?.email)?.charAt(0).toUpperCase() || 'U'}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                {storeNameLoading ? 'Loading...' : (storeName || user?.email?.split('@')[0] || 'Unknown User')}
              </p>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="secondary" className="text-xs">
                  {user?.role?.replace('_', ' ').toUpperCase() || 'USER'}
                </Badge>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-xs text-green-600 dark:text-green-400 font-medium">Active</span>
                </div>
              </div>

            </div>
          </div>
        </div>
      )}

      {/* Enhanced Navigation with Sub-items */}
      <nav className="flex-1 px-3 py-1 space-y-0.5 overflow-y-auto scrollbar-hide hover:scrollbar-show">
        {/* Fallback for no navigation items */}
        {finalNavigation.length === 0 && (
          <div className="p-4 text-center text-gray-500">
            <p className="text-sm">No navigation items available</p>
            <Link
              to="/admin/dashboard"
              className="block mt-2 px-3 py-2 text-sm text-accent-600 hover:bg-accent-50 rounded-lg"
            >
              📊 Dashboard
            </Link>
          </div>
        )}

        {finalNavigation.map((item, index) => {
          const Icon = item.icon
          const isExpanded = expandedItems.includes(item.name)
          const hasActiveChild = isParentActive(item.subItems)
          const isDirectActive = item.href && isItemActive(item.href)
          const hasSubItems = item.subItems && item.subItems.length > 0
          const uniqueKey = `nav-${item.name.toLowerCase().replace(/\s+/g, '-')}-${index}-${userRole}`

          // If item has href (direct link), render as Link, otherwise as expandable button
          if (item.href && !hasSubItems) {
            return (
              <div key={uniqueKey} className="space-y-1">
                <Link
                  to={item.href}
                  className={cn(
                    "group flex items-center gap-3 px-3 py-1.5 text-sm font-medium transition-all duration-200 relative w-full rounded-lg",
                    isDirectActive
                      ? "text-accent-600 dark:text-accent-400 bg-accent-50 dark:bg-accent-900/20"
                      : "text-primary-700 dark:text-primary-300 hover:text-primary-900 dark:hover:text-primary-100 hover:bg-primary-50 dark:hover:bg-primary-700/50"
                  )}
                  title={isCollapsed ? item.name : undefined}
                >
                  <div className={cn(
                    "flex items-center justify-center w-8 h-8 transition-colors",
                    isDirectActive
                      ? "text-blue-600 dark:text-blue-400"
                      : "text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300"
                  )}>
                    <Icon className="h-5 w-5" />
                  </div>

                  {!isCollapsed && (
                    <>
                      <div className="flex-1 text-left">
                        <div className="flex items-center gap-2">
                          <span>{item.name}</span>
                          {item.badge && (
                            <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                              {item.badge}
                            </Badge>
                          )}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                          {item.description}
                        </div>
                      </div>
                    </>
                  )}
                </Link>
              </div>
            )
          }

          return (
            <div key={uniqueKey} className="space-y-1">
              {/* Main Navigation Item */}
              <button
                onClick={() => toggleExpanded(item.name)}
                className={cn(
                  "group flex items-center gap-3 px-3 py-1.5 text-sm font-medium transition-all duration-200 relative w-full rounded-lg",
                  isExpanded || hasActiveChild
                    ? "text-blue-600 dark:text-blue-400"
                    : "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700"
                )}
                title={isCollapsed ? item.name : undefined}
              >
                <div className={cn(
                  "flex items-center justify-center w-8 h-8 transition-colors",
                  hasActiveChild
                    ? "text-blue-600 dark:text-blue-400"
                    : "text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-200"
                )}>
                  <Icon className="h-4 w-4 flex-shrink-0" />
                </div>

                {!isCollapsed && (
                  <>
                    <div className="flex-1 min-w-0 text-left">
                      <div className="truncate font-medium">{item.name}</div>
                    </div>

                    <div className="flex items-center gap-2">
                      {item.badge && (
                        <Badge className={cn(
                          "text-xs px-2 py-0.5 font-medium",
                          getBadgeColor(item.badge.color)
                        )}>
                          {item.badge.text}
                        </Badge>
                      )}
                      <ChevronRight className={cn(
                        "h-4 w-4 transition-transform duration-200 text-gray-400 dark:text-gray-500",
                        isExpanded && "rotate-90 text-blue-600 dark:text-blue-400"
                      )} />
                    </div>
                  </>
                )}


              </button>

              {/* Sub-items */}
              {!isCollapsed && isExpanded && item.subItems && (
                <div className="ml-6 space-y-0.5 border-l-2 border-gray-100 dark:border-gray-700 pl-4 py-1">
                  {item.subItems
                    .filter(subItem => !subItem.roles || subItem.roles.includes(userRole))
                    .map((subItem, subIndex) => {
                    const SubIcon = subItem.icon || Settings // Fallback icon
                    const isActive = isItemActive(subItem.href)

                    return (
                      <Link
                        key={`${uniqueKey}-${subItem.name}-${subIndex}`}
                        to={subItem.href}
                        className={cn(
                          "flex items-center gap-3 px-3 py-1.5 text-sm transition-colors rounded-md",
                          isActive
                            ? "text-blue-600 dark:text-blue-400 font-medium"
                            : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100"
                        )}
                      >
                        <SubIcon className={cn(
                          "h-4 w-4 flex-shrink-0 transition-colors",
                          isActive ? "text-blue-600 dark:text-blue-400" : "text-gray-500 dark:text-gray-400"
                        )} />
                        <span className="truncate">{subItem.name}</span>
                      </Link>
                    )
                  })}
                </div>
              )}
            </div>
          )
        })}


      </nav>

      {/* Enhanced Footer */}
      <div className="px-3 py-2 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <Button
          variant="ghost"
          onClick={handleSignOut}
          className={cn(
            "w-full justify-start gap-3 text-gray-600 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors",
            isCollapsed && "justify-center px-2"
          )}
          title={isCollapsed ? "Sign Out" : undefined}
        >
          <LogOut className="h-4 w-4" />
          {!isCollapsed && <span>Sign Out</span>}
        </Button>

        {!isCollapsed && (
          <div className="mt-3 text-center">
            <p className="text-xs text-gray-400 dark:text-gray-500">
              © 2025 Womanza Admin
            </p>
            <p className="text-xs text-gray-400 dark:text-gray-500">
              Version 2.1.0
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
