import { CustomersService } from '../lib/customers-service'
import type { Customer } from '../lib/firebase'

// Sample customer data for testing
const sampleCustomers: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>[] = [
  {
    name: '<PERSON><PERSON>',
    email: 'ayes<PERSON>.<EMAIL>',
    phone: '+92 300 1234567',
    status: 'active',
    groups: ['vip', 'loyal'],
    totalSpent: 25000,
    orderCount: 12,
    loyaltyPoints: 1250,
    lastOrderDate: new Date(Date.now() - 86400000 * 3).toISOString(), // 3 days ago
    address: {
      shipping: {
        line1: '123 Main Street, Block A',
        city: 'Karachi',
        zip: '75500',
        country: 'Pakistan'
      },
      billing: {
        line1: '123 Main Street, Block A',
        city: 'Karachi',
        zip: '75500',
        country: 'Pakistan'
      }
    },
    dateOfBirth: '1990-03-15',
    gender: 'female',
    preferredLanguage: 'en',
    timezone: 'Asia/Karachi',
    marketingOptIn: true,
    tags: ['high-value', 'gold-lover', 'frequent-buyer'],
    notes: 'VIP customer with excellent payment history. Prefers gold jewelry and traditional designs.'
  },
  {
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+92 321 9876543',
    status: 'active',
    groups: ['loyal'],
    totalSpent: 15000,
    orderCount: 8,
    loyaltyPoints: 750,
    lastOrderDate: new Date(Date.now() - 86400000 * 7).toISOString(), // 1 week ago
    address: {
      shipping: {
        line1: '456 Garden Road',
        city: 'Lahore',
        zip: '54000',
        country: 'Pakistan'
      },
      billing: {
        line1: '456 Garden Road',
        city: 'Lahore',
        zip: '54000',
        country: 'Pakistan'
      }
    },
    dateOfBirth: '1985-07-22',
    gender: 'female',
    preferredLanguage: 'en',
    timezone: 'Asia/Karachi',
    marketingOptIn: true,
    tags: ['silver-lover', 'regular-buyer'],
    notes: 'Regular customer who prefers silver jewelry. Responds well to email campaigns.'
  },
  {
    name: 'Sarah Ahmed',
    email: '<EMAIL>',
    phone: '+92 333 5555555',
    status: 'active',
    groups: ['new'],
    totalSpent: 3500,
    orderCount: 2,
    loyaltyPoints: 175,
    lastOrderDate: new Date(Date.now() - 86400000 * 2).toISOString(), // 2 days ago
    address: {
      shipping: {
        line1: '789 University Road',
        city: 'Islamabad',
        zip: '44000',
        country: 'Pakistan'
      },
      billing: {
        line1: '789 University Road',
        city: 'Islamabad',
        zip: '44000',
        country: 'Pakistan'
      }
    },
    dateOfBirth: '1995-12-10',
    gender: 'female',
    preferredLanguage: 'en',
    timezone: 'Asia/Karachi',
    marketingOptIn: true,
    tags: ['new-customer', 'young-professional'],
    notes: 'New customer with high potential. Interested in modern designs.'
  },
  {
    name: 'Zainab Hassan',
    email: '<EMAIL>',
    phone: '+92 345 7777777',
    status: 'active',
    groups: ['regular'],
    totalSpent: 8500,
    orderCount: 5,
    loyaltyPoints: 425,
    lastOrderDate: new Date(Date.now() - 86400000 * 14).toISOString(), // 2 weeks ago
    address: {
      shipping: {
        line1: '321 Mall Road',
        city: 'Faisalabad',
        zip: '38000',
        country: 'Pakistan'
      },
      billing: {
        line1: '321 Mall Road',
        city: 'Faisalabad',
        zip: '38000',
        country: 'Pakistan'
      }
    },
    dateOfBirth: '1988-05-18',
    gender: 'female',
    preferredLanguage: 'en',
    timezone: 'Asia/Karachi',
    marketingOptIn: false,
    tags: ['occasional-buyer'],
    notes: 'Prefers traditional designs. Not interested in marketing emails.'
  },
  {
    name: 'Mariam Sheikh',
    email: '<EMAIL>',
    phone: '+92 300 8888888',
    status: 'inactive',
    groups: ['regular'],
    totalSpent: 12000,
    orderCount: 6,
    loyaltyPoints: 600,
    lastOrderDate: new Date(Date.now() - 86400000 * 120).toISOString(), // 4 months ago
    address: {
      shipping: {
        line1: '654 Clifton Block 5',
        city: 'Karachi',
        zip: '75600',
        country: 'Pakistan'
      },
      billing: {
        line1: '654 Clifton Block 5',
        city: 'Karachi',
        zip: '75600',
        country: 'Pakistan'
      }
    },
    dateOfBirth: '1982-09-25',
    gender: 'female',
    preferredLanguage: 'en',
    timezone: 'Asia/Karachi',
    marketingOptIn: true,
    tags: ['at-risk', 'previous-loyal'],
    notes: 'Previously loyal customer who has not ordered recently. May need re-engagement campaign.'
  },
  {
    name: 'Hina Malik',
    email: '<EMAIL>',
    phone: '+92 333 9999999',
    status: 'active',
    groups: ['vip'],
    totalSpent: 35000,
    orderCount: 18,
    loyaltyPoints: 1750,
    lastOrderDate: new Date(Date.now() - 86400000 * 1).toISOString(), // 1 day ago
    address: {
      shipping: {
        line1: '987 Defence Phase 6',
        city: 'Lahore',
        zip: '54792',
        country: 'Pakistan'
      },
      billing: {
        line1: '987 Defence Phase 6',
        city: 'Lahore',
        zip: '54792',
        country: 'Pakistan'
      }
    },
    dateOfBirth: '1987-11-08',
    gender: 'female',
    preferredLanguage: 'en',
    timezone: 'Asia/Karachi',
    marketingOptIn: true,
    tags: ['vip', 'high-value', 'frequent-buyer', 'diamond-lover'],
    notes: 'Top VIP customer with highest lifetime value. Prefers premium diamond jewelry.'
  }
]

/**
 * Add sample customers to the store
 */
export async function addSampleCustomers(storeId: string): Promise<void> {
  const customersService = new CustomersService(storeId)
  
  console.log('🔄 Adding sample customers to store:', storeId)
  
  try {
    for (const customerData of sampleCustomers) {
      try {
        const customerId = await customersService.createCustomer(customerData)
        console.log('✅ Created customer:', customerData.name, 'with ID:', customerId)
      } catch (error) {
        console.error('❌ Failed to create customer:', customerData.name, error)
      }
    }
    
    console.log('🎉 Sample customers added successfully!')
  } catch (error) {
    console.error('❌ Error adding sample customers:', error)
    throw error
  }
}

/**
 * Check if store has customers
 */
export async function hasCustomers(storeId: string): Promise<boolean> {
  const customersService = new CustomersService(storeId)
  
  try {
    const customers = await customersService.getCustomers({ limit: 1 })
    return customers.length > 0
  } catch (error) {
    console.error('❌ Error checking customers:', error)
    return false
  }
}

/**
 * Initialize customers for a store if none exist
 */
export async function initializeCustomersIfNeeded(storeId: string): Promise<void> {
  try {
    const hasExistingCustomers = await hasCustomers(storeId)
    
    if (!hasExistingCustomers) {
      console.log('📝 No customers found, adding sample data...')
      await addSampleCustomers(storeId)
    } else {
      console.log('✅ Store already has customers')
    }
  } catch (error) {
    console.error('❌ Error initializing customers:', error)
    throw error
  }
}
