import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { dataService } from '../lib/data-service'
import { useRealTimeProducts } from '../hooks/useRealTimeData'
import { DataTable, Column } from '../components/ui/data-table'
import { Button } from '../components/ui/button'
import { Badge } from '../components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Input } from '../components/ui/input'
import { cn } from '../lib/utils'
import { formatPrice } from '../lib/currency'
import { FallbackImage, ImagePlaceholder } from '../components/ui/fallback-image'
import {
  Package,
  Plus,
  Search,
  Filter,
  Download,
  Edit,
  Trash2,
  Eye,
  Image as ImageIcon,
  DollarSign,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Grid,
  List
} from 'lucide-react'

interface Product {
  id: string
  storeId: string
  categoryId: string | null
  name: string
  slug: string
  description: string | null
  shortDescription: string | null
  sku: string | null
  barcode: string | null
  price: number
  compareAtPrice: number | null
  costPrice: number | null
  inventoryQuantity: number
  lowStockThreshold: number
  trackInventory: boolean
  status: 'draft' | 'active' | 'archived'
  isFeatured: boolean
  requiresShipping: boolean
  weightGrams: number | null
  dimensions: any
  images: any[]
  variants: any[]
  options: any[]
  seoTitle: string | null
  seoDescription: string | null
  vendor: string | null
  productType: string | null
  publishedAt: string | null
  createdAt: string
  updatedAt: string
  categories?: {
    name: string
  }
}

export function ProductsPage() {
  const navigate = useNavigate()
  const { user, hasPermission } = useFirebaseAuthStore()
  const { products, loading, error, lastUpdated } = useRealTimeProducts()
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table')
  const [showAddModal, setShowAddModal] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    draft: 0,
    lowStock: 0,
    outOfStock: 0,
    totalValue: 0
  })

  const canEdit = hasPermission(['super_admin', 'admin', 'editor'])
  const canDelete = hasPermission(['super_admin', 'admin'])

  // Calculate stats from real-time products data
  useEffect(() => {
    if (products.length > 0) {
      const newStats = {
        total: products.length,
        active: products.filter(p => p.status === 'active').length,
        draft: products.filter(p => p.status === 'draft').length,
        lowStock: products.filter(p => p.trackInventory && p.inventoryQuantity <= p.lowStockThreshold).length,
        outOfStock: products.filter(p => p.trackInventory && p.inventoryQuantity === 0).length,
        totalValue: products.reduce((sum, p) => sum + (p.price * p.inventoryQuantity), 0)
      }
      setStats(newStats)
    }
  }, [products])

  const handleDeleteProduct = async (product: Product) => {
    if (!confirm(`Are you sure you want to delete "${product.name}"?`)) return

    try {
      await dataService.deleteProduct(product.id)
      // Real-time data will automatically update the products list
    } catch (error) {
      console.error('Error deleting product:', error)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Active</Badge>
      case 'draft':
        return <Badge variant="secondary"><Clock className="h-3 w-3 mr-1" />Draft</Badge>
      case 'archived':
        return <Badge variant="outline"><XCircle className="h-3 w-3 mr-1" />Archived</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getInventoryBadge = (quantity: number) => {
    if (quantity === 0) {
      return <Badge className="bg-red-100 text-red-800"><AlertTriangle className="h-3 w-3 mr-1" />Out of Stock</Badge>
    } else if (quantity <= 10) {
      return <Badge className="bg-yellow-100 text-yellow-800"><AlertTriangle className="h-3 w-3 mr-1" />Low Stock</Badge>
    }
    return <Badge className="bg-green-100 text-green-800">{quantity} in stock</Badge>
  }

  const columns: Column<Product>[] = [
    {
      key: 'name',
      title: 'Product',
      sortable: true,
      filterable: true,
      render: (_, product) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg overflow-hidden flex-shrink-0">
            {product.images && product.images.length > 0 ? (
              <img
                src={typeof product.images[0] === 'string' ? product.images[0] : product.images[0]?.url || ''}
                alt={product.name}
                className="w-10 h-10 object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  const parent = target.parentElement;
                  if (parent && !parent.querySelector('.image-placeholder')) {
                    const placeholder = document.createElement('div');
                    placeholder.className = 'image-placeholder w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-400';
                    placeholder.textContent = '📷';
                    parent.appendChild(placeholder);
                  }
                }}
              />
            ) : (
              <div className="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                <ImageIcon className="h-5 w-5 text-gray-400" />
              </div>
            )}
          </div>
          <div>
            <p className="font-medium text-gray-900 dark:text-gray-100">{product.name}</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">{product.sku || 'No SKU'}</p>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      filterable: true,
      render: (status) => getStatusBadge(status)
    },
    {
      key: 'inventoryQuantity',
      title: 'Inventory',
      sortable: true,
      filterable: false,
      render: (quantity) => getInventoryBadge(quantity)
    },
    {
      key: 'price',
      title: 'Price',
      sortable: true,
      filterable: false,
      render: (price) => (
        <div className="font-medium">
          {formatPrice(price || 0)}
        </div>
      )
    },
    {
      key: 'productType',
      title: 'Type',
      filterable: true,
      render: (type) => type || 'General'
    },
    {
      key: 'vendor',
      title: 'Vendor',
      filterable: true,
      render: (vendor) => vendor || '-'
    }
  ]

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">Products</h1>
          <p className="text-gray-500 dark:text-gray-400">Manage your product catalog and inventory</p>
        </div>
        <div className="flex items-center gap-3">
          {/* Add Product Button */}
          <Button
            onClick={() => navigate('/admin/add-product')}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Product
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-900 dark:text-blue-100">Total Products</CardTitle>
            <div className="p-2 bg-blue-100 dark:bg-blue-800/50 rounded-lg">
              <Package className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">{stats.total}</div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-900 dark:text-green-100">Active</CardTitle>
            <div className="p-2 bg-green-100 dark:bg-green-800/50 rounded-lg">
              <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900 dark:text-green-100">{stats.active}</div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 border-yellow-200 dark:border-yellow-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-yellow-900 dark:text-yellow-100">Draft</CardTitle>
            <div className="p-2 bg-yellow-100 dark:bg-yellow-800/50 rounded-lg">
              <Clock className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">{stats.draft}</div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 border-orange-200 dark:border-orange-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-900 dark:text-orange-100">Low Stock</CardTitle>
            <div className="p-2 bg-orange-100 dark:bg-orange-800/50 rounded-lg">
              <AlertTriangle className="h-4 w-4 text-orange-600 dark:text-orange-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-900 dark:text-orange-100">{stats.lowStock}</div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 border-red-200 dark:border-red-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-900 dark:text-red-100">Out of Stock</CardTitle>
            <div className="p-2 bg-red-100 dark:bg-red-800/50 rounded-lg">
              <XCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-900 dark:text-red-100">{stats.outOfStock}</div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border-purple-200 dark:border-purple-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-900 dark:text-purple-100">Total Value</CardTitle>
            <div className="p-2 bg-purple-100 dark:bg-purple-800/50 rounded-lg">
              <DollarSign className="h-4 w-4 text-purple-600 dark:text-purple-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">{formatPrice(stats.totalValue)}</div>
          </CardContent>
        </Card>
      </div>

      {/* Products Table/Grid */}
      {viewMode === 'table' ? (
        <DataTable
          data={products}
          columns={columns}
          loading={loading}
          searchable={true}
          filterable={true}
          exportable={true}
          viewModeToggle={{
            currentMode: viewMode,
            onModeChange: setViewMode
          }}
          actions={{
            view: (product) => {
              // Navigate to product detail view
              navigate(`/admin/products/${product.id}`)
            },
            edit: canEdit ? (product) => {
              // Navigate to edit product page
              navigate(`/admin/products/${product.id}/edit`)
            } : undefined,
            delete: canDelete ? handleDeleteProduct : undefined
          }}
          actionButtonProps={{
            size: 'sm',
            className: 'px-2 py-1 h-8'
          }}
          emptyState={{
            title: 'No products found',
            description: 'Get started by adding your first product to the catalog.',
            icon: <Package className="h-6 w-6 text-gray-400" />
          }}
        />
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {products.map((product) => (
            <Card
              key={product.id}
              className="overflow-hidden border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-lg transition-all duration-200 bg-white dark:bg-gray-800"
            >
              <div className="aspect-square bg-gray-100 dark:bg-gray-700 relative">
                {product.images && product.images.length > 0 ? (
                  <img
                    src={typeof product.images[0] === 'string' ? product.images[0] : product.images[0]?.url || ''}
                    alt={product.name}
                    className="object-cover w-full h-full"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const parent = target.parentElement;
                      if (parent && !parent.querySelector('.image-placeholder')) {
                        const placeholder = document.createElement('div');
                        placeholder.className = 'image-placeholder flex items-center justify-center h-full text-gray-400 dark:text-gray-500';
                        placeholder.textContent = '📷 Image not found';
                        parent.appendChild(placeholder);
                      }
                    }}
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-400 dark:text-gray-500">
                    <ImageIcon className="h-12 w-12" />
                  </div>
                )}
                <div className="absolute top-3 right-3">
                  {getStatusBadge(product.status)}
                </div>
              </div>
              <CardHeader className="pb-3">
                <CardTitle className="line-clamp-1 text-base text-gray-900 dark:text-gray-100">{product.name}</CardTitle>
                <CardDescription className="line-clamp-2 text-sm text-gray-600 dark:text-gray-400">
                  {product.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Price</span>
                    <span className="font-semibold text-gray-900 dark:text-gray-100">{formatPrice(product.price)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Stock</span>
                    {getInventoryBadge(product.inventoryQuantity)}
                  </div>
                  {product.sku && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400">SKU</span>
                      <span className="text-sm font-mono text-gray-700 dark:text-gray-300">{product.sku}</span>
                    </div>
                  )}
                </div>

                <div className="flex justify-end gap-1 mt-4 pt-3 border-t border-gray-100 dark:border-gray-700">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedProduct(product)}
                    className="h-8 w-8 p-0 hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                    title="View product"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  {canEdit && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400"
                      title="Edit product"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  )}
                  {canDelete && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteProduct(product)}
                      className="h-8 w-8 p-0 hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400"
                      title="Delete product"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
