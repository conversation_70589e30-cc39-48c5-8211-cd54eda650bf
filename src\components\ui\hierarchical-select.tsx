import React, { useState, useRef, useEffect } from 'react'
import { ChevronDown, ChevronRight } from 'lucide-react'
import { cn } from '../../lib/utils'

interface HierarchicalOption {
  value: string
  label: string
  parentId?: string
  children?: HierarchicalOption[]
  level?: number
}

interface HierarchicalSelectProps {
  options: HierarchicalOption[]
  value?: string
  onValueChange: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function HierarchicalSelect({
  options,
  value,
  onValueChange,
  placeholder = "Select an option",
  className,
  disabled = false
}: HierarchicalSelectProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set())
  const selectRef = useRef<HTMLDivElement>(null)

  // Build hierarchical structure
  const buildHierarchy = (items: HierarchicalOption[]): HierarchicalOption[] => {
    const itemMap = new Map<string, HierarchicalOption>()
    const rootItems: HierarchicalOption[] = []

    // Create map of all items
    items.forEach(item => {
      itemMap.set(item.value, { ...item, children: [], level: 0 })
    })

    // Build hierarchy
    items.forEach(item => {
      const mappedItem = itemMap.get(item.value)!
      if (item.parentId && itemMap.has(item.parentId)) {
        const parent = itemMap.get(item.parentId)!
        parent.children!.push(mappedItem)
        mappedItem.level = (parent.level || 0) + 1
      } else {
        rootItems.push(mappedItem)
      }
    })

    return rootItems
  }

  const hierarchicalOptions = buildHierarchy(options)

  // Find selected option label
  const selectedOption = options?.find(opt => opt.value === value)
  const selectedLabel = selectedOption ? selectedOption.label : placeholder

  // Render option with proper indentation
  const renderOption = (option: HierarchicalOption): React.ReactNode[] => {
    const hasChildren = option.children && option.children.length > 0
    const isExpanded = expandedNodes.has(option.value)
    const isSelected = value === option.value

    const elements: React.ReactNode[] = []

    // Main option
    elements.push(
      <div
        key={option.value}
        className={cn(
          "flex items-center px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",
          isSelected && "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300"
        )}
        style={{ paddingLeft: `${12 + (option.level || 0) * 20}px` }}
        onClick={() => {
          onValueChange(option.value)
          setIsOpen(false)
        }}
      >
        {hasChildren && (
          <button
            className="mr-2 p-0.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
            onClick={(e) => {
              e.stopPropagation()
              const newExpanded = new Set(expandedNodes)
              if (isExpanded) {
                newExpanded.delete(option.value)
              } else {
                newExpanded.add(option.value)
              }
              setExpandedNodes(newExpanded)
            }}
          >
            {isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
          </button>
        )}
        {!hasChildren && <div className="w-5 mr-2" />}
        <span className="flex-1 text-sm">{option.label}</span>
      </div>
    )

    // Children (if expanded)
    if (hasChildren && isExpanded) {
      option.children!.forEach(child => {
        elements.push(...renderOption(child))
      })
    }

    return elements
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Auto-expand path to selected item
  useEffect(() => {
    if (value) {
      const findPath = (items: HierarchicalOption[], targetValue: string, path: string[] = []): string[] | null => {
        for (const item of items) {
          const currentPath = [...path, item.value]
          if (item.value === targetValue) {
            return currentPath
          }
          if (item.children) {
            const childPath = findPath(item.children, targetValue, currentPath)
            if (childPath) return childPath
          }
        }
        return null
      }

      const path = findPath(hierarchicalOptions, value)
      if (path) {
        setExpandedNodes(new Set(path.slice(0, -1))) // Expand all parents
      }
    }
  }, [value, hierarchicalOptions])

  return (
    <div ref={selectRef} className={cn("relative", className)}>
      <button
        type="button"
        className={cn(
          "flex h-10 w-full items-center justify-between rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:ring-offset-gray-950",
          isOpen && "ring-2 ring-blue-500"
        )}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
      >
        <span className={cn(
          "truncate",
          !selectedOption && "text-gray-500 dark:text-gray-400"
        )}>
          {selectedLabel}
        </span>
        <ChevronDown className={cn(
          "h-4 w-4 transition-transform",
          isOpen && "rotate-180"
        )} />
      </button>

      {isOpen && (
        <div className="absolute z-50 mt-1 w-full rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-lg">
          <div className="max-h-60 overflow-auto py-1">
            {hierarchicalOptions.length === 0 ? (
              <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                No options available
              </div>
            ) : (
              hierarchicalOptions.flatMap(option => renderOption(option))
            )}
          </div>
        </div>
      )}
    </div>
  )
}

// Helper function to build category breadcrumb
export function getCategoryBreadcrumb(
  categories: HierarchicalOption[],
  categoryId: string
): string {
  const findCategory = (items: HierarchicalOption[], id: string): HierarchicalOption | null => {
    for (const item of items) {
      if (item.value === id) return item
      if (item.children) {
        const found = findCategory(item.children, id)
        if (found) return found
      }
    }
    return null
  }

  const buildPath = (items: HierarchicalOption[], id: string, path: string[] = []): string[] => {
    for (const item of items) {
      if (item.value === id) {
        return [...path, item.label]
      }
      if (item.children) {
        const childPath = buildPath(item.children, id, [...path, item.label])
        if (childPath.length > path.length) return childPath
      }
    }
    return path
  }

  const path = buildPath(categories, categoryId)
  return path.join(' > ')
}
