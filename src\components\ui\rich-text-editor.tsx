import React from 'react'
import { Textarea } from './textarea'
import { cn } from '../../lib/utils'

interface RichTextEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function RichTextEditor({
  value,
  onChange,
  placeholder = "Enter description...",
  className,
  disabled = false
}: RichTextEditorProps) {

  // Temporarily use a simple textarea instead of complex rich text editor
  return (
    <div className={cn("space-y-2", className)}>
      <Textarea
        value={value.replace(/<[^>]*>/g, '')} // Strip HTML for now
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        rows={4}
        className="min-h-[120px]"
      />
      <div className="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400">
        <span>Rich text formatting temporarily disabled</span>
        <span>{value.replace(/<[^>]*>/g, '').length} characters</span>
      </div>
    </div>
  )
}

// Helper function to strip HTML tags for plain text
export function stripHtml(html: string): string {
  const div = document.createElement('div')
  div.innerHTML = html
  return div.textContent || div.innerText || ''
}

// Helper function to convert plain text to HTML
export function textToHtml(text: string): string {
  return text.replace(/\n/g, '<br>')
}
