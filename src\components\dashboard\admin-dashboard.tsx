import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { SalesChart, OrdersChart, ProductSalesChart } from '../ui/chart'
import { ChartWrapper } from '../ui/chart-error-boundary'
import { useDashboardData } from '../../hooks/useDashboardData'
import { useStableChartData } from '../../hooks/useStableData'
import { useStoreManagement } from '../../store/store-management'
import { cn } from '../../lib/utils'
import {
  BarChart3,
  DollarSign,
  Package,
  ShoppingCart,
  Users,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Eye,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Activity,
  CreditCard,
  Download,
  Bell,
  Star,
  Zap,
  Target
} from 'lucide-react'
import { RealTimeIndicator } from '../ui/real-time-indicator'
import { formatCurrency, formatNumber, formatPercentage, getStatusColor, getStatusLabel, formatDateTime } from '../../lib/utils'
import { Link } from 'react-router-dom'

export function AdminDashboard() {
  const { stats, recentOrders, salesData, topProducts, loading, error, refetch } = useDashboardData()
  const { currentStore } = useStoreManagement()
  const stableSalesData = useStableChartData(salesData)

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-500 mb-2">Error loading dashboard data</p>
          <Button onClick={refetch} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            Admin Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage products, orders, marketing, and customers
          </p>
        </div>
        <div className="flex items-center gap-3">
          <RealTimeIndicator />
          <Button onClick={refetch} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>



      {/* Stats Cards - Same as Super Admin */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats?.totalRevenue || 0)}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.revenueChange ? (
                <span className={cn(
                  "flex items-center",
                  stats.revenueChange >= 0 ? "text-green-600" : "text-red-600"
                )}>
                  {stats.revenueChange >= 0 ? <ArrowUpRight className="h-3 w-3 mr-1" /> : <ArrowDownRight className="h-3 w-3 mr-1" />}
                  {formatPercentage(Math.abs(stats.revenueChange))} from last month
                </span>
              ) : (
                "No change data available"
              )}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats?.totalOrders || 0)}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.ordersChange ? (
                <span className={cn(
                  "flex items-center",
                  stats.ordersChange >= 0 ? "text-green-600" : "text-red-600"
                )}>
                  {stats.ordersChange >= 0 ? <ArrowUpRight className="h-3 w-3 mr-1" /> : <ArrowDownRight className="h-3 w-3 mr-1" />}
                  {formatPercentage(Math.abs(stats.ordersChange))} from last month
                </span>
              ) : (
                "No change data available"
              )}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats?.totalProducts || 0)}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.lowStockCount ? (
                <span className="text-orange-600">
                  {stats.lowStockCount} low stock items
                </span>
              ) : (
                "All items in stock"
              )}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats?.totalCustomers || 0)}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.customersChange ? (
                <span className={cn(
                  "flex items-center",
                  stats.customersChange >= 0 ? "text-green-600" : "text-red-600"
                )}>
                  {stats.customersChange >= 0 ? <ArrowUpRight className="h-3 w-3 mr-1" /> : <ArrowDownRight className="h-3 w-3 mr-1" />}
                  {formatPercentage(Math.abs(stats.customersChange))} from last month
                </span>
              ) : (
                "No change data available"
              )}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section - Same as Super Admin */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Sales Overview
            </CardTitle>
            <CardDescription>Revenue trends over time</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartWrapper>
              <SalesChart data={stableSalesData} />
            </ChartWrapper>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Order Trends
            </CardTitle>
            <CardDescription>Order volume over time</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartWrapper>
              <OrdersChart data={stableSalesData} />
            </ChartWrapper>
          </CardContent>
        </Card>
      </div>

      {/* Recent Orders and Top Products - Same as Super Admin */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              Recent Orders
            </CardTitle>
            <CardDescription>Latest customer orders</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentOrders?.length > 0 ? (
                recentOrders.slice(0, 5).map((order) => (
                  <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">#{order.orderNumber}</p>
                      <p className="text-sm text-gray-500">{order.customerEmail}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(order.total)}</p>
                      <Badge variant="secondary" className={getStatusColor(order.status)}>
                        {getStatusLabel(order.status)}
                      </Badge>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">No recent orders</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              Top Products
            </CardTitle>
            <CardDescription>Best performing products</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topProducts?.length > 0 ? (
                topProducts.slice(0, 5).map((product) => (
                  <div key={product.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">{product.name}</p>
                      <p className="text-sm text-gray-500">SKU: {product.sku}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(product.price)}</p>
                      <p className="text-sm text-gray-500">{product.salesCount || 0} sold</p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">No product data available</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
