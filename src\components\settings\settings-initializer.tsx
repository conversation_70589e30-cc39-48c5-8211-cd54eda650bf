import React, { useState } from 'react'
import { Button } from '../ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Alert, AlertDescription } from '../ui/alert'
import { 
  Settings, 
  Database, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Play,
  RefreshCw
} from 'lucide-react'
import { 
  initializeStoreSettings, 
  initializeMultipleStores, 
  checkStoreSettings 
} from '../../utils/initialize-settings'
import { useFirebaseAuthStore } from '../../store/firebase-auth'
import { useStoreManagement } from '../../store/store-management'

interface SettingsInitializerProps {
  onComplete?: () => void
}

export function SettingsInitializer({ onComplete }: SettingsInitializerProps) {
  const { user } = useFirebaseAuthStore()
  const { currentStore } = useStoreManagement()
  const [loading, setLoading] = useState(false)
  const [checking, setChecking] = useState(false)
  const [settingsExist, setSettingsExist] = useState<boolean | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Get store ID
  const storeId = currentStore?.id || user?.primaryStoreId || user?.storeId || 'womanza-jewelry-store'

  // Sample store IDs for bulk initialization
  const sampleStores = [
    'womanza-jewelry-store',
    'sample-store-1',
    'sample-store-2'
  ]

  const handleCheckSettings = async () => {
    setChecking(true)
    setError(null)
    
    try {
      const exists = await checkStoreSettings(storeId)
      setSettingsExist(exists)
    } catch (error) {
      console.error('Error checking settings:', error)
      setError('Failed to check settings status')
    } finally {
      setChecking(false)
    }
  }

  const handleInitializeSingle = async () => {
    setLoading(true)
    setError(null)
    
    try {
      await initializeStoreSettings(storeId)
      setSettingsExist(true)
      onComplete?.()
    } catch (error) {
      console.error('Error initializing settings:', error)
      setError('Failed to initialize settings')
    } finally {
      setLoading(false)
    }
  }

  const handleInitializeMultiple = async () => {
    setLoading(true)
    setError(null)
    
    try {
      await initializeMultipleStores(sampleStores)
      setSettingsExist(true)
      onComplete?.()
    } catch (error) {
      console.error('Error initializing multiple stores:', error)
      setError('Failed to initialize multiple stores')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Settings Database Initialization
          </CardTitle>
          <CardDescription>
            Initialize the Firestore collections and default settings for your store(s)
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Store Status */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h3 className="font-medium">Current Store: {storeId}</h3>
              <p className="text-sm text-gray-500">
                {settingsExist === null ? 'Status unknown' : 
                 settingsExist ? 'Settings initialized' : 'Settings not found'}
              </p>
            </div>
            <div className="flex items-center gap-2">
              {settingsExist === true && (
                <Badge variant="default" className="bg-green-100 text-green-800">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Ready
                </Badge>
              )}
              {settingsExist === false && (
                <Badge variant="destructive">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Not Initialized
                </Badge>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={handleCheckSettings}
                disabled={checking}
              >
                {checking ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
                Check
              </Button>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Initialization Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Initialize Current Store</CardTitle>
                <CardDescription>
                  Set up default settings for the current store only
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  onClick={handleInitializeSingle}
                  disabled={loading || settingsExist === true}
                  className="w-full"
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Play className="h-4 w-4 mr-2" />
                  )}
                  Initialize {storeId}
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Initialize Sample Stores</CardTitle>
                <CardDescription>
                  Set up default settings for multiple sample stores
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  onClick={handleInitializeMultiple}
                  disabled={loading}
                  variant="outline"
                  className="w-full"
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Database className="h-4 w-4 mr-2" />
                  )}
                  Initialize All Samples
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Settings Categories Info */}
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <h3 className="font-medium mb-3 flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Settings Categories to be Created
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
              {[
                'General', 'Branding', 'SEO & Analytics',
                'Auth & Access', 'Email Config', 'Payment',
                'Shipping', 'Tax', 'Compliance',
                'Behavior', 'Panel Preferences'
              ].map((category) => (
                <div key={category} className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  {category}
                </div>
              ))}
            </div>
          </div>

          {/* Instructions */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Note:</strong> This will create Firestore documents with default settings. 
              Existing settings will be preserved and only missing fields will be added.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  )
}
