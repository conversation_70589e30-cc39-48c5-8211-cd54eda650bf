// Currency configuration and utilities

export interface CurrencyConfig {
  code: string
  symbol: string
  name: string
  position: 'before' | 'after'
  decimals: number
}

export const CURRENCIES: Record<string, CurrencyConfig> = {
  PKR: {
    code: 'PKR',
    symbol: 'Rs.',
    name: 'Pakistani Rupee',
    position: 'before',
    decimals: 0
  },
  USD: {
    code: 'USD',
    symbol: '$',
    name: 'US Dollar',
    position: 'before',
    decimals: 2
  },
  EUR: {
    code: 'EUR',
    symbol: '€',
    name: 'Euro',
    position: 'before',
    decimals: 2
  },
  GBP: {
    code: 'GBP',
    symbol: '£',
    name: 'British Pound',
    position: 'before',
    decimals: 2
  },
  CAD: {
    code: 'CAD',
    symbol: 'C$',
    name: 'Canadian Dollar',
    position: 'before',
    decimals: 2
  },
  AUD: {
    code: 'AUD',
    symbol: 'A$',
    name: 'Australian Dollar',
    position: 'before',
    decimals: 2
  },
  INR: {
    code: 'INR',
    symbol: '₹',
    name: 'Indian Rupee',
    position: 'before',
    decimals: 0
  },
  JPY: {
    code: 'JPY',
    symbol: '¥',
    name: 'Japanese Yen',
    position: 'before',
    decimals: 0
  },
  CNY: {
    code: 'CNY',
    symbol: '¥',
    name: 'Chinese Yuan',
    position: 'before',
    decimals: 2
  },
  SAR: {
    code: 'SAR',
    symbol: 'ر.س',
    name: 'Saudi Riyal',
    position: 'after',
    decimals: 2
  },
  AED: {
    code: 'AED',
    symbol: 'د.إ',
    name: 'UAE Dirham',
    position: 'after',
    decimals: 2
  }
}

// Get current currency from localStorage or default to PKR
export function getCurrentCurrency(): string {
  try {
    const storeId = import.meta.env.VITE_STORE_ID || '4db760eb-679e-4783-a563-717c0b12dec2'
    const settings = localStorage.getItem(`store-settings-${storeId}`)
    if (settings) {
      const parsed = JSON.parse(settings)
      return parsed.currency || 'PKR'
    }
  } catch (error) {
    console.warn('Could not load currency from settings:', error)
  }
  return 'PKR'
}

// Format price with currency
export function formatPrice(amount: number, currencyCode?: string): string {
  const currency = currencyCode || getCurrentCurrency()
  const config = CURRENCIES[currency] || CURRENCIES.PKR
  
  // Format number with appropriate decimals
  const formattedAmount = config.decimals === 0 
    ? Math.round(amount).toLocaleString()
    : amount.toFixed(config.decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  
  // Position symbol before or after amount
  if (config.position === 'before') {
    return `${config.symbol}${formattedAmount}`
  } else {
    return `${formattedAmount} ${config.symbol}`
  }
}

// Format price for input fields (without currency symbol)
export function formatPriceInput(amount: number, currencyCode?: string): string {
  const currency = currencyCode || getCurrentCurrency()
  const config = CURRENCIES[currency] || CURRENCIES.PKR
  
  return config.decimals === 0 
    ? Math.round(amount).toString()
    : amount.toFixed(config.decimals)
}

// Parse price from string input
export function parsePrice(priceString: string): number {
  // Remove all non-numeric characters except decimal point
  const cleaned = priceString.replace(/[^\d.-]/g, '')
  const parsed = parseFloat(cleaned)
  return isNaN(parsed) ? 0 : parsed
}

// Get currency symbol
export function getCurrencySymbol(currencyCode?: string): string {
  const currency = currencyCode || getCurrentCurrency()
  const config = CURRENCIES[currency] || CURRENCIES.PKR
  return config.symbol
}

// Get currency configuration
export function getCurrencyConfig(currencyCode?: string): CurrencyConfig {
  const currency = currencyCode || getCurrentCurrency()
  return CURRENCIES[currency] || CURRENCIES.PKR
}

// Format price range
export function formatPriceRange(minPrice: number, maxPrice: number, currencyCode?: string): string {
  if (minPrice === maxPrice) {
    return formatPrice(minPrice, currencyCode)
  }
  return `${formatPrice(minPrice, currencyCode)} - ${formatPrice(maxPrice, currencyCode)}`
}

// Convert price between currencies (mock conversion for development)
export function convertPrice(amount: number, fromCurrency: string, toCurrency: string): number {
  // Mock conversion rates (in production, use real exchange rates)
  const mockRates: Record<string, number> = {
    PKR: 1,
    USD: 0.0036, // 1 PKR = 0.0036 USD
    EUR: 0.0033,
    GBP: 0.0028,
    INR: 0.30,
    SAR: 0.013,
    AED: 0.013
  }
  
  if (fromCurrency === toCurrency) return amount
  
  const fromRate = mockRates[fromCurrency] || 1
  const toRate = mockRates[toCurrency] || 1
  
  // Convert to PKR first, then to target currency
  const pkrAmount = amount / fromRate
  return pkrAmount * toRate
}

// Format discount percentage
export function formatDiscount(originalPrice: number, discountedPrice: number): string {
  if (originalPrice <= discountedPrice) return ''
  
  const discountPercent = Math.round(((originalPrice - discountedPrice) / originalPrice) * 100)
  return `${discountPercent}% OFF`
}

// Calculate tax amount
export function calculateTax(amount: number, taxRate: number): number {
  return amount * (taxRate / 100)
}

// Format price with tax
export function formatPriceWithTax(amount: number, taxRate: number, currencyCode?: string): string {
  const taxAmount = calculateTax(amount, taxRate)
  const totalAmount = amount + taxAmount
  return formatPrice(totalAmount, currencyCode)
}

// Initialize currency settings (for compatibility with the new system)
export function initializeCurrencySettings(): void {
  // This function is called on app startup to ensure currency settings are loaded
  // The current implementation already loads from localStorage in getCurrentCurrency()
  // This is a placeholder for future enhancements like loading from API
  console.log('Currency settings initialized with current currency:', getCurrentCurrency())
}

// Save currency settings (for compatibility with the new system)
export function saveCurrencySettings(settings: any): void {
  // This function would save currency settings to localStorage or API
  // For now, it's a placeholder since currency is managed through store settings
  console.log('Currency settings saved:', settings)
}

// Currency presets for the settings page
export const CURRENCY_PRESETS = {
  USD: {
    currency: 'USD',
    symbol: '$',
    position: 'before' as const,
    decimals: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.'
  },
  EUR: {
    currency: 'EUR',
    symbol: '€',
    position: 'after' as const,
    decimals: 2,
    thousandsSeparator: '.',
    decimalSeparator: ','
  },
  GBP: {
    currency: 'GBP',
    symbol: '£',
    position: 'before' as const,
    decimals: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.'
  },
  PKR: {
    currency: 'PKR',
    symbol: '₨',
    position: 'before' as const,
    decimals: 0,
    thousandsSeparator: ',',
    decimalSeparator: '.'
  },
  INR: {
    currency: 'INR',
    symbol: '₹',
    position: 'before' as const,
    decimals: 0,
    thousandsSeparator: ',',
    decimalSeparator: '.'
  }
}
