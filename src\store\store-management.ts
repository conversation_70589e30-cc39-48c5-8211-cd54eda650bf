import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  onSnapshot
} from 'firebase/firestore'
import { db } from '../lib/firebase'
import type { Store, PendingStoreRequest } from '../lib/firebase'

/**
 * Individual Store Management
 * Each store instance operates independently with its own data
 */

interface StoreManagementState {
  // Current store (determined by environment or user's store)
  currentStore: Store | null
  currentStoreId: string | null

  // App admin only - for managing all stores
  allStores: Store[]
  pendingRequests: PendingStoreRequest[]

  // Loading states
  loading: boolean
  error: string | null

  // Actions
  fetchCurrentStore: () => Promise<void>
  fetchAllStores: () => Promise<void> // App admin only
  fetchPendingRequests: () => Promise<void> // App admin only
  createStoreRequest: (request: Omit<PendingStoreRequest, 'id' | 'createdAt' | 'status'>) => Promise<string>
  approveStoreRequest: (requestId: string) => Promise<void> // App admin only
  rejectStoreRequest: (requestId: string, reason: string) => Promise<void> // App admin only
  updateCurrentStore: (updates: Partial<Store>) => Promise<void>
  switchStore: (storeId: string) => Promise<void>
  setCurrentStore: (store: Store) => void // Set current store directly
  syncStoreNameFromSettings: (storeId: string, storeName: string, storeSlug?: string) => Promise<void>
  clearError: () => void
}

export const useStoreManagement = create<StoreManagementState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentStore: null,
      currentStoreId: null,
      allStores: [],
      pendingRequests: [],
      loading: false,
      error: null,

      clearError: () => set({ error: null }),

      // Set current store (used by authentication)
      setCurrentStore: (store: Store) => {
        set({
          currentStore: store,
          currentStoreId: store.id,
          error: null
        })
        // Save to localStorage for services that need it
        localStorage.setItem('currentStoreId', store.id)
        console.log('🏪 Store management - Current store set:', store.id)
      },

      // Fetch current store (individual store mode)
      fetchCurrentStore: async () => {
        const { loading, currentStore } = get()
        if (loading || currentStore) return // Prevent multiple calls

        try {
          set({ loading: true, error: null })

          // Get store ID from environment variable or use the actual store ID
          const storeId = import.meta.env.VITE_STORE_ID || 'womanza-jewelry-store'

          const storeDoc = await getDoc(doc(db, 'stores', storeId))

          if (storeDoc.exists()) {
            const store = { id: storeDoc.id, ...storeDoc.data() } as Store
            set({
              currentStore: store,
              currentStoreId: storeId
            })
            // Save to localStorage for services that need it
            localStorage.setItem('currentStoreId', storeId)
          } else {
            set({ error: 'Store not found. Please contact administrator.' })
          }
        } catch (error) {
          console.error('Error fetching current store:', error)
          set({ error: 'Failed to fetch store details' })
        } finally {
          set({ loading: false })
        }
      },

      // Fetch all stores (App admin only)
      fetchAllStores: async () => {
        try {
          set({ loading: true, error: null })

          const storesSnapshot = await getDocs(
            query(collection(db, 'stores'), orderBy('createdAt', 'desc'))
          )

          const stores = storesSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as Store[]

          set({ allStores: stores })
        } catch (error) {
          console.error('Error fetching all stores:', error)
          set({ error: 'Failed to fetch stores' })
        } finally {
          set({ loading: false })
        }
      },

      fetchPendingRequests: async () => {
        set({ loading: true, error: null })
        try {
          const q = query(
            collection(db, 'pending_store_requests'),
            where('status', '==', 'pending'),
            orderBy('createdAt', 'desc')
          )
          
          const querySnapshot = await getDocs(q)
          const requests: PendingStoreRequest[] = []
          
          querySnapshot.forEach((doc) => {
            requests.push({
              id: doc.id,
              ...doc.data()
            } as PendingStoreRequest)
          })
          
          set({ pendingRequests: requests, loading: false })
        } catch (error: any) {
          console.error('Error fetching pending requests:', error)
          set({ error: error.message, loading: false })
        }
      },

      createStoreRequest: async (requestData) => {
        set({ error: null })
        try {
          const docRef = await addDoc(collection(db, 'pending_store_requests'), {
            ...requestData,
            status: 'pending',
            createdAt: serverTimestamp()
          })
          
          return docRef.id
        } catch (error: any) {
          console.error('Error creating store request:', error)
          set({ error: error.message })
          throw error
        }
      },

      approveStoreRequest: async (requestId: string) => {
        set({ error: null })
        try {
          // Get the request
          const requestDoc = await getDoc(doc(db, 'pending_store_requests', requestId))
          if (!requestDoc.exists()) {
            throw new Error('Request not found')
          }

          const requestData = requestDoc.data() as PendingStoreRequest

          // Create the store
          const storeRef = await addDoc(collection(db, 'stores'), {
            name: requestData.store.name,
            slug: requestData.store.slug,
            currency: requestData.store.currency,
            country: requestData.store.country,
            description: requestData.store.description || '',
            ownerId: requestData.owner.uid,
            status: 'active',
            domain: null,
            contactEmail: requestData.owner.email,
            contactPhone: null,
            logoUrl: null,
            primaryColor: '#3B82F6',
            secondaryColor: '#1E40AF',
            taxRate: 0,
            settings: {
              timezone: 'UTC',
              language: 'en',
              theme: 'light'
            },
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          })

          // Update the request status
          await updateDoc(doc(db, 'pending_store_requests', requestId), {
            status: 'approved',
            reviewedAt: serverTimestamp()
          })

          // Note: Custom claims should be set via Firebase Functions
          // This is a placeholder for the actual implementation
          console.log('Store approved:', storeRef.id)
          
        } catch (error: any) {
          console.error('Error approving store request:', error)
          set({ error: error.message })
          throw error
        }
      },

      rejectStoreRequest: async (requestId: string, reason: string) => {
        set({ error: null })
        try {
          await updateDoc(doc(db, 'pending_store_requests', requestId), {
            status: 'rejected',
            rejectionReason: reason,
            reviewedAt: serverTimestamp()
          })
        } catch (error: any) {
          console.error('Error rejecting store request:', error)
          set({ error: error.message })
          throw error
        }
      },

      // Update current store
      updateCurrentStore: async (updates: Partial<Store>) => {
        const { currentStoreId, currentStore } = get()

        if (!currentStoreId || !currentStore) {
          set({ error: 'No current store to update' })
          return
        }

        set({ error: null })
        try {
          await updateDoc(doc(db, 'stores', currentStoreId), {
            ...updates,
            updatedAt: serverTimestamp()
          })

          // Update local state
          set({
            currentStore: { ...currentStore, ...updates }
          })
        } catch (error: any) {
          console.error('Error updating current store:', error)
          set({ error: error.message })
          throw error
        }
      },

      // Switch to a different store (for super admins)
      switchStore: async (storeId: string) => {
        try {
          set({ loading: true, error: null })

          const storeDoc = await getDoc(doc(db, 'stores', storeId))

          if (storeDoc.exists()) {
            const store = { id: storeDoc.id, ...storeDoc.data() } as Store
            set({
              currentStore: store,
              currentStoreId: storeId,
              loading: false
            })
            // Save to localStorage for services that need it
            localStorage.setItem('currentStoreId', storeId)
            console.log('🔄 Switched to store:', storeId)
          } else {
            set({
              error: `Store ${storeId} not found`,
              loading: false
            })
          }
        } catch (error: any) {
          console.error('Error switching store:', error)
          set({
            error: error.message,
            loading: false
          })
          throw error
        }
      },

      // Sync store name from settings to main store document
      syncStoreNameFromSettings: async (storeId: string, storeName: string, storeSlug?: string) => {
        try {
          const storeRef = doc(db, 'stores', storeId)
          const updateData: any = {
            name: storeName,
            updatedAt: serverTimestamp()
          }

          if (storeSlug) {
            updateData.slug = storeSlug
          }

          await updateDoc(storeRef, updateData)

          // Update current store in state if it's the same store
          const { currentStore } = get()
          if (currentStore?.id === storeId) {
            set({
              currentStore: {
                ...currentStore,
                name: storeName,
                slug: storeSlug || currentStore.slug
              }
            })
          }

          console.log('✅ Store name synced to main document:', storeName)
        } catch (error) {
          console.error('❌ Error syncing store name:', error)
          throw error
        }
      }
    }),
    {
      name: 'store-management-individual',
      partialize: (state) => ({
        currentStoreId: state.currentStoreId,
        currentStore: state.currentStore
      })
    }
  )
)
