import { useState } from 'react'
import { createUserWithEmailAndPassword } from 'firebase/auth'
import { doc, setDoc } from 'firebase/firestore'
import { auth, db } from '../../lib/firebase'
import { Button } from '../ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Alert, AlertDescription } from '../ui/alert'
import { UserPlus, CheckCircle, AlertCircle, Loader2 } from 'lucide-react'

const STORE_ID = 'womanza'

interface AdminUser {
  email: string
  password: string
  fullName: string
  role: 'super_admin' | 'admin' | 'editor' | 'viewer'
}

const defaultUsers: AdminUser[] = [
  {
    email: '<EMAIL>',
    password: 'password123',
    fullName: 'Super Admin',
    role: 'super_admin'
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    fullName: 'Store Manager',
    role: 'admin'
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    fullName: 'Content Editor',
    role: 'editor'
  }
]

export function AdminSetup() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<{ email: string; success: boolean; message: string }[]>([])
  const [isComplete, setIsComplete] = useState(false)

  const createUser = async (userData: AdminUser) => {
    try {
      // Create user in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        userData.email,
        userData.password
      )

      const user = userCredential.user

      // Create user document in Firestore
      const userDoc = {
        id: user.uid,
        email: userData.email,
        fullName: userData.fullName,
        role: userData.role,
        storeId: STORE_ID,
        storeIds: [STORE_ID],
        active: true,
        createdBy: null,
        createdAt: new Date().toISOString(),
        lastLoginAt: null,
        avatarUrl: null
      }

      await setDoc(doc(db, 'users', user.uid), userDoc)

      return { success: true, message: 'Created successfully' }
    } catch (error: any) {
      if (error.code === 'auth/email-already-in-use') {
        return { success: false, message: 'Email already exists' }
      }
      return { success: false, message: error.message || 'Failed to create user' }
    }
  }

  const setupAdminUsers = async () => {
    setLoading(true)
    setResults([])

    const newResults: { email: string; success: boolean; message: string }[] = []

    for (const userData of defaultUsers) {
      const result = await createUser(userData)
      newResults.push({
        email: userData.email,
        success: result.success,
        message: result.message
      })
    }

    setResults(newResults)
    setLoading(false)
    setIsComplete(true)
  }

  if (isComplete) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Admin Setup Complete
          </CardTitle>
          <CardDescription>
            Admin users have been set up for your store
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {results.map((result, index) => (
            <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium">{result.email}</p>
                <p className="text-sm text-gray-500">{result.message}</p>
              </div>
              {result.success ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <AlertCircle className="h-5 w-5 text-red-600" />
              )}
            </div>
          ))}
          
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Important:</strong> Please change the default passwords after first login.
              Default password for all accounts is: <code>password123</code>
            </AlertDescription>
          </Alert>

          <div className="pt-4">
            <Button 
              onClick={() => window.location.href = '/login'} 
              className="w-full"
            >
              Go to Login
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserPlus className="h-5 w-5" />
          Setup Admin Users
        </CardTitle>
        <CardDescription>
          Create the initial admin users for your Womanza store
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="font-medium">The following admin users will be created:</h3>
          {defaultUsers.map((user, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="font-medium">{user.email}</p>
                <p className="text-sm text-gray-500">{user.fullName} ({user.role.replace('_', ' ')})</p>
              </div>
            </div>
          ))}
        </div>

        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            All users will be created with the default password: <code>password123</code>
            <br />
            Please change these passwords after first login for security.
          </AlertDescription>
        </Alert>

        <Button 
          onClick={setupAdminUsers} 
          disabled={loading}
          className="w-full"
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating Users...
            </>
          ) : (
            <>
              <UserPlus className="mr-2 h-4 w-4" />
              Create Admin Users
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  )
}
