import React, { useState, useRef, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'

// UI Components
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { Label } from '../components/ui/label'
import { Badge } from '../components/ui/badge'
import { Select } from '../components/ui/select'
import { Textarea } from '../components/ui/textarea'
import { Switch } from '../components/ui/switch'
import { Checkbox } from '../components/ui/checkbox'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { HierarchicalSelect } from '../components/ui/hierarchical-select'
import { FallbackImage } from '../components/ui/fallback-image'

// Icons
import {
  Save,
  CheckCircle,
  Upload,
  X,
  Plus,
  Package,
  DollarSign,
  Image as ImageIcon,
  Video,
  Tag,
  Trash2,
  GripVertical,
  Search,
  Star,
  Info,
  AlertTriangle,
  Globe,
  Link as LinkIcon,
  Eye,
  Calendar,
  Clock,
  ArrowLeft
} from 'lucide-react'

// Types
import { ProductFormData, ValidationErrors, UploadProgress, Category } from '../types/product'

// Services
import { StorageService } from '../lib/storage-service'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { useCategoriesStore } from '../store/categories-store'
import { dataService } from '../lib/data-service'

// Tab type definition
type TabType = 'basic' | 'media' | 'pricing' | 'variants' | 'seo' | 'related' | 'review'

export function EditProductPage() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { user, hasPermission } = useFirebaseAuthStore()
  const { categories, loading: categoriesLoading, fetchCategories, subscribeToCategories } = useCategoriesStore()
  const storageService = new StorageService()

  // State management
  const [activeTab, setActiveTab] = useState<TabType>('basic')
  const [saving, setSaving] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<{ [key: number]: number }>({})
  const [errors, setErrors] = useState<ValidationErrors>({})
  const [newTag, setNewTag] = useState('')
  const [loading, setLoading] = useState(true)
  
  // Variant state
  const [selectedSizes, setSelectedSizes] = useState<string[]>([])
  const [selectedColors, setSelectedColors] = useState<string[]>([])
  const [selectedMaterials, setSelectedMaterials] = useState<string[]>([])
  const [customSize, setCustomSize] = useState('')
  const [customColor, setCustomColor] = useState('')
  
  // Related products state
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedRelatedProducts, setSelectedRelatedProducts] = useState<string[]>([])
  
  // Variant settings state
  const [variantSettings, setVariantSettings] = useState({
    trackInventory: false,
    allowCustomerSelection: true,
    showVariantImages: false
  })
  
  // Related products settings state
  const [relatedSettings, setRelatedSettings] = useState({
    showOnProductPage: true,
    showInCart: false,
    maxProducts: 4,
    sectionTitle: 'You might also like'
  })

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null)
  const autoGeneratedRef = useRef({
    slug: false,
    sku: false,
    seoTitle: false
  })

  const canEdit = hasPermission(['super_admin', 'admin', 'editor'])

  // Form data state
  const [form, setForm] = useState<ProductFormData>({
    // Basic Info
    name: '',
    slug: '',
    description: '',
    categoryId: '',
    tags: [],
    status: 'draft',

    // Images & Media
    images: [],
    videoUrl: '',

    // Pricing & Inventory
    price: {
      regular: 0,
      sale: 0
    },
    sku: '',
    stock: {
      quantity: 0,
      status: 'in_stock',
      allowBackorders: false
    },
    unit: 'pcs',

    // Variants
    variants: [],
    variantAttributes: {},

    // SEO
    seo: {
      title: '',
      description: '',
      canonicalUrl: ''
    },

    // Related Products
    relatedProducts: [],

    // Additional
    featured: false
  })

  useEffect(() => {
    if (id) {
      fetchProduct(id)
    }

    // Fetch categories and set up real-time subscription
    fetchCategories()
    const unsubscribe = subscribeToCategories()

    // Cleanup subscription on unmount
    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
    }
  }, [id])

  // Utility functions
  const updateForm = (field: keyof ProductFormData, value: any) => {
    setForm(prev => ({ ...prev, [field]: value }))
    // Clear errors when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  // Variant helper functions
  const toggleSize = (size: string) => {
    setSelectedSizes(prev => 
      prev.includes(size) 
        ? prev.filter(s => s !== size)
        : [...prev, size]
    )
  }

  const toggleColor = (color: string) => {
    setSelectedColors(prev => 
      prev.includes(color) 
        ? prev.filter(c => c !== color)
        : [...prev, color]
    )
  }

  const toggleMaterial = (material: string) => {
    setSelectedMaterials(prev => 
      prev.includes(material) 
        ? prev.filter(m => m !== material)
        : [...prev, material]
    )
  }

  const addCustomSize = () => {
    if (customSize.trim() && !selectedSizes.includes(customSize.trim())) {
      setSelectedSizes(prev => [...prev, customSize.trim()])
      setCustomSize('')
    }
  }

  const addCustomColor = () => {
    if (customColor.trim() && !selectedColors.includes(customColor.trim())) {
      setSelectedColors(prev => [...prev, customColor.trim()])
      setCustomColor('')
    }
  }

  // Related products helper functions
  const toggleRelatedProduct = (productId: string) => {
    setSelectedRelatedProducts(prev => 
      prev.includes(productId) 
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    )
  }

  const fetchProduct = async (productId: string) => {
    try {
      setLoading(true)
      const data = await dataService.getProduct(productId)
      if (data) {
        // Map the fetched product data to form state
        setForm({
          name: data.name || '',
          slug: data.slug || '',
          description: data.description || '',
          categoryId: data.categoryId || '',
          tags: data.tags || [],
          status: data.status || 'draft',
          images: data.images || [],
          videoUrl: data.videoUrl || '',
          price: {
            regular: data.price?.regular || 0,
            sale: data.price?.sale || 0
          },
          sku: data.sku || '',
          stock: {
            quantity: data.stock?.quantity || 0,
            status: data.stock?.status || 'in_stock',
            allowBackorders: data.stock?.allowBackorders || false
          },
          unit: data.unit || 'pcs',
          variants: data.variants || [],
          variantAttributes: data.variantAttributes || {},
          seo: {
            title: data.seo?.title || '',
            description: data.seo?.description || '',
            canonicalUrl: data.seo?.canonicalUrl || ''
          },
          relatedProducts: data.relatedProducts || [],
          featured: data.featured || false
        })

        // Set variant state if available
        if (data.variantAttributes) {
          setSelectedSizes(data.variantAttributes.sizes || [])
          setSelectedColors(data.variantAttributes.colors || [])
          setSelectedMaterials(data.variantAttributes.materials || [])
          setVariantSettings(data.variantAttributes.settings || variantSettings)
        }

        // Set related products state if available
        if (data.relatedProducts) {
          setSelectedRelatedProducts(data.relatedProducts)
        }
        if (data.relatedProductsSettings) {
          setRelatedSettings(data.relatedProductsSettings)
        }
      }
    } catch (error) {
      console.error('Error fetching product:', error)
      toast.error('Failed to load product')
    } finally {
      setLoading(false)
    }
  }



  // Tag management
  const addTag = () => {
    if (newTag.trim() && !form.tags.includes(newTag.trim())) {
      updateForm('tags', [...form.tags, newTag.trim()])
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    updateForm('tags', form.tags.filter(tag => tag !== tagToRemove))
  }

  // Save functionality
  const handleSave = async (status: 'draft' | 'published') => {
    if (!id) return

    setSaving(true)
    try {
      const productData = {
        name: form.name,
        slug: form.slug,
        description: form.description || null,
        categoryId: form.categoryId || null,
        tags: form.tags || [],
        images: form.images || [],
        videoUrl: form.videoUrl || null,
        price: {
          regular: form.price.regular || 0,
          sale: form.price.sale && form.price.sale > 0 ? form.price.sale : null
        },
        sku: form.sku,
        stock: {
          quantity: form.stock.quantity || 0,
          status: form.stock.status || 'in_stock',
          allowBackorders: form.stock.allowBackorders || false
        },
        unit: form.unit || 'pcs',
        variants: form.variants || [],
        variantAttributes: {
          sizes: selectedSizes,
          colors: selectedColors,
          materials: selectedMaterials,
          settings: variantSettings
        },
        seo: {
          title: form.seo.title || `${form.name} - Womanza`,
          description: form.seo.description || null,
          canonicalUrl: form.seo.canonicalUrl || null
        },
        relatedProducts: selectedRelatedProducts,
        relatedProductsSettings: relatedSettings,
        featured: form.featured || false,
        status: status,
        updatedAt: new Date().toISOString(),
        ...(status === 'published' && { publishedAt: new Date().toISOString() })
      }

      await dataService.updateProduct(id, productData)
      toast.success(`Product ${status === 'published' ? 'published' : 'updated'} successfully!`)
      navigate(`/admin/products/${id}`)
    } catch (error) {
      console.error('Error updating product:', error)
      toast.error('Failed to update product')
    } finally {
      setSaving(false)
    }
  }

  const canPublish = (): boolean => {
    return !!(
      form.name.trim() &&
      form.categoryId &&
      form.price.regular > 0 &&
      form.slug.trim()
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading product...</p>
        </div>
      </div>
    )
  }

  if (!form.name && !loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">Product Not Found</h2>
          <p className="text-gray-500 dark:text-gray-400 mb-4">The product you're looking for doesn't exist.</p>
          <Button onClick={() => navigate('/admin/products')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Products
          </Button>
        </div>
      </div>
    )
  }

  // Tab configuration
  const tabs = [
    { id: 'basic' as TabType, label: 'Basic Info', icon: Package },
    { id: 'media' as TabType, label: 'Images & Media', icon: ImageIcon },
    { id: 'pricing' as TabType, label: 'Pricing & Inventory', icon: DollarSign },
    { id: 'variants' as TabType, label: 'Variants', icon: Tag },
    { id: 'seo' as TabType, label: 'SEO', icon: Globe },
    { id: 'related' as TabType, label: 'Related Products', icon: LinkIcon },
    { id: 'review' as TabType, label: 'Review & Update', icon: CheckCircle }
  ]

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/admin/products/${id}`)}
            className="hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">Edit Product</h1>
            <p className="text-gray-500 dark:text-gray-400">Update product information</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={() => handleSave('draft')}
            disabled={saving}
          >
            <Save className="h-4 w-4 mr-2" />
            Save as Draft
          </Button>
          <Button
            onClick={() => handleSave('published')}
            disabled={saving || !canPublish()}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            {saving ? 'Updating...' : 'Update & Publish'}
          </Button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <Icon className="h-4 w-4" />
                {tab.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {/* Basic Info Tab */}
        {activeTab === 'basic' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - Main Info */}
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Product Information</CardTitle>
                  <CardDescription>
                    Basic details about your product
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="name" className="text-sm font-medium">
                      Product Name *
                    </Label>
                    <Input
                      id="name"
                      value={form.name}
                      onChange={(e) => updateForm('name', e.target.value)}
                      placeholder="Enter product name"
                      className={errors.name ? 'border-red-500' : ''}
                    />
                    {errors.name && (
                      <p className="text-sm text-red-500 mt-1">{errors.name}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="slug" className="text-sm font-medium">
                      Product Slug
                    </Label>
                    <Input
                      id="slug"
                      value={form.slug}
                      onChange={(e) => updateForm('slug', e.target.value)}
                      placeholder="product-slug"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      URL-friendly version of the name. Leave blank to auto-generate.
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="description" className="text-sm font-medium">
                      Description
                    </Label>
                    <Textarea
                      id="description"
                      value={form.description}
                      onChange={(e) => updateForm('description', e.target.value)}
                      placeholder="Describe your product..."
                      rows={4}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Product Tags</CardTitle>
                  <CardDescription>
                    Add tags to help customers find your product
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-2">
                    <Input
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      placeholder="Add a tag"
                      onKeyPress={(e) => e.key === 'Enter' && addTag()}
                    />
                    <Button type="button" onClick={addTag} disabled={!newTag.trim()}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  {form.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {form.tags.map((tag, index) => (
                        <Badge
                          key={index}
                          variant="secondary"
                          className="cursor-pointer"
                          onClick={() => removeTag(tag)}
                        >
                          {tag} ×
                        </Badge>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Right Column - Category & Status */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Organization</CardTitle>
                  <CardDescription>
                    Categorize and organize your product
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="category" className="text-sm font-medium">
                      Category *
                    </Label>
                    <HierarchicalSelect
                      options={categories.map(cat => ({
                        value: cat.id,
                        label: cat.name,
                        parentId: cat.parentId
                      }))}
                      value={form.categoryId}
                      onValueChange={(value) => updateForm('categoryId', value)}
                      placeholder={categoriesLoading ? "Loading categories..." : "Select a category"}
                      className={errors.categoryId ? 'border-red-500' : ''}
                      disabled={categoriesLoading}
                    />
                    {errors.categoryId && (
                      <p className="text-sm text-red-500 mt-1">{errors.categoryId}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="sku" className="text-sm font-medium">
                      SKU *
                    </Label>
                    <Input
                      id="sku"
                      value={form.sku}
                      onChange={(e) => updateForm('sku', e.target.value)}
                      placeholder="Product SKU"
                      className={errors.sku ? 'border-red-500' : ''}
                    />
                    {errors.sku && (
                      <p className="text-sm text-red-500 mt-1">{errors.sku}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="status" className="text-sm font-medium">
                      Status
                    </Label>
                    <Select
                      value={form.status}
                      onValueChange={(value: 'draft' | 'published' | 'archived') =>
                        updateForm('status', value)
                      }
                      options={[
                        { value: 'draft', label: 'Draft' },
                        { value: 'published', label: 'Published' },
                        { value: 'archived', label: 'Archived' }
                      ]}
                      placeholder="Select status"
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="featured"
                      checked={form.featured}
                      onCheckedChange={(checked) => updateForm('featured', checked)}
                    />
                    <Label htmlFor="featured" className="text-sm font-medium">
                      Featured Product
                    </Label>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Media Tab */}
        {activeTab === 'media' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Product Images</CardTitle>
                <CardDescription>
                  Upload high-quality images of your product
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {form.images.map((image, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={typeof image === 'string' ? image : image.url}
                        alt={`Product image ${index + 1}`}
                        className="w-full h-32 object-cover rounded-lg border border-gray-200 dark:border-gray-700"
                        onError={(e) => {
                          // Handle broken images
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const parent = target.parentElement;
                          if (parent && !parent.querySelector('.image-placeholder')) {
                            const placeholder = document.createElement('div');
                            placeholder.className = 'image-placeholder w-full h-32 bg-gray-200 dark:bg-gray-700 flex items-center justify-center rounded-lg border border-gray-200 dark:border-gray-700 text-gray-400';
                            placeholder.textContent = 'Image not found';
                            parent.appendChild(placeholder);
                          }
                        }}
                      />
                      <Button
                        variant="destructive"
                        size="sm"
                        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => {
                          const newImages = form.images.filter((_, i) => i !== index)
                          updateForm('images', newImages)
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  <div
                    className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg h-32 flex items-center justify-center cursor-pointer hover:border-gray-400 dark:hover:border-gray-500 transition-colors"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <div className="text-center">
                      <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-500">Add Image</p>
                    </div>
                  </div>
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  multiple
                  className="hidden"
                  onChange={() => {
                    // Handle file upload logic here
                    console.log('File upload triggered')
                  }}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Product Video</CardTitle>
                <CardDescription>
                  Add a video URL to showcase your product
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="videoUrl" className="text-sm font-medium">
                      Video URL
                    </Label>
                    <Input
                      id="videoUrl"
                      value={form.videoUrl}
                      onChange={(e) => updateForm('videoUrl', e.target.value)}
                      placeholder="https://youtube.com/watch?v=..."
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Supports YouTube, Vimeo, and direct video links
                    </p>
                  </div>
                  {form.videoUrl && (
                    <div className="aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                      <Video className="h-12 w-12 text-gray-400" />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Pricing & Inventory Tab */}
        {activeTab === 'pricing' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Pricing</CardTitle>
                <CardDescription>
                  Set your product pricing
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="regularPrice" className="text-sm font-medium">
                    Regular Price *
                  </Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="regularPrice"
                      type="number"
                      min="0"
                      step="0.01"
                      value={form.price.regular}
                      onChange={(e) => updateForm('price', { ...form.price, regular: parseFloat(e.target.value) || 0 })}
                      placeholder="0.00"
                      className={`pl-10 ${errors.price ? 'border-red-500' : ''}`}
                    />
                  </div>
                  {errors.price && (
                    <p className="text-sm text-red-500 mt-1">{errors.price}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="salePrice" className="text-sm font-medium">
                    Sale Price
                  </Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="salePrice"
                      type="number"
                      min="0"
                      step="0.01"
                      value={form.price.sale}
                      onChange={(e) => updateForm('price', { ...form.price, sale: parseFloat(e.target.value) || 0 })}
                      placeholder="0.00"
                      className={`pl-10 ${errors.salePrice ? 'border-red-500' : ''}`}
                    />
                  </div>
                  {errors.salePrice && (
                    <p className="text-sm text-red-500 mt-1">{errors.salePrice}</p>
                  )}
                  <p className="text-xs text-gray-500 mt-1">
                    Leave blank if not on sale
                  </p>
                </div>

                <div>
                  <Label htmlFor="unit" className="text-sm font-medium">
                    Unit
                  </Label>
                  <Select
                    value={form.unit}
                    onValueChange={(value) => updateForm('unit', value)}
                    options={[
                      { value: 'pcs', label: 'Pieces' },
                      { value: 'kg', label: 'Kilograms' },
                      { value: 'g', label: 'Grams' },
                      { value: 'l', label: 'Liters' },
                      { value: 'ml', label: 'Milliliters' },
                      { value: 'm', label: 'Meters' },
                      { value: 'cm', label: 'Centimeters' }
                    ]}
                    placeholder="Select unit"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Inventory</CardTitle>
                <CardDescription>
                  Manage your product inventory
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="quantity" className="text-sm font-medium">
                    Stock Quantity
                  </Label>
                  <Input
                    id="quantity"
                    type="number"
                    min="0"
                    value={form.stock.quantity}
                    onChange={(e) => updateForm('stock', { ...form.stock, quantity: parseInt(e.target.value) || 0 })}
                    placeholder="0"
                    className={errors.stock ? 'border-red-500' : ''}
                  />
                  {errors.stock && (
                    <p className="text-sm text-red-500 mt-1">{errors.stock}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="stockStatus" className="text-sm font-medium">
                    Stock Status
                  </Label>
                  <Select
                    value={form.stock.status}
                    onValueChange={(value: 'in_stock' | 'out_of_stock' | 'low_stock') =>
                      updateForm('stock', { ...form.stock, status: value })
                    }
                    options={[
                      { value: 'in_stock', label: 'In Stock' },
                      { value: 'low_stock', label: 'Low Stock' },
                      { value: 'out_of_stock', label: 'Out of Stock' }
                    ]}
                    placeholder="Select stock status"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="allowBackorders"
                    checked={form.stock.allowBackorders}
                    onCheckedChange={(checked) =>
                      updateForm('stock', { ...form.stock, allowBackorders: checked })
                    }
                  />
                  <Label htmlFor="allowBackorders" className="text-sm font-medium">
                    Allow backorders
                  </Label>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Variants Tab */}
        {activeTab === 'variants' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Product Attributes</CardTitle>
                <CardDescription>
                  Define attributes like size, color, or material that create product variations
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Size Options</Label>
                    <div className="mt-2 space-y-2">
                      <div className="flex flex-wrap gap-2">
                        {['XS', 'S', 'M', 'L', 'XL', 'XXL'].map((size) => (
                          <Button
                            key={size}
                            variant={selectedSizes.includes(size) ? "default" : "outline"}
                            size="sm"
                            className="h-8 px-3"
                            onClick={() => toggleSize(size)}
                          >
                            {size}
                          </Button>
                        ))}
                      </div>
                      <div className="flex gap-2">
                        <Input
                          placeholder="Add custom size"
                          className="text-sm"
                          value={customSize}
                          onChange={(e) => setCustomSize(e.target.value)}
                          onKeyPress={(e) => e.key === 'Enter' && addCustomSize()}
                        />
                        <Button
                          type="button"
                          size="sm"
                          onClick={addCustomSize}
                          disabled={!customSize.trim()}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      {selectedSizes.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {selectedSizes.map((size) => (
                            <Badge
                              key={size}
                              variant="secondary"
                              className="text-xs cursor-pointer"
                              onClick={() => toggleSize(size)}
                            >
                              {size} ×
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Color Options</Label>
                    <div className="mt-2 space-y-2">
                      <div className="grid grid-cols-4 gap-2">
                        {[
                          { name: 'Black', color: '#000000' },
                          { name: 'White', color: '#FFFFFF' },
                          { name: 'Red', color: '#EF4444' },
                          { name: 'Blue', color: '#3B82F6' },
                          { name: 'Green', color: '#10B981' },
                          { name: 'Yellow', color: '#F59E0B' },
                          { name: 'Purple', color: '#8B5CF6' },
                          { name: 'Pink', color: '#EC4899' }
                        ].map((colorOption) => (
                          <div
                            key={colorOption.name}
                            className={`flex flex-col items-center p-2 border rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 ${
                              selectedColors.includes(colorOption.name)
                                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                                : 'border-gray-200 dark:border-gray-700'
                            }`}
                            onClick={() => toggleColor(colorOption.name)}
                          >
                            <div
                              className="w-6 h-6 rounded-full border border-gray-300 dark:border-gray-600"
                              style={{ backgroundColor: colorOption.color }}
                            />
                            <span className="text-xs mt-1">{colorOption.name}</span>
                          </div>
                        ))}
                      </div>
                      <div className="flex gap-2">
                        <Input
                          placeholder="Add custom color"
                          className="text-sm"
                          value={customColor}
                          onChange={(e) => setCustomColor(e.target.value)}
                          onKeyPress={(e) => e.key === 'Enter' && addCustomColor()}
                        />
                        <Button
                          type="button"
                          size="sm"
                          onClick={addCustomColor}
                          disabled={!customColor.trim()}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      {selectedColors.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {selectedColors.map((color) => (
                            <Badge
                              key={color}
                              variant="secondary"
                              className="text-xs cursor-pointer"
                              onClick={() => toggleColor(color)}
                            >
                              {color} ×
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium">Material Options</Label>
                  <div className="mt-2 space-y-2">
                    <div className="flex flex-wrap gap-2">
                      {['Cotton', 'Polyester', 'Silk', 'Wool', 'Linen', 'Denim'].map((material) => (
                        <Button
                          key={material}
                          variant={selectedMaterials.includes(material) ? "default" : "outline"}
                          size="sm"
                          className="h-8 px-3"
                          onClick={() => toggleMaterial(material)}
                        >
                          {material}
                        </Button>
                      ))}
                    </div>
                    {selectedMaterials.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {selectedMaterials.map((material) => (
                          <Badge
                            key={material}
                            variant="secondary"
                            className="text-xs cursor-pointer"
                            onClick={() => toggleMaterial(material)}
                          >
                            {material} ×
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Variant Settings</CardTitle>
                <CardDescription>
                  Configure how variants are displayed and managed
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Track inventory by variant</Label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Enable individual stock tracking for each variant
                    </p>
                  </div>
                  <Switch
                    checked={variantSettings.trackInventory}
                    onCheckedChange={(checked) =>
                      setVariantSettings(prev => ({ ...prev, trackInventory: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Allow customers to select variants</Label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Show variant options on product page
                    </p>
                  </div>
                  <Switch
                    checked={variantSettings.allowCustomerSelection}
                    onCheckedChange={(checked) =>
                      setVariantSettings(prev => ({ ...prev, allowCustomerSelection: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Show variant images</Label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Display different images for each variant
                    </p>
                  </div>
                  <Switch
                    checked={variantSettings.showVariantImages}
                    onCheckedChange={(checked) =>
                      setVariantSettings(prev => ({ ...prev, showVariantImages: checked }))
                    }
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* SEO Tab */}
        {activeTab === 'seo' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Search Engine Optimization</CardTitle>
                <CardDescription>
                  Optimize your product for search engines
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="seoTitle" className="text-sm font-medium">
                    SEO Title
                  </Label>
                  <Input
                    id="seoTitle"
                    value={form.seo.title}
                    onChange={(e) => updateForm('seo', { ...form.seo, title: e.target.value })}
                    placeholder="SEO optimized title"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Recommended: 50-60 characters
                  </p>
                </div>

                <div>
                  <Label htmlFor="seoDescription" className="text-sm font-medium">
                    SEO Description
                  </Label>
                  <Textarea
                    id="seoDescription"
                    value={form.seo.description}
                    onChange={(e) => updateForm('seo', { ...form.seo, description: e.target.value })}
                    placeholder="SEO meta description"
                    rows={3}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Recommended: 150-160 characters
                  </p>
                </div>

                <div>
                  <Label htmlFor="canonicalUrl" className="text-sm font-medium">
                    Canonical URL
                  </Label>
                  <Input
                    id="canonicalUrl"
                    value={form.seo.canonicalUrl}
                    onChange={(e) => updateForm('seo', { ...form.seo, canonicalUrl: e.target.value })}
                    placeholder="https://example.com/product-url"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Leave blank to use default URL
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Related Products Tab */}
        {activeTab === 'related' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Add Related Products</CardTitle>
                <CardDescription>
                  Search and select products to recommend to customers
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search products by name, SKU, or category..."
                    className="pl-10"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                <div className="border border-gray-200 dark:border-gray-700 rounded-lg max-h-64 overflow-y-auto">
                  <div className="p-2 space-y-1">
                    {[
                      { id: '1', name: 'Premium Cotton T-Shirt', price: 29.99, category: 'T-Shirts' },
                      { id: '2', name: 'Denim Jacket', price: 89.99, category: 'Jackets' },
                      { id: '3', name: 'Summer Dress', price: 59.99, category: 'Dresses' },
                      { id: '4', name: 'Casual Sneakers', price: 79.99, category: 'Shoes' }
                    ].map((product) => (
                      <div
                        key={product.id}
                        className="flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg cursor-pointer"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                            <ImageIcon className="h-6 w-6 text-gray-400" />
                          </div>
                          <div>
                            <p className="font-medium text-sm">{product.name}</p>
                            <p className="text-xs text-gray-500">{product.category} • ${product.price}</p>
                          </div>
                        </div>
                        <Button
                          size="sm"
                          variant={selectedRelatedProducts.includes(product.id) ? "default" : "outline"}
                          onClick={() => toggleRelatedProduct(product.id)}
                        >
                          {selectedRelatedProducts.includes(product.id) ? (
                            <CheckCircle className="h-4 w-4" />
                          ) : (
                            <Plus className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Display Settings</CardTitle>
                <CardDescription>
                  Configure how related products are shown to customers
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Show related products on product page</Label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Display recommendations below product details
                    </p>
                  </div>
                  <Switch
                    checked={relatedSettings.showOnProductPage}
                    onCheckedChange={(checked) =>
                      setRelatedSettings(prev => ({ ...prev, showOnProductPage: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Show in cart recommendations</Label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Suggest related products when item is added to cart
                    </p>
                  </div>
                  <Switch
                    checked={relatedSettings.showInCart}
                    onCheckedChange={(checked) =>
                      setRelatedSettings(prev => ({ ...prev, showInCart: checked }))
                    }
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="maxRelated" className="text-sm font-medium">Maximum products to show</Label>
                    <Input
                      id="maxRelated"
                      type="number"
                      min="1"
                      max="12"
                      value={relatedSettings.maxProducts}
                      onChange={(e) =>
                        setRelatedSettings(prev => ({
                          ...prev,
                          maxProducts: parseInt(e.target.value) || 4
                        }))
                      }
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="relatedTitle" className="text-sm font-medium">Section title</Label>
                    <Input
                      id="relatedTitle"
                      value={relatedSettings.sectionTitle}
                      onChange={(e) =>
                        setRelatedSettings(prev => ({
                          ...prev,
                          sectionTitle: e.target.value
                        }))
                      }
                      className="mt-1"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Review & Update Tab */}
        {activeTab === 'review' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Review Product Information</CardTitle>
                <CardDescription>
                  Review all product details before updating
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">Basic Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-gray-500">Name:</span>
                      <p>{form.name || 'Not set'}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-500">SKU:</span>
                      <p>{form.sku || 'Not set'}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-500">Category:</span>
                      <p>{categories?.find(c => c.id === form.categoryId)?.name || 'Not set'}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-500">Status:</span>
                      <Badge variant={form.status === 'published' ? 'default' : 'secondary'}>
                        {form.status}
                      </Badge>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-3">Pricing & Inventory</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-gray-500">Regular Price:</span>
                      <p>${form.price.regular}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-500">Sale Price:</span>
                      <p>{form.price.sale ? `$${form.price.sale}` : 'Not set'}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-500">Stock Quantity:</span>
                      <p>{form.stock.quantity} {form.unit}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-500">Stock Status:</span>
                      <Badge variant={form.stock.status === 'in_stock' ? 'default' : 'destructive'}>
                        {form.stock.status.replace('_', ' ')}
                      </Badge>
                    </div>
                  </div>
                </div>

                {(selectedSizes.length > 0 || selectedColors.length > 0 || selectedMaterials.length > 0) && (
                  <div>
                    <h3 className="text-lg font-medium mb-3">Variants</h3>
                    <div className="space-y-2 text-sm">
                      {selectedSizes.length > 0 && (
                        <div>
                          <span className="font-medium text-gray-500">Sizes:</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {selectedSizes.map(size => (
                              <Badge key={size} variant="outline">{size}</Badge>
                            ))}
                          </div>
                        </div>
                      )}
                      {selectedColors.length > 0 && (
                        <div>
                          <span className="font-medium text-gray-500">Colors:</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {selectedColors.map(color => (
                              <Badge key={color} variant="outline">{color}</Badge>
                            ))}
                          </div>
                        </div>
                      )}
                      {selectedMaterials.length > 0 && (
                        <div>
                          <span className="font-medium text-gray-500">Materials:</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {selectedMaterials.map(material => (
                              <Badge key={material} variant="outline">{material}</Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className="flex justify-end gap-3 pt-6 border-t">
                  <Button
                    variant="outline"
                    onClick={() => handleSave('draft')}
                    disabled={saving}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save as Draft
                  </Button>
                  <Button
                    onClick={() => handleSave('published')}
                    disabled={saving || !canPublish()}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    {saving ? 'Updating...' : 'Update & Publish'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
