#!/usr/bin/env node

/**
 * Womanza Admin Panel - Settings Initialization Script
 * 
 * This script initializes store settings directly in Firestore
 * without requiring browser console commands.
 */

import { initializeApp } from 'firebase/app'
import { getFirestore, doc, setDoc, getDoc, serverTimestamp } from 'firebase/firestore'
import { getAuth, signInWithEmailAndPassword } from 'firebase/auth'
import readline from 'readline'

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAxCHZm5Sywq6m8GDfy8VhgInuVG8V5jE4",
  authDomain: "womanza-store.firebaseapp.com",
  projectId: "womanza-store",
  storageBucket: "womanza-store.firebasestorage.app",
  messagingSenderId: "133271608005",
  appId: "1:133271608005:web:576cdefabd87ea7e886eac"
}

// Initialize Firebase
const app = initializeApp(firebaseConfig)
const db = getFirestore(app)
const auth = getAuth(app)

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

// Utility function to prompt user input
function prompt(question) {
  return new Promise((resolve) => {
    rl.question(question, resolve)
  })
}

// Default settings for each category
const defaultSettings = {
  general: {
    storeName: 'Womanza Jewelry Store',
    storeDescription: 'Premium handcrafted jewelry and accessories',
    storeEmail: '<EMAIL>',
    storePhone: '+****************',
    storeAddress: '123 Jewelry Lane, Fashion District, NY 10001',
    timezone: 'America/New_York',
    currency: 'USD',
    language: 'en',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12h',
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  branding: {
    primaryColor: '#8B5CF6',
    secondaryColor: '#06B6D4',
    accentColor: '#F59E0B',
    logoUrl: '',
    faviconUrl: '',
    brandFontFamily: 'Inter',
    headingFontFamily: 'Playfair Display',
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  seo: {
    metaTitle: 'Womanza Jewelry Store - Premium Handcrafted Jewelry',
    metaDescription: 'Discover our exquisite collection of handcrafted jewelry and accessories. Premium quality, unique designs, and exceptional craftsmanship.',
    metaKeywords: 'jewelry, handcrafted, premium, accessories, rings, necklaces, earrings',
    ogTitle: 'Womanza Jewelry Store',
    ogDescription: 'Premium handcrafted jewelry and accessories',
    ogImage: '',
    twitterCard: 'summary_large_image',
    structuredDataEnabled: true,
    sitemapEnabled: true,
    robotsEnabled: true,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  auth: {
    allowRegistration: true,
    requireEmailVerification: true,
    enableSocialLogin: false,
    googleLoginEnabled: false,
    facebookLoginEnabled: false,
    twitterLoginEnabled: false,
    passwordMinLength: 8,
    requireStrongPassword: true,
    enableTwoFactor: false,
    sessionTimeout: 24,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  'email-config': {
    fromEmail: '<EMAIL>',
    fromName: 'Womanza Jewelry Store',
    smtpHost: '',
    smtpPort: 587,
    smtpUsername: '',
    smtpPassword: '',
    smtpSecure: true,
    orderConfirmationEnabled: true,
    orderStatusUpdateEnabled: true,
    marketingEmailsEnabled: false,
    newsletterEnabled: true,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  payment: {
    stripeEnabled: false,
    stripePublishableKey: '',
    stripeSecretKey: '',
    paypalEnabled: false,
    paypalClientId: '',
    paypalClientSecret: '',
    cashOnDeliveryEnabled: true,
    bankTransferEnabled: true,
    defaultCurrency: 'USD',
    acceptedCurrencies: ['USD', 'EUR', 'GBP'],
    taxIncluded: false,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  shipping: {
    freeShippingThreshold: 100,
    freeShippingEnabled: true,
    flatRateShipping: 10,
    flatRateEnabled: true,
    localDeliveryEnabled: true,
    localDeliveryFee: 5,
    internationalShippingEnabled: false,
    estimatedDeliveryDays: 7,
    trackingEnabled: false,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  tax: {
    taxEnabled: true,
    defaultTaxRate: 8.5,
    taxIncludedInPrice: false,
    taxCalculationMethod: 'destination',
    taxDisplayMethod: 'excluding',
    digitalProductsTaxable: true,
    shippingTaxable: false,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  compliance: {
    gdprEnabled: true,
    ccpaEnabled: false,
    cookieConsentEnabled: true,
    privacyPolicyUrl: '/privacy-policy',
    termsOfServiceUrl: '/terms-of-service',
    refundPolicyUrl: '/refund-policy',
    dataRetentionDays: 365,
    anonymizeData: true,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  behavior: {
    autoSaveEnabled: false,
    realTimeUpdatesEnabled: true,
    notificationsEnabled: true,
    soundEnabled: false,
    animationsEnabled: true,
    compactModeEnabled: false,
    darkModeEnabled: false,
    sidebarCollapsed: false,
    itemsPerPage: 25,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  'panel-preferences': {
    dashboardLayout: 'default',
    defaultView: 'grid',
    showWelcomeMessage: true,
    showQuickActions: true,
    showRecentActivity: true,
    showStatistics: true,
    autoRefreshInterval: 30,
    compactSidebar: false,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  }
}

async function checkStoreExists(storeId) {
  try {
    const storeRef = doc(db, 'stores', storeId)
    const storeSnap = await getDoc(storeRef)
    return storeSnap.exists()
  } catch (error) {
    console.error('Error checking store:', error)
    return false
  }
}

async function checkSettingsExist(storeId, category) {
  try {
    const settingsRef = doc(db, 'stores', storeId, 'settings', category)
    const settingsSnap = await getDoc(settingsRef)
    return settingsSnap.exists()
  } catch (error) {
    console.error(`Error checking ${category} settings:`, error)
    return false
  }
}

async function initializeStoreSettings(storeId) {
  console.log(`\n🔄 Initializing settings for store: ${storeId}`)
  
  // Check if store exists
  const storeExists = await checkStoreExists(storeId)
  if (!storeExists) {
    console.log(`❌ Store '${storeId}' does not exist. Please create the store first.`)
    return false
  }

  let successCount = 0
  let skipCount = 0
  let errorCount = 0

  for (const [category, settings] of Object.entries(defaultSettings)) {
    try {
      // Check if settings already exist
      const exists = await checkSettingsExist(storeId, category)
      
      if (exists) {
        console.log(`   ⏭️  ${category} settings already exist, skipping...`)
        skipCount++
        continue
      }

      // Create settings document
      const settingsRef = doc(db, 'stores', storeId, 'settings', category)
      await setDoc(settingsRef, settings)
      
      console.log(`   ✅ ${category} settings initialized`)
      successCount++
      
    } catch (error) {
      console.error(`   ❌ Failed to initialize ${category} settings:`, error.message)
      errorCount++
    }
  }

  console.log(`\n📊 Summary for ${storeId}:`)
  console.log(`   ✅ Created: ${successCount} categories`)
  console.log(`   ⏭️  Skipped: ${skipCount} categories (already exist)`)
  console.log(`   ❌ Errors: ${errorCount} categories`)
  
  return errorCount === 0
}

async function main() {
  console.log('🚀 Womanza Admin Panel - Settings Initialization Script')
  console.log('============================================================\n')

  try {
    // Get admin credentials
    const email = await prompt('Enter admin email: ')
    const password = await prompt('Enter admin password: ')

    console.log('\n🔐 Authenticating...')
    await signInWithEmailAndPassword(auth, email, password)
    console.log('✅ Authentication successful')

    // Get store ID
    const storeId = await prompt('\nEnter store ID (default: womanza-jewelry-store): ') || 'womanza-jewelry-store'

    // Initialize settings
    const success = await initializeStoreSettings(storeId)

    if (success) {
      console.log('\n🎉 Settings initialization completed successfully!')
      console.log('\n📋 Next steps:')
      console.log('1. Start your development server: npm run dev')
      console.log('2. Open http://localhost:5174')
      console.log('3. Navigate to Settings page')
      console.log('4. Verify all settings categories are loaded')
      console.log('5. Test the save/discard functionality')
    } else {
      console.log('\n⚠️  Settings initialization completed with some errors.')
      console.log('Please check the error messages above and try again.')
    }

  } catch (error) {
    console.error('\n❌ Script failed:', error.message)
    
    if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
      console.log('\n💡 Authentication failed. Please check your credentials.')
    } else if (error.code === 'permission-denied') {
      console.log('\n💡 Permission denied. Please ensure:')
      console.log('   - Your user has admin/editor role')
      console.log('   - Firestore rules allow settings write access')
      console.log('   - You have access to the specified store')
    }
  } finally {
    rl.close()
    process.exit(0)
  }
}

// Run the script
main().catch(console.error)
