import { create } from 'zustand'
import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  serverTimestamp
} from 'firebase/firestore'
import { db } from '../lib/firebase'
import { useStoreManagement } from './store-management'

// Get current store ID dynamically
const getCurrentStoreId = () => {
  return useStoreManagement.getState().currentStoreId || import.meta.env.VITE_STORE_ID || 'womanza'
}

export interface OrderItem {
  id: string
  productId: string
  productName: string
  productImage?: string
  sku: string
  quantity: number
  price: number
  total: number
}

export interface ShippingAddress {
  firstName: string
  lastName: string
  company?: string
  address1: string
  address2?: string
  city: string
  province: string
  country: string
  zip: string
  phone?: string
}

export interface Order {
  id: string
  orderNumber: string
  customerId: string
  customerEmail: string
  customerName: string
  items: OrderItem[]
  subtotal: number
  tax: number
  shipping: number
  discount: number
  total: number
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded'
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded' | 'partially_refunded'
  paymentMethod: string
  shippingAddress: ShippingAddress
  billingAddress?: ShippingAddress
  notes?: string
  tags: string[]
  fulfillmentStatus: 'unfulfilled' | 'partial' | 'fulfilled'
  trackingNumber?: string
  trackingUrl?: string
  storeId: string
  createdAt: string
  updatedAt: string
  createdBy: string
}

interface OrdersState {
  orders: Order[]
  loading: boolean
  error: string | null
  // Actions
  fetchOrders: () => Promise<void>
  addOrder: (order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) => Promise<string>
  updateOrder: (id: string, updates: Partial<Order>) => Promise<void>
  updateOrderStatus: (id: string, status: Order['status']) => Promise<void>
  updatePaymentStatus: (id: string, paymentStatus: Order['paymentStatus']) => Promise<void>
  updateFulfillmentStatus: (id: string, fulfillmentStatus: Order['fulfillmentStatus']) => Promise<void>
  getOrder: (id: string) => Promise<Order | null>
  // Real-time subscription
  subscribeToOrders: () => () => void
  clearError: () => void
}

export const useOrdersStore = create<OrdersState>((set, get) => ({
  orders: [],
  loading: false,
  error: null,

  clearError: () => set({ error: null }),

  fetchOrders: async () => {
    set({ loading: true, error: null })
    try {
      const currentStoreId = getCurrentStoreId()
      const q = query(
        collection(db, 'stores', currentStoreId, 'orders'),
        orderBy('createdAt', 'desc')
      )
      
      const querySnapshot = await getDocs(q)
      const orders: Order[] = []
      
      querySnapshot.forEach((doc) => {
        const data = doc.data()
        orders.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
          updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt
        } as Order)
      })
      
      set({ orders, loading: false })
    } catch (error: any) {
      console.error('Error fetching orders:', error)
      set({ error: error.message, loading: false })
    }
  },

  addOrder: async (orderData) => {
    set({ error: null })
    try {
      const currentStoreId = getCurrentStoreId()
      const newOrder = {
        ...orderData,
        storeId: currentStoreId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      }

      const docRef = await addDoc(collection(db, 'stores', currentStoreId, 'orders'), newOrder)

      // Add to local state immediately for optimistic updates
      const optimisticOrder: Order = {
        ...orderData,
        id: docRef.id,
        storeId: currentStoreId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      set(state => ({
        orders: [optimisticOrder, ...state.orders]
      }))
      
      return docRef.id
    } catch (error: any) {
      console.error('Error adding order:', error)
      set({ error: error.message })
      throw error
    }
  },

  updateOrder: async (id, updates) => {
    set({ error: null })
    try {
      const updateData = {
        ...updates,
        updatedAt: serverTimestamp()
      }
      
      await updateDoc(doc(db, 'orders', id), updateData)
      
      // Update local state immediately for optimistic updates
      set(state => ({
        orders: state.orders.map(order =>
          order.id === id
            ? { ...order, ...updates, updatedAt: new Date().toISOString() }
            : order
        )
      }))
    } catch (error: any) {
      console.error('Error updating order:', error)
      set({ error: error.message })
      throw error
    }
  },

  updateOrderStatus: async (id, status) => {
    await get().updateOrder(id, { status })
  },

  updatePaymentStatus: async (id, paymentStatus) => {
    await get().updateOrder(id, { paymentStatus })
  },

  updateFulfillmentStatus: async (id, fulfillmentStatus) => {
    await get().updateOrder(id, { fulfillmentStatus })
  },

  getOrder: async (id) => {
    try {
      const docSnap = await getDoc(doc(db, 'orders', id))
      
      if (docSnap.exists()) {
        const data = docSnap.data()
        return {
          id: docSnap.id,
          ...data,
          createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
          updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt
        } as Order
      }
      
      return null
    } catch (error: any) {
      console.error('Error getting order:', error)
      set({ error: error.message })
      return null
    }
  },

  subscribeToOrders: () => {
    const currentStoreId = getCurrentStoreId()
    const q = query(
      collection(db, 'stores', currentStoreId, 'orders'),
      orderBy('createdAt', 'desc')
    )
    
    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const orders: Order[] = []
      
      querySnapshot.forEach((doc) => {
        const data = doc.data()
        orders.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
          updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt
        } as Order)
      })
      
      set({ orders, loading: false })
    }, (error) => {
      console.error('Error in orders subscription:', error)
      set({ error: error.message, loading: false })
    })
    
    return unsubscribe
  }
}))

// Auto-fetch orders on store creation
useOrdersStore.getState().fetchOrders()
