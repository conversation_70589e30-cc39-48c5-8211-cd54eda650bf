import { Suspense, lazy, useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Toaster } from 'react-hot-toast'
import { Layout } from './components/layout/layout'
import { ProtectedRoute } from './components/auth/protected-route'
import { PageLoading } from './components/ui/loading'

import { ThemeProvider } from './components/theme/theme-provider'
import { ErrorBoundary } from './components/error-boundary'
import { isFirebaseConfigured, isUsingEmulator } from './lib/firebase'
import { initializeCurrencySettings } from './lib/currency'
import { appAdminService } from './lib/app-admin-service'
import { LoginPage } from './pages/login'
import { DashboardPage } from './pages/dashboard'
import { SetupPage } from './pages/setup'
import { StoreRegistrationPage } from './pages/store-registration'
import { AppAdminDashboard } from './pages/app-admin-dashboard'
import { FirebaseCleanupPage } from './pages/firebase-cleanup'
import { QuickCleanupPage } from './pages/quick-cleanup'
import { useStoreManagement } from './store/store-management'
import { useProductsStore } from './store/products-store'
import { useCategoriesStore } from './store/categories-store'
import { useOrdersStore } from './store/orders-store'

// Lazy load pages for better performance
const ProductsPage = lazy(() => import('./pages/products').then(module => ({ default: module.ProductsPage })))
const AddProductPage = lazy(() => import('./pages/add-product').then(module => ({ default: module.AddProductPage })))
const ProductDetailPage = lazy(() => import('./pages/product-detail').then(module => ({ default: module.ProductDetailPage })))
const EditProductPage = lazy(() => import('./pages/edit-product').then(module => ({ default: module.EditProductPage })))
const CategoriesPage = lazy(() => import('./pages/categories').then(module => ({ default: module.CategoriesPage })))
const OrdersPage = lazy(() => import('./pages/orders').then(module => ({ default: module.OrdersPage })))
const OrderDetailPage = lazy(() => import('./pages/order-detail').then(module => ({ default: module.OrderDetailPage })))
const ReturnsRefundsPage = lazy(() => import('./pages/returns-refunds').then(module => ({ default: module.ReturnsRefundsPage })))
const CustomersPage = lazy(() => import('./pages/customers'))
const CustomerDetailPage = lazy(() => import('./pages/customer-detail'))
const CustomerFormPage = lazy(() => import('./pages/customer-form'))
const CustomerGroupsPage = lazy(() => import('./pages/customer-groups'))
const CustomerGroupFormPage = lazy(() => import('./pages/customer-group-form'))
const CustomerInsightsPage = lazy(() => import('./pages/customer-insights'))
const InventoryPage = lazy(() => import('./pages/inventory'))
const InventoryDetailPage = lazy(() => import('./pages/inventory-detail'))
const InventoryAdjustPage = lazy(() => import('./pages/inventory-adjust'))
const InventoryBulkAdjustPage = lazy(() => import('./pages/inventory-bulk-adjust'))
const InventoryReportsPage = lazy(() => import('./pages/inventory-reports'))
const UsersPage = lazy(() => import('./pages/users').then(module => ({ default: module.UsersPage })))
const AnalyticsPage = lazy(() => import('./pages/analytics').then(module => ({ default: module.AnalyticsPage })))
const MarketingPage = lazy(() => import('./pages/marketing'))
const CouponFormPage = lazy(() => import('./pages/marketing/coupon-form'))
const PromotionFormPage = lazy(() => import('./pages/marketing/promotion-form'))
// Settings pages
const GeneralSettingsPage = lazy(() => import('./pages/settings/general').then(module => ({ default: module.GeneralSettingsPage })))
const AppearanceSettingsPage = lazy(() => import('./pages/settings/appearance').then(module => ({ default: module.AppearanceSettingsPage })))
const CommerceSettingsPage = lazy(() => import('./pages/settings/commerce').then(module => ({ default: module.CommerceSettingsPage })))
const AdvancedSettingsPage = lazy(() => import('./pages/settings/advanced').then(module => ({ default: module.AdvancedSettingsPage })))
const ProfilePage = lazy(() => import('./pages/profile').then(module => ({ default: module.ProfilePage })))
const RolesPermissionsPage = lazy(() => import('./pages/roles-permissions').then(module => ({ default: module.RolesPermissionsPage })))
const ActivityLogsPage = lazy(() => import('./pages/activity-logs').then(module => ({ default: module.ActivityLogsPage })))

// Support Pages
const KnowledgeBasePage = lazy(() => import('./pages/support/knowledge-base'))
const SupportChatPage = lazy(() => import('./pages/support/support-chat'))
const SubmitTicketPage = lazy(() => import('./pages/support/submit-ticket'))

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    },
  },
})

function App() {
  const [isInitialized, setIsInitialized] = useState(false)
  const { currentStoreId, fetchCurrentStore } = useStoreManagement()
  const { subscribeToProducts } = useProductsStore()
  const { subscribeToCategories } = useCategoriesStore()
  const { subscribeToOrders } = useOrdersStore()

  // Initialize Firebase integration and currency settings on app load
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize currency settings
        initializeCurrencySettings()

        // Log Firebase configuration status
        if (!isFirebaseConfigured) {
          console.log('🔧 Firebase not configured, using emulator/mock mode for development')
        } else if (isUsingEmulator) {
          console.log('🔧 Using Firebase emulator mode for development')
        } else {
          console.log('🔥 Firebase configured for production mode')
        }

        // Log Firebase status
        console.log('🔥 Firebase integration ready')

        // Load current store
        fetchCurrentStore()

        setIsInitialized(true)
      } catch (error) {
        console.error('App initialization failed:', error)
        setIsInitialized(true) // Continue anyway
      }
    }

    initializeApp()
  }, [])

  // Initialize real-time subscriptions when store is loaded
  useEffect(() => {
    if (currentStoreId && isInitialized) {
      console.log('🚀 Initializing real-time subscriptions for store:', currentStoreId)

      // Subscribe to real-time updates
      const unsubscribeProducts = subscribeToProducts()
      const unsubscribeCategories = subscribeToCategories()
      const unsubscribeOrders = subscribeToOrders()

      return () => {
        unsubscribeProducts()
        unsubscribeCategories()
        unsubscribeOrders()
      }
    }
  }, [currentStoreId, isInitialized, subscribeToProducts, subscribeToCategories, subscribeToOrders])

  // Show loading screen while initializing
  if (!isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Initializing Womanza Admin Panel...</p>
          <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
            {isFirebaseConfigured ? 'Connecting to Firebase...' : 'Starting in development mode...'}
          </p>
        </div>
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <Router>
          <Routes>
          <Route path="/setup" element={<SetupPage />} />
          <Route path="/register" element={<StoreRegistrationPage />} />
          <Route path="/app-admin" element={<AppAdminDashboard />} />
          <Route path="/firebase-cleanup" element={<FirebaseCleanupPage />} />
          <Route path="/quick-cleanup" element={<QuickCleanupPage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route
            path="/admin"
            element={
              <ProtectedRoute>
                <Layout />
              </ProtectedRoute>
            }
          >
            <Route path="dashboard" element={<DashboardPage />} />
            {/* App admin route temporarily disabled */}
            <Route path="products" element={
              <Suspense fallback={<PageLoading />}>
                <ProductsPage />
              </Suspense>
            } />
            <Route path="add-product" element={
              <Suspense fallback={<PageLoading />}>
                <AddProductPage />
              </Suspense>
            } />
            <Route path="products/:id" element={
              <Suspense fallback={<PageLoading />}>
                <ProductDetailPage />
              </Suspense>
            } />
            <Route path="products/:id/edit" element={
              <Suspense fallback={<PageLoading />}>
                <EditProductPage />
              </Suspense>
            } />
            <Route path="categories" element={
              <Suspense fallback={<PageLoading />}>
                <CategoriesPage />
              </Suspense>
            } />
            <Route path="orders" element={
              <Suspense fallback={<PageLoading />}>
                <OrdersPage />
              </Suspense>
            } />
            <Route path="orders/detail" element={
              <Suspense fallback={<PageLoading />}>
                <OrderDetailPage />
              </Suspense>
            } />
            <Route path="orders/returns" element={
              <Suspense fallback={<PageLoading />}>
                <ReturnsRefundsPage />
              </Suspense>
            } />
            <Route path="customers" element={
              <Suspense fallback={<PageLoading />}>
                <CustomersPage />
              </Suspense>
            } />
            <Route path="customers/groups" element={
              <Suspense fallback={<PageLoading />}>
                <CustomerGroupsPage />
              </Suspense>
            } />
            <Route path="customers/groups/new" element={
              <Suspense fallback={<PageLoading />}>
                <CustomerGroupFormPage />
              </Suspense>
            } />
            <Route path="customers/groups/:groupId/edit" element={
              <Suspense fallback={<PageLoading />}>
                <CustomerGroupFormPage />
              </Suspense>
            } />
            <Route path="customers/insights" element={
              <Suspense fallback={<PageLoading />}>
                <CustomerInsightsPage />
              </Suspense>
            } />
            <Route path="customers/new" element={
              <Suspense fallback={<PageLoading />}>
                <CustomerFormPage />
              </Suspense>
            } />
            <Route path="customers/:id/edit" element={
              <Suspense fallback={<PageLoading />}>
                <CustomerFormPage />
              </Suspense>
            } />
            <Route path="customers/:id" element={
              <Suspense fallback={<PageLoading />}>
                <CustomerDetailPage />
              </Suspense>
            } />
            <Route path="inventory" element={
              <Suspense fallback={<PageLoading />}>
                <InventoryPage />
              </Suspense>
            } />
            {/* More specific routes must come before generic :productId route */}
            <Route path="inventory/bulk-adjust" element={
              <Suspense fallback={<PageLoading />}>
                <InventoryBulkAdjustPage />
              </Suspense>
            } />
            <Route path="inventory/reports" element={
              <Suspense fallback={<PageLoading />}>
                <InventoryReportsPage />
              </Suspense>
            } />
            <Route path="inventory/:productId/adjust" element={
              <Suspense fallback={<PageLoading />}>
                <InventoryAdjustPage />
              </Suspense>
            } />
            <Route path="inventory/:productId/:variantId" element={
              <Suspense fallback={<PageLoading />}>
                <InventoryDetailPage />
              </Suspense>
            } />
            <Route path="inventory/:productId" element={
              <Suspense fallback={<PageLoading />}>
                <InventoryDetailPage />
              </Suspense>
            } />
            <Route path="analytics" element={
              <Suspense fallback={<PageLoading />}>
                <AnalyticsPage />
              </Suspense>
            } />
            <Route
              path="marketing"
              element={
                <ProtectedRoute requiredRoles={['super_admin', 'admin']}>
                  <Suspense fallback={<PageLoading />}>
                    <MarketingPage />
                  </Suspense>
                </ProtectedRoute>
              }
            />
            <Route
              path="marketing/coupons/create"
              element={
                <ProtectedRoute requiredRoles={['super_admin', 'admin', 'editor']}>
                  <Suspense fallback={<PageLoading />}>
                    <CouponFormPage />
                  </Suspense>
                </ProtectedRoute>
              }
            />
            <Route
              path="marketing/promotions/create"
              element={
                <ProtectedRoute requiredRoles={['super_admin', 'admin', 'editor']}>
                  <Suspense fallback={<PageLoading />}>
                    <PromotionFormPage />
                  </Suspense>
                </ProtectedRoute>
              }
            />
            <Route
              path="users"
              element={
                <ProtectedRoute requiredRoles={['super_admin', 'admin']}>
                  <Suspense fallback={<PageLoading />}>
                    <UsersPage />
                  </Suspense>
                </ProtectedRoute>
              }
            />

            <Route
              path="roles-permissions"
              element={
                <ProtectedRoute requiredRoles={['super_admin']}>
                  <Suspense fallback={<PageLoading />}>
                    <RolesPermissionsPage />
                  </Suspense>
                </ProtectedRoute>
              }
            />

            <Route
              path="activity-logs"
              element={
                <ProtectedRoute requiredRoles={['super_admin', 'admin']}>
                  <Suspense fallback={<PageLoading />}>
                    <ActivityLogsPage />
                  </Suspense>
                </ProtectedRoute>
              }
            />

            <Route path="settings/general" element={
              <ProtectedRoute requiredRoles={['super_admin', 'admin']}>
                <Suspense fallback={<PageLoading />}>
                  <GeneralSettingsPage />
                </Suspense>
              </ProtectedRoute>
            } />
            <Route path="settings/appearance" element={
              <ProtectedRoute requiredRoles={['super_admin', 'admin']}>
                <Suspense fallback={<PageLoading />}>
                  <AppearanceSettingsPage />
                </Suspense>
              </ProtectedRoute>
            } />
            <Route path="settings/commerce" element={
              <ProtectedRoute requiredRoles={['super_admin', 'admin']}>
                <Suspense fallback={<PageLoading />}>
                  <CommerceSettingsPage />
                </Suspense>
              </ProtectedRoute>
            } />
            <Route path="settings/advanced" element={
              <ProtectedRoute requiredRoles={['super_admin', 'admin']}>
                <Suspense fallback={<PageLoading />}>
                  <AdvancedSettingsPage />
                </Suspense>
              </ProtectedRoute>
            } />
            <Route path="settings" element={<Navigate to="/admin/settings/general" replace />} />
            <Route
              path="profile"
              element={
                <Suspense fallback={<PageLoading />}>
                  <ProfilePage />
                </Suspense>
              }
            />

            {/* Support Routes */}
            <Route
              path="support/knowledge-base"
              element={
                <Suspense fallback={<PageLoading />}>
                  <KnowledgeBasePage />
                </Suspense>
              }
            />
            <Route
              path="support/chat"
              element={
                <Suspense fallback={<PageLoading />}>
                  <SupportChatPage />
                </Suspense>
              }
            />
            <Route
              path="support/ticket"
              element={
                <Suspense fallback={<PageLoading />}>
                  <SubmitTicketPage />
                </Suspense>
              }
            />
          </Route>
          <Route path="/" element={<Navigate to="/admin/dashboard" replace />} />
        </Routes>
        </Router>
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#ffffff',
              color: '#1f2937',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
              fontSize: '14px',
              fontWeight: '500',
            },
            success: {
              style: {
                background: '#f0fdf4',
                color: '#166534',
                border: '1px solid #bbf7d0',
              },
              iconTheme: {
                primary: '#16a34a',
                secondary: '#ffffff',
              },
            },
            error: {
              style: {
                background: '#fef2f2',
                color: '#991b1b',
                border: '1px solid #fecaca',
              },
              iconTheme: {
                primary: '#dc2626',
                secondary: '#ffffff',
              },
            },
          }}
        />
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  )
}

export default App
