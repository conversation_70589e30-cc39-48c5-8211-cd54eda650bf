import { useState, useEffect, useCallback } from 'react'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { userService, type CreateUserData, type UpdateUserData } from '../lib/user-service'
import { type AdminUser } from '../lib/firebase'

// Role hierarchy for permission checking
const ROLE_HIERARCHY = {
  super_admin: 4,
  admin: 3,
  editor: 2,
  viewer: 1
}

export interface CreateUserForm {
  email: string
  fullName: string
  password: string
  role: 'admin' | 'editor' | 'viewer'
}

export interface UpdateUserForm {
  fullName?: string
  role?: 'admin' | 'editor' | 'viewer'
  active?: boolean
}

export function useUserManagement() {
  const { user: currentUser } = useFirebaseAuthStore()
  const [users, setUsers] = useState<AdminUser[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Permission checks using role hierarchy
  const canManageUsers = currentUser?.role === 'super_admin' || currentUser?.role === 'admin'
  const canCreateUsers = canManageUsers

  const canEditUser = useCallback((targetUser: AdminUser) => {
    if (!currentUser) return false
    return userService.canManageUser(currentUser, targetUser)
  }, [currentUser])

  const canDeleteUser = useCallback((targetUser: AdminUser) => {
    if (!currentUser) return false
    // Only super_admin can delete users, and cannot delete other super_admins
    return currentUser.role === 'super_admin' && targetUser.role !== 'super_admin'
  }, [currentUser?.role])

  const canAssignRole = useCallback((role: AdminUser['role']) => {
    if (!currentUser) return false
    return userService.canAssignRole(currentUser, role)
  }, [currentUser])

  // Load users from database
  const loadUsers = useCallback(async () => {
    const userStoreId = currentUser?.storeId || currentUser?.primaryStoreId
    const hasStoreAccess = userStoreId || (currentUser?.storeIds && currentUser.storeIds.length > 0)

    if (!hasStoreAccess || !canManageUsers) return

    setLoading(true)
    setError(null)

    try {
      const targetStoreId = userStoreId || (currentUser?.storeIds && currentUser.storeIds[0])
      const fetchedUsers = await userService.getUsers(targetStoreId)
      setUsers(fetchedUsers)
    } catch (err: any) {
      console.error('Error loading users:', err)
      setError(err.message || 'Failed to load users')
    } finally {
      setLoading(false)
    }
  }, [currentUser?.storeId, canManageUsers])

  // Create new user
  const createUser = useCallback(async (userData: CreateUserForm) => {
    // Check if user has permission and store access
    const userStoreId = currentUser?.storeId || currentUser?.primaryStoreId
    const hasStoreAccess = userStoreId || (currentUser?.storeIds && currentUser.storeIds.length > 0)

    if (!hasStoreAccess || !canCreateUsers) {
      throw new Error('Permission denied')
    }

    // Check if current user can assign this role
    if (!canAssignRole(userData.role)) {
      throw new Error('Permission denied: Cannot assign this role')
    }

    try {
      setError(null)

      const createRequest: CreateUserData = {
        email: userData.email,
        fullName: userData.fullName,
        password: userData.password,
        role: userData.role
      }

      // Determine which store to create the user in
      const targetStoreId = userStoreId || (currentUser?.storeIds && currentUser.storeIds[0])

      const newUser = await userService.createUser(createRequest, currentUser.id, targetStoreId)

      // Refresh users list
      await loadUsers()
      return { id: newUser.id }

    } catch (error: any) {
      console.error('Error creating user:', error)
      setError(error.message || 'Failed to create user')
      throw new Error(error.message || 'Failed to create user')
    }
  }, [currentUser, canCreateUsers, canAssignRole, loadUsers])

  // Update user
  const updateUser = useCallback(async (userId: string, updates: UpdateUserForm) => {
    const targetUser = users?.find(u => u.id === userId)
    if (!targetUser || !canEditUser(targetUser)) {
      throw new Error('Permission denied')
    }

    // Check if current user can assign the new role (if role is being updated)
    if (updates.role && !canAssignRole(updates.role)) {
      throw new Error('Permission denied: Cannot assign this role')
    }

    try {
      setError(null)

      const updateRequest: UpdateUserData = {
        fullName: updates.fullName,
        role: updates.role,
        active: updates.active
      }

      const userStoreId = currentUser?.storeId || currentUser?.primaryStoreId
      const targetStoreId = userStoreId || (currentUser?.storeIds && currentUser.storeIds[0])

      await userService.updateUser(userId, updateRequest, targetStoreId)

      // Refresh users list
      await loadUsers()

    } catch (error: any) {
      console.error('Error updating user:', error)
      setError(error.message || 'Failed to update user')
      throw new Error(error.message || 'Failed to update user')
    }
  }, [users, canEditUser, canAssignRole, loadUsers])

  // Toggle user status
  const toggleUserStatus = useCallback(async (userId: string) => {
    const targetUser = users?.find(u => u.id === userId)
    if (!targetUser || !canEditUser(targetUser)) {
      throw new Error('Permission denied')
    }

    await updateUser(userId, { active: !targetUser.active })
  }, [users, canEditUser, updateUser])

  // Delete user
  const deleteUser = useCallback(async (userId: string) => {
    const targetUser = users?.find(u => u.id === userId)
    if (!targetUser || !canDeleteUser(targetUser)) {
      throw new Error('Permission denied')
    }

    try {
      setError(null)

      const userStoreId = currentUser?.storeId || currentUser?.primaryStoreId
      const targetStoreId = userStoreId || (currentUser?.storeIds && currentUser.storeIds[0])

      await userService.deleteUser(userId, targetStoreId)

      // Refresh users list
      await loadUsers()

    } catch (error: any) {
      console.error('Error deleting user:', error)
      setError(error.message || 'Failed to delete user')
      throw new Error(error.message || 'Failed to delete user')
    }
  }, [users, canDeleteUser, loadUsers])

  // Send password reset email
  const sendPasswordReset = useCallback(async (email: string) => {
    try {
      setError(null)
      await userService.sendPasswordReset(email)
    } catch (error: any) {
      console.error('Error sending password reset:', error)
      setError(error.message || 'Failed to send password reset email')
      throw new Error(error.message || 'Failed to send password reset email')
    }
  }, [])

  // Load initial data
  useEffect(() => {
    const userStoreId = currentUser?.storeId || currentUser?.primaryStoreId
    const hasStoreAccess = userStoreId || (currentUser?.storeIds && currentUser.storeIds.length > 0)

    if (!hasStoreAccess || !canManageUsers) return
    loadUsers()
  }, [currentUser?.storeId, currentUser?.primaryStoreId, currentUser?.storeIds, canManageUsers, loadUsers])

  return {
    users,
    loading,
    error,
    canManageUsers,
    canCreateUsers,
    canEditUser,
    canDeleteUser,
    canAssignRole,
    createUser,
    updateUser,
    toggleUserStatus,
    deleteUser,
    sendPasswordReset,
    refetch: loadUsers
  }
}
