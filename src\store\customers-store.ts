import { create } from 'zustand'
import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  serverTimestamp
} from 'firebase/firestore'
import { db } from '../lib/firebase'
import { useStoreManagement } from './store-management'
import { useFirebaseAuthStore } from './firebase-auth'

// Store ID for filtering data (fallback)
const FALLBACK_STORE_ID = import.meta.env.VITE_STORE_ID || 'Womanza'

// Helper function to get current store ID
const getCurrentStoreId = () => {
  const { currentStoreId } = useStoreManagement.getState()
  const { user } = useFirebaseAuthStore.getState()
  return currentStoreId || user?.primaryStoreId || user?.storeId || FALLBACK_STORE_ID
}

export interface CustomerAddress {
  id: string
  firstName: string
  lastName: string
  company?: string
  address1: string
  address2?: string
  city: string
  province: string
  country: string
  zip: string
  phone?: string
  isDefault: boolean
}

export interface Customer {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  dateOfBirth?: string
  gender?: 'male' | 'female' | 'other'
  addresses: CustomerAddress[]
  defaultAddress?: CustomerAddress
  tags: string[]
  notes?: string
  acceptsMarketing: boolean
  totalSpent: number
  ordersCount: number
  averageOrderValue: number
  lastOrderDate?: string
  customerGroup?: string
  status: 'active' | 'disabled'
  storeId: string
  createdAt: string
  updatedAt: string
  createdBy: string
}

interface CustomersState {
  customers: Customer[]
  loading: boolean
  error: string | null
  // Actions
  fetchCustomers: () => Promise<void>
  addCustomer: (customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt' | 'totalSpent' | 'ordersCount' | 'averageOrderValue'>) => Promise<string>
  updateCustomer: (id: string, updates: Partial<Customer>) => Promise<void>
  deleteCustomer: (id: string) => Promise<void>
  getCustomer: (id: string) => Promise<Customer | null>
  addCustomerAddress: (customerId: string, address: Omit<CustomerAddress, 'id'>) => Promise<void>
  updateCustomerAddress: (customerId: string, addressId: string, updates: Partial<CustomerAddress>) => Promise<void>
  deleteCustomerAddress: (customerId: string, addressId: string) => Promise<void>
  setDefaultAddress: (customerId: string, addressId: string) => Promise<void>
  // Real-time subscription
  subscribeToCustomers: () => () => void
  clearError: () => void
}

export const useCustomersStore = create<CustomersState>((set, get) => ({
  customers: [],
  loading: false,
  error: null,

  clearError: () => set({ error: null }),

  fetchCustomers: async () => {
    set({ loading: true, error: null })

    const currentStoreId = getCurrentStoreId()
    if (!currentStoreId) {
      set({ error: 'No store context available', loading: false })
      return
    }

    try {
      // Try store-specific customers first
      let q = query(
        collection(db, 'stores', currentStoreId, 'customers'),
        orderBy('createdAt', 'desc')
      )

      let querySnapshot = await getDocs(q)

      // If no customers in store subcollection, try global customers collection
      if (querySnapshot.empty) {
        console.log('🔍 No customers in store subcollection, trying global collection...')
        q = query(
          collection(db, 'customers'),
          where('storeId', '==', currentStoreId),
          orderBy('createdAt', 'desc')
        )
        querySnapshot = await getDocs(q)
      }

      const customers: Customer[] = []

      querySnapshot.forEach((doc) => {
        const data = doc.data()
        customers.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
          updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt
        } as Customer)
      })

      set({ customers, loading: false })
      console.log(`✅ Fetched ${customers.length} customers for store: ${currentStoreId}`)
    } catch (error: any) {
      console.error('Error fetching customers:', error)
      set({ error: error.message, loading: false })
    }
  },

  addCustomer: async (customerData) => {
    set({ error: null })

    const currentStoreId = getCurrentStoreId()
    if (!currentStoreId) {
      set({ error: 'No store context available' })
      throw new Error('No store context available')
    }

    try {
      const newCustomer = {
        ...customerData,
        totalSpent: 0,
        ordersCount: 0,
        averageOrderValue: 0,
        storeId: currentStoreId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      }

      // Add to store-specific customers collection
      const docRef = await addDoc(collection(db, 'stores', currentStoreId, 'customers'), newCustomer)

      // Add to local state immediately for optimistic updates
      const optimisticCustomer: Customer = {
        ...customerData,
        id: docRef.id,
        totalSpent: 0,
        ordersCount: 0,
        averageOrderValue: 0,
        storeId: currentStoreId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      set(state => ({
        customers: [optimisticCustomer, ...state.customers]
      }))

      console.log(`✅ Added customer to store: ${currentStoreId}`)
      return docRef.id
    } catch (error: any) {
      console.error('Error adding customer:', error)
      set({ error: error.message })
      throw error
    }
  },

  updateCustomer: async (id, updates) => {
    set({ error: null })
    try {
      const updateData = {
        ...updates,
        updatedAt: serverTimestamp()
      }
      
      await updateDoc(doc(db, 'customers', id), updateData)
      
      // Update local state immediately for optimistic updates
      set(state => ({
        customers: state.customers.map(customer =>
          customer.id === id
            ? { ...customer, ...updates, updatedAt: new Date().toISOString() }
            : customer
        )
      }))
    } catch (error: any) {
      console.error('Error updating customer:', error)
      set({ error: error.message })
      throw error
    }
  },

  deleteCustomer: async (id) => {
    set({ error: null })
    try {
      await deleteDoc(doc(db, 'customers', id))
      
      // Remove from local state immediately
      set(state => ({
        customers: state.customers.filter(customer => customer.id !== id)
      }))
    } catch (error: any) {
      console.error('Error deleting customer:', error)
      set({ error: error.message })
      throw error
    }
  },

  getCustomer: async (id) => {
    try {
      const docSnap = await getDoc(doc(db, 'customers', id))
      
      if (docSnap.exists()) {
        const data = docSnap.data()
        return {
          id: docSnap.id,
          ...data,
          createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
          updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt
        } as Customer
      }
      
      return null
    } catch (error: any) {
      console.error('Error getting customer:', error)
      set({ error: error.message })
      return null
    }
  },

  addCustomerAddress: async (customerId, addressData) => {
    set({ error: null })
    try {
      const customer = await get().getCustomer(customerId)
      if (!customer) throw new Error('Customer not found')
      
      const newAddress: CustomerAddress = {
        ...addressData,
        id: `addr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      }
      
      const updatedAddresses = [...customer.addresses, newAddress]
      await get().updateCustomer(customerId, { addresses: updatedAddresses })
    } catch (error: any) {
      console.error('Error adding customer address:', error)
      set({ error: error.message })
      throw error
    }
  },

  updateCustomerAddress: async (customerId, addressId, updates) => {
    set({ error: null })
    try {
      const customer = await get().getCustomer(customerId)
      if (!customer) throw new Error('Customer not found')
      
      const updatedAddresses = customer.addresses.map(addr =>
        addr.id === addressId ? { ...addr, ...updates } : addr
      )
      
      await get().updateCustomer(customerId, { addresses: updatedAddresses })
    } catch (error: any) {
      console.error('Error updating customer address:', error)
      set({ error: error.message })
      throw error
    }
  },

  deleteCustomerAddress: async (customerId, addressId) => {
    set({ error: null })
    try {
      const customer = await get().getCustomer(customerId)
      if (!customer) throw new Error('Customer not found')
      
      const updatedAddresses = customer.addresses.filter(addr => addr.id !== addressId)
      await get().updateCustomer(customerId, { addresses: updatedAddresses })
    } catch (error: any) {
      console.error('Error deleting customer address:', error)
      set({ error: error.message })
      throw error
    }
  },

  setDefaultAddress: async (customerId, addressId) => {
    set({ error: null })
    try {
      const customer = await get().getCustomer(customerId)
      if (!customer) throw new Error('Customer not found')
      
      const updatedAddresses = customer.addresses.map(addr => ({
        ...addr,
        isDefault: addr.id === addressId
      }))
      
      const defaultAddress = updatedAddresses?.find(addr => addr.id === addressId)
      
      await get().updateCustomer(customerId, { 
        addresses: updatedAddresses,
        defaultAddress
      })
    } catch (error: any) {
      console.error('Error setting default address:', error)
      set({ error: error.message })
      throw error
    }
  },

  subscribeToCustomers: () => {
    const currentStoreId = getCurrentStoreId()
    if (!currentStoreId) {
      console.warn('No store context available for customers subscription')
      return () => {}
    }

    // Subscribe to store-specific customers collection
    const q = query(
      collection(db, 'stores', currentStoreId, 'customers'),
      orderBy('createdAt', 'desc')
    )

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const customers: Customer[] = []

      querySnapshot.forEach((doc) => {
        const data = doc.data()
        customers.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
          updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt
        } as Customer)
      })

      set({ customers, loading: false, error: null })
      console.log(`🔄 Customers updated in real-time for store: ${currentStoreId}`)
    }, (error) => {
      console.error('❌ Error in customers subscription:', error)
      set({ error: error.message, loading: false })
    })

    return unsubscribe
  }
}))

// Auto-fetch customers on store creation
useCustomersStore.getState().fetchCustomers()
