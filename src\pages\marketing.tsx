import React, { useState, useEffect } from 'react'
import { useNavigate, Link } from 'react-router-dom'
import { Button } from '../components/ui/button'
import { Badge } from '../components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { DataTable, Column } from '../components/ui/data-table'
import { MarketingService, type Coupon, type Promotion, type Banner, type MarketingInsights } from '../lib/marketing-service'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { formatCurrency, formatDateTime } from '../lib/utils'
import { toast } from 'react-hot-toast'
import {
  Plus,
  Tag,
  Megaphone,
  Image,
  Mail,
  BarChart3,
  Eye,
  Users,
  DollarSign,
  TrendingUp,
  Calendar,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Edit,
  Trash2,
  Gift,
  Percent,
  Target,
  ArrowRight,
  RefreshCw,
  Download,
  Copy,
  Play,
  Pause,
  Clock
} from 'lucide-react'

export default function MarketingPage() {
  const navigate = useNavigate()
  const { user, hasPermission } = useFirebaseAuthStore()
  const [coupons, setCoupons] = useState<Coupon[]>([])
  const [promotions, setPromotions] = useState<Promotion[]>([])
  const [banners, setBanners] = useState<Banner[]>([])
  const [insights, setInsights] = useState<MarketingInsights | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'coupons' | 'promotions' | 'banners' | 'campaigns'>('overview')

  // Initialize service
  const marketingService = new MarketingService('womanza-jewelry-store')

  // Permission checks
  const canEdit = hasPermission(['super_admin', 'admin', 'editor'])
  const canDelete = hasPermission(['super_admin', 'admin'])

  useEffect(() => {
    if (user) {
      setupRealtimeListeners()
    }
    return () => {
      marketingService.cleanup()
    }
  }, [])

  const setupRealtimeListeners = () => {
    setLoading(true)

    // Set up real-time listeners
    marketingService.onCouponsChange((couponsData) => {
      setCoupons(couponsData)
      updateInsights(couponsData, promotions, banners)
    })

    marketingService.onPromotionsChange((promotionsData) => {
      setPromotions(promotionsData)
      updateInsights(coupons, promotionsData, banners)
    })

    marketingService.onBannersChange((bannersData) => {
      setBanners(bannersData)
      updateInsights(coupons, promotions, bannersData)
    })

    setLoading(false)
  }

  const updateInsights = (couponsData: Coupon[], promotionsData: Promotion[], bannersData: Banner[]) => {
    const activeCoupons = couponsData.filter(c => c.active)
    const activePromotions = promotionsData.filter(p => p.active)
    const activeBanners = bannersData.filter(b => b.active)

    // Calculate usage rate
    const totalCouponUsage = couponsData.reduce((sum, c) => sum + c.usedCount, 0)
    const totalCouponLimit = couponsData.reduce((sum, c) => sum + c.usageLimit, 0)
    const couponUsageRate = totalCouponLimit > 0 ? (totalCouponUsage / totalCouponLimit) * 100 : 0

    const newInsights: MarketingInsights = {
      totalCoupons: couponsData.length,
      activeCoupons: activeCoupons.length,
      totalPromotions: promotionsData.length,
      activePromotions: activePromotions.length,
      totalBanners: bannersData.length,
      activeBanners: activeBanners.length,
      totalCampaigns: 0,
      couponUsageRate,
      promotionRevenue: 125000, // TODO: Calculate from orders
      topPerformingCoupons: couponsData
        .sort((a, b) => b.usedCount - a.usedCount)
        .slice(0, 5)
        .map(c => ({
          id: c.id,
          code: c.code,
          usageCount: c.usedCount,
          revenue: 0 // TODO: Calculate from orders
        })),
      topPerformingPromotions: promotionsData
        .slice(0, 5)
        .map(p => ({
          id: p.id,
          title: p.title,
          revenue: 0, // TODO: Calculate from orders
          orderCount: 0 // TODO: Calculate from orders
        })),
      bannerClickStats: bannersData
        .map(b => ({
          id: b.id,
          title: b.title,
          clicks: b.clickCount,
          impressions: b.impressionCount,
          ctr: b.impressionCount > 0 ? (b.clickCount / b.impressionCount) * 100 : 0
        }))
    }

    setInsights(newInsights)
  }

  const loadData = async () => {
    try {
      setLoading(true)
      const [couponsData, promotionsData, bannersData, insightsData] = await Promise.all([
        marketingService.getCoupons(),
        marketingService.getPromotions(),
        marketingService.getBanners(),
        marketingService.getMarketingInsights()
      ])
      setCoupons(couponsData)
      setPromotions(promotionsData)
      setBanners(bannersData)
      setInsights(insightsData)
    } catch (error) {
      console.error('Error loading marketing data:', error)
      toast.error('Failed to load marketing data', {
        style: {
          background: '#EF4444',
          color: 'white',
        }
      })
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-600"></div>
      </div>
    )
  }

  return (
    <div className="page-container">
      {/* Header */}
      <div className="page-header flex items-center justify-between">
        <div>
          <h1 className="page-title flex items-center gap-2">
            <Target className="h-6 w-6 text-purple-600" />
            Marketing Management
          </h1>
          <p className="page-subtitle">Manage promotions, coupons, banners, and campaigns</p>
        </div>
        {canEdit && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => {setLoading(true); loadData()}}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            {activeTab === 'coupons' && (
              <Button onClick={() => navigate('/admin/marketing/coupons/create')}>
                <Plus className="h-4 w-4 mr-2" />
                New Coupon
              </Button>
            )}
            {activeTab === 'promotions' && (
              <Button onClick={() => navigate('/admin/marketing/promotions/create')}>
                <Plus className="h-4 w-4 mr-2" />
                New Promotion
              </Button>
            )}
            {activeTab === 'banners' && (
              <Button onClick={() => navigate('/admin/marketing/banners/create')}>
                <Plus className="h-4 w-4 mr-2" />
                Upload Banner
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3, color: 'text-blue-600' },
            { id: 'coupons', label: 'Coupons', icon: Gift, color: 'text-green-600' },
            { id: 'promotions', label: 'Promotions', icon: Percent, color: 'text-purple-600' },
            { id: 'banners', label: 'Banners', icon: Image, color: 'text-orange-600' },
            { id: 'campaigns', label: 'Email Campaigns', icon: Mail, color: 'text-indigo-600' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className={`h-4 w-4 mr-2 ${activeTab === tab.id ? 'text-purple-600' : tab.color}`} />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <OverviewTab insights={insights} />
      )}

      {activeTab === 'coupons' && (
        <CouponsTab coupons={coupons} onRefresh={loadData} />
      )}

      {activeTab === 'promotions' && (
        <PromotionsTab promotions={promotions} onRefresh={loadData} />
      )}

      {activeTab === 'banners' && (
        <BannersTab banners={banners} onRefresh={loadData} />
      )}

      {activeTab === 'campaigns' && (
        <CampaignsTab />
      )}
    </div>
  )
}

// Overview Tab Component
function OverviewTab({ insights }: { insights: MarketingInsights | null }) {
  const navigate = useNavigate()

  if (!insights) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">Loading marketing insights...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="card-optimized">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Gift className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active Coupons</p>
                <p className="text-2xl font-bold text-gray-900">{insights.activeCoupons}</p>
                <p className="text-xs text-gray-500">of {insights.totalCoupons} total</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="card-optimized">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Percent className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active Promotions</p>
                <p className="text-2xl font-bold text-gray-900">{insights.activePromotions}</p>
                <p className="text-xs text-gray-500">of {insights.totalPromotions} total</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="card-optimized">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DollarSign className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Promotion Revenue</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(insights.promotionRevenue)}</p>
                <p className="text-xs text-green-600">+12% from last month</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="card-optimized">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Coupon Usage Rate</p>
                <p className="text-2xl font-bold text-gray-900">{insights.couponUsageRate.toFixed(1)}%</p>
                <p className="text-xs text-gray-500">conversion rate</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="card-optimized cursor-pointer hover:shadow-md transition-shadow" onClick={() => navigate('/admin/marketing/coupons')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Gift className="h-5 w-5 text-green-600" />
              Coupon Management
            </CardTitle>
            <CardDescription>
              Create and manage discount coupons
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">{insights.activeCoupons} active coupons</span>
              <ArrowRight className="h-4 w-4" />
            </div>
          </CardContent>
        </Card>

        <Card className="card-optimized cursor-pointer hover:shadow-md transition-shadow" onClick={() => navigate('/admin/marketing/promotions')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Percent className="h-5 w-5 text-purple-600" />
              Promotions
            </CardTitle>
            <CardDescription>
              Manage site-wide and category promotions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">{insights.activePromotions} running</span>
              <ArrowRight className="h-4 w-4" />
            </div>
          </CardContent>
        </Card>

        <Card className="card-optimized cursor-pointer hover:shadow-md transition-shadow" onClick={() => navigate('/admin/marketing/campaigns')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5 text-blue-600" />
              Campaigns
            </CardTitle>
            <CardDescription>
              Email and SMS marketing campaigns
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">Coming soon</span>
              <ArrowRight className="h-4 w-4" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Performing Coupons */}
        <Card>
          <CardHeader>
            <CardTitle>Top Performing Coupons</CardTitle>
            <CardDescription>Most used coupons by usage count</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {insights.topPerformingCoupons.map((coupon, index) => (
                <div key={coupon.id} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-8 h-8 bg-accent-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-accent-600">#{index + 1}</span>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">{coupon.code}</p>
                      <p className="text-xs text-gray-500">{coupon.usageCount} uses</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">{formatCurrency(coupon.revenue)}</p>
                    <p className="text-xs text-gray-500">revenue</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Banner Click Stats */}
        <Card>
          <CardHeader>
            <CardTitle>Banner Performance</CardTitle>
            <CardDescription>Click-through rates for active banners</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {insights.bannerClickStats.map((banner, index) => (
                <div key={banner.id} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                      <Image className="h-4 w-4 text-purple-600" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">{banner.title}</p>
                      <p className="text-xs text-gray-500">{banner.clicks} clicks / {banner.impressions} views</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">{banner.ctr.toFixed(2)}%</p>
                    <p className="text-xs text-gray-500">CTR</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Coupons Tab Component
function CouponsTab({ coupons, onRefresh }: { coupons: Coupon[], onRefresh: () => void }) {
  const navigate = useNavigate()
  const { hasPermission } = useFirebaseAuthStore()
  const canEdit = hasPermission(['super_admin', 'admin', 'editor'])
  const canDelete = hasPermission(['super_admin', 'admin'])

  const handleDeleteCoupon = async (couponId: string) => {
    if (confirm('Are you sure you want to delete this coupon?')) {
      try {
        await marketingService.deleteCoupon(couponId)
        toast.success('Coupon deleted successfully')
      } catch (error) {
        toast.error('Failed to delete coupon')
      }
    }
  }

  const columns: Column<Coupon>[] = [
    {
      key: 'code',
      label: 'Code',
      render: (coupon) => (
        <div className="flex items-center gap-2">
          <code className="px-2 py-1 bg-gray-100 rounded text-sm font-mono">{coupon?.code || 'N/A'}</code>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigator.clipboard.writeText(coupon?.code || '')}
            disabled={!coupon?.code}
          >
            <Copy className="h-3 w-3" />
          </Button>
        </div>
      )
    },
    {
      key: 'type',
      label: 'Type',
      render: (coupon) => {
        if (!coupon?.discountType) {
          return <Badge variant="outline">Unknown</Badge>
        }
        return (
          <Badge variant={coupon.discountType === 'percentage' ? 'default' : coupon.discountType === 'flat' ? 'secondary' : 'outline'}>
            {coupon.discountType === 'percentage' ? `${coupon.discountValue}% off` :
             coupon.discountType === 'flat' ? `₹${coupon.discountValue} off` :
             'Free Shipping'}
          </Badge>
        )
      }
    },
    {
      key: 'usage',
      label: 'Usage',
      render: (coupon) => {
        if (!coupon || typeof coupon.usedCount !== 'number' || typeof coupon.usageLimit !== 'number') {
          return <div className="text-sm text-gray-400">No data</div>
        }
        const percentage = coupon.usageLimit > 0 ? ((coupon.usedCount / coupon.usageLimit) * 100).toFixed(1) : '0'
        return (
          <div className="text-sm">
            <div className="font-medium">{coupon.usedCount} / {coupon.usageLimit}</div>
            <div className="text-gray-500">{percentage}% used</div>
          </div>
        )
      }
    },
    {
      key: 'validity',
      label: 'Validity',
      render: (coupon) => {
        if (!coupon?.expiresAt) {
          return <div className="text-sm text-gray-400">No expiry set</div>
        }
        return (
          <div className="text-sm">
            <div>Until {formatDateTime(coupon.expiresAt)}</div>
            <div className="text-gray-500">
              {new Date(coupon.expiresAt) > new Date() ? 'Active' : 'Expired'}
            </div>
          </div>
        )
      }
    },
    {
      key: 'status',
      label: 'Status',
      render: (coupon) => (
        <Badge variant={coupon?.active ? 'default' : 'secondary'}>
          {coupon?.active ? 'Active' : 'Inactive'}
        </Badge>
      )
    }
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Coupons</h2>
          <p className="text-sm text-gray-500">Manage discount coupons and track their performance</p>
        </div>
        {canEdit && (
          <Button variant="outline" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        )}
      </div>

      <DataTable
        data={coupons}
        columns={columns}
        searchable={true}
        filterable={true}
        actions={{
          view: (coupon) => navigate(`/admin/marketing/coupons/${coupon.id}`),
          edit: canEdit ? (coupon) => navigate(`/admin/marketing/coupons/${coupon.id}/edit`) : undefined,
          delete: canDelete ? (coupon) => handleDeleteCoupon(coupon.id) : undefined
        }}
        emptyState={{
          title: 'No coupons found',
          description: 'Create your first coupon to start offering discounts.',
          icon: <Gift className="h-6 w-6 text-gray-400" />
        }}
      />
    </div>
  )
}

// Promotions Tab Component
function PromotionsTab({ promotions, onRefresh }: { promotions: Promotion[], onRefresh: () => void }) {
  const navigate = useNavigate()
  const { hasPermission } = useFirebaseAuthStore()
  const canEdit = hasPermission(['super_admin', 'admin', 'editor'])
  const canDelete = hasPermission(['super_admin', 'admin'])

  const handleDeletePromotion = async (promotionId: string) => {
    if (confirm('Are you sure you want to delete this promotion?')) {
      try {
        await marketingService.deletePromotion(promotionId)
        toast.success('Promotion deleted successfully')
      } catch (error) {
        toast.error('Failed to delete promotion')
      }
    }
  }

  const handleTogglePromotion = async (promotionId: string, active: boolean) => {
    try {
      await marketingService.updatePromotion(promotionId, { active })
      toast.success(`Promotion ${active ? 'activated' : 'deactivated'} successfully`)
    } catch (error) {
      toast.error('Failed to update promotion')
    }
  }

  const columns: Column<Promotion>[] = [
    {
      key: 'title',
      label: 'Title',
      render: (promotion) => (
        <div>
          <div className="font-medium">{promotion?.title || 'Unknown'}</div>
          <div className="text-sm text-gray-500">{promotion?.description || ''}</div>
        </div>
      )
    },
    {
      key: 'discount',
      label: 'Discount',
      render: (promotion) => {
        if (!promotion || !promotion.discountType) {
          return <Badge variant="outline">Unknown</Badge>
        }
        return (
          <Badge variant="secondary">
            {promotion.discountType === 'percentage' ? `${promotion.discountValue}% off` :
             promotion.discountType === 'fixed_amount' ? `₹${promotion.discountValue} off` :
             'Free Shipping'}
          </Badge>
        )
      }
    },
    {
      key: 'period',
      label: 'Period',
      render: (promotion) => {
        if (!promotion?.startDate || !promotion?.endDate) {
          return <div className="text-sm text-gray-400">No dates set</div>
        }
        return (
          <div className="text-sm">
            <div>{formatDateTime(promotion.startDate)}</div>
            <div className="text-gray-500">to {formatDateTime(promotion.endDate)}</div>
          </div>
        )
      }
    },
    {
      key: 'status',
      label: 'Status',
      render: (promotion) => {
        if (!promotion) {
          return <Badge variant="outline">Unknown</Badge>
        }

        const now = new Date()
        const start = promotion.startDate ? new Date(promotion.startDate) : null
        const end = promotion.endDate ? new Date(promotion.endDate) : null

        let status = 'Scheduled'
        let variant: 'default' | 'secondary' | 'destructive' | 'outline' = 'outline'

        if (start && end && now >= start && now <= end && promotion.active) {
          status = 'Active'
          variant = 'default'
        } else if (end && now > end) {
          status = 'Expired'
          variant = 'secondary'
        } else if (promotion.active === false) {
          status = 'Inactive'
          variant = 'destructive'
        }

        return <Badge variant={variant}>{status}</Badge>
      }
    },
    {
      key: 'autoApply',
      label: 'Auto Apply',
      render: (promotion) => (
        <Badge variant={promotion?.autoApply ? 'default' : 'outline'}>
          {promotion?.autoApply ? 'Yes' : 'No'}
        </Badge>
      )
    }
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Promotions</h2>
          <p className="text-sm text-gray-500">Manage site-wide and category promotions</p>
        </div>
        {canEdit && (
          <Button variant="outline" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        )}
      </div>

      <DataTable
        data={promotions}
        columns={columns}
        searchable={true}
        filterable={true}
        actions={{
          view: (promotion) => navigate(`/admin/marketing/promotions/${promotion.id}`),
          edit: canEdit ? (promotion) => navigate(`/admin/marketing/promotions/${promotion.id}/edit`) : undefined,
          delete: canDelete ? (promotion) => handleDeletePromotion(promotion.id) : undefined,
          custom: canEdit ? [
            {
              label: 'Toggle Status',
              icon: <Play className="h-4 w-4" />,
              onClick: (promotion) => handleTogglePromotion(promotion.id, !promotion.active)
            }
          ] : undefined
        }}
        emptyState={{
          title: 'No promotions found',
          description: 'Create your first promotion to boost sales.',
          icon: <Percent className="h-6 w-6 text-gray-400" />
        }}
      />
    </div>
  )
}

// Banners Tab Component
function BannersTab({ banners, onRefresh }: { banners: Banner[], onRefresh: () => void }) {
  const navigate = useNavigate()
  const { hasPermission } = useFirebaseAuthStore()
  const canEdit = hasPermission(['super_admin', 'admin'])
  const canDelete = hasPermission(['super_admin', 'admin'])

  const handleDeleteBanner = async (bannerId: string) => {
    if (confirm('Are you sure you want to delete this banner?')) {
      try {
        await marketingService.deleteBanner(bannerId)
        toast.success('Banner deleted successfully')
      } catch (error) {
        toast.error('Failed to delete banner')
      }
    }
  }

  const columns: Column<Banner>[] = [
    {
      key: 'image',
      label: 'Preview',
      render: (banner) => (
        <img
          src={banner?.imageUrl || '/placeholder-image.jpg'}
          alt={banner?.title || 'Banner'}
          className="w-16 h-10 object-cover rounded"
          onError={(e) => {
            e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA2NCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAyMEwyOCAyNEwzNiAxNkw0MCAyMFYzMkgyNFYyMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'
          }}
        />
      )
    },
    {
      key: 'title',
      label: 'Title',
      render: (banner) => (
        <div>
          <div className="font-medium">{banner?.title || 'Untitled'}</div>
          <div className="text-sm text-gray-500">Position: {banner?.position || 'Unknown'}</div>
        </div>
      )
    },
    {
      key: 'performance',
      label: 'Performance',
      render: (banner) => (
        <div className="text-sm">
          <div>{banner.clickCount} clicks / {banner.impressionCount} views</div>
          <div className="text-gray-500">
            CTR: {banner.impressionCount > 0 ? ((banner.clickCount / banner.impressionCount) * 100).toFixed(2) : 0}%
          </div>
        </div>
      )
    },
    {
      key: 'period',
      label: 'Period',
      render: (banner) => (
        <div className="text-sm">
          <div>{formatDateTime(banner.startDate)}</div>
          <div className="text-gray-500">to {formatDateTime(banner.endDate)}</div>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (banner) => (
        <Badge variant={banner.active ? 'default' : 'secondary'}>
          {banner.active ? 'Active' : 'Inactive'}
        </Badge>
      )
    }
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Banners</h2>
          <p className="text-sm text-gray-500">Manage promotional banners and track their performance</p>
        </div>
        {canEdit && (
          <Button variant="outline" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        )}
      </div>

      <DataTable
        data={banners}
        columns={columns}
        searchable={true}
        filterable={true}
        actions={{
          view: (banner) => navigate(`/admin/marketing/banners/${banner.id}`),
          edit: canEdit ? (banner) => navigate(`/admin/marketing/banners/${banner.id}/edit`) : undefined,
          delete: canDelete ? (banner) => handleDeleteBanner(banner.id) : undefined
        }}
        emptyState={{
          title: 'No banners found',
          description: 'Upload your first banner to start promoting.',
          icon: <Image className="h-6 w-6 text-gray-400" />
        }}
      />
    </div>
  )
}

// Campaigns Tab Component
function CampaignsTab() {
  const navigate = useNavigate()
  const { hasPermission } = useFirebaseAuthStore()
  const canEdit = hasPermission(['super_admin', 'admin'])

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Email Campaigns</h2>
          <p className="text-sm text-gray-500">Create and manage email marketing campaigns</p>
        </div>
        {canEdit && (
          <div className="flex gap-3">
            <Button disabled>
              <Plus className="h-4 w-4 mr-2" />
              Create Campaign
            </Button>
          </div>
        )}
      </div>

      {/* Coming Soon Card */}
      <Card className="card-optimized">
        <CardContent className="p-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Mail className="h-8 w-8 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Email Campaigns Coming Soon</h3>
            <p className="text-gray-500 mb-8 max-w-2xl mx-auto">
              Email campaign functionality will be available in a future update.
              You'll be able to create, schedule, and track email marketing campaigns to engage with your customers.
            </p>

            {/* Feature Preview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <h4 className="font-medium text-gray-900 mb-1">Template Builder</h4>
                <p className="text-sm text-gray-500">Drag-and-drop email templates</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
                <h4 className="font-medium text-gray-900 mb-1">Audience Targeting</h4>
                <p className="text-sm text-gray-500">Segment customers by behavior</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <BarChart3 className="h-6 w-6 text-orange-600" />
                </div>
                <h4 className="font-medium text-gray-900 mb-1">Analytics & Tracking</h4>
                <p className="text-sm text-gray-500">Track opens, clicks, and conversions</p>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-600">
                <strong>Tip:</strong> In the meantime, you can use coupons and promotions to drive sales and engage customers.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}


