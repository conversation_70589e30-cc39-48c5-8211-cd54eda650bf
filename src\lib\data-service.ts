import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  onSnapshot,
  writeBatch,
  increment,
  arrayUnion,
  arrayRemove
} from 'firebase/firestore'
import { db, isUsingEmulator, isFirebaseConfigured, type AdminUser, type Product, type Order, type Customer, type Category } from './firebase'

// Service class for handling all data operations
export class DataService {
  private storeId = import.meta.env.VITE_STORE_ID || 'womanza_store_id'

  // Helper method to check if we should use Firebase
  private get useFirebase(): boolean {
    return isFirebaseConfigured
  }

  // Real-time subscription management
  private subscriptions: (() => void)[] = []

  // Clean up subscriptions
  public cleanup() {
    this.subscriptions.forEach(unsubscribe => unsubscribe())
    this.subscriptions = []
  }
  // Products
  async getProducts(): Promise<Product[]> {
    if (!this.useFirebase) {
      console.warn('Firebase not configured, returning empty array')
      return []
    }

    try {
      // Query products from store subcollection
      const q = query(
        collection(db, 'stores', this.storeId, 'products'),
        orderBy('createdAt', 'desc'),
        limit(100)
      )
      const snapshot = await getDocs(q)
      const products = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
        updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
        publishedAt: doc.data().publishedAt?.toDate?.()?.toISOString() || doc.data().publishedAt
      } as Product))

      // Sort on client side to avoid index requirement
      return products.sort((a, b) => {
        const dateA = new Date(a.createdAt || 0).getTime()
        const dateB = new Date(b.createdAt || 0).getTime()
        return dateB - dateA // Descending order
      })
    } catch (error) {
      console.error('Error fetching products:', error)
      return []
    }
  }

  async getProduct(id: string): Promise<Product | null> {
    if (!this.useFirebase) {
      console.warn('Firebase not configured')
      return null
    }

    try {
      const docRef = doc(db, 'stores', this.storeId, 'products', id)
      const docSnap = await getDoc(docRef)

      if (!docSnap.exists()) {
        return null
      }

      const data = docSnap.data()
      return {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
        publishedAt: data.publishedAt?.toDate?.()?.toISOString() || data.publishedAt
      } as Product
    } catch (error) {
      console.error('Error fetching product:', error)
      return null
    }
  }

  async createProduct(productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product> {
    if (!this.useFirebase) {
      throw new Error('Firebase not configured')
    }

    try {
      const docRef = await addDoc(collection(db, 'stores', this.storeId, 'products'), {
        ...productData,
        storeId: this.storeId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      })

      const docSnap = await getDoc(docRef)
      const data = docSnap.data()!
      return {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
        publishedAt: data.publishedAt?.toDate?.()?.toISOString() || data.publishedAt
      } as Product
    } catch (error) {
      console.error('Error creating product:', error)
      throw new Error('Failed to create product')
    }
  }

  async updateProduct(id: string, updates: Partial<Product>): Promise<Product> {
    if (!this.useFirebase) {
      throw new Error('Firebase not configured')
    }

    try {
      const docRef = doc(db, 'stores', this.storeId, 'products', id)
      await updateDoc(docRef, {
        ...updates,
        updatedAt: serverTimestamp()
      })

      const docSnap = await getDoc(docRef)
      const data = docSnap.data()!
      return {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
        publishedAt: data.publishedAt?.toDate?.()?.toISOString() || data.publishedAt
      } as Product
    } catch (error) {
      console.error('Error updating product:', error)
      throw new Error('Failed to update product')
    }
  }

  async deleteProduct(id: string): Promise<void> {
    if (!this.useFirebase) {
      throw new Error('Firebase not configured')
    }

    try {
      await deleteDoc(doc(db, 'stores', this.storeId, 'products', id))
    } catch (error) {
      console.error('Error deleting product:', error)
      throw new Error('Failed to delete product')
    }
  }

  // Orders
  async getOrders(): Promise<Order[]> {
    if (!this.useFirebase) {
      console.warn('Firebase not configured, returning empty array')
      return []
    }

    try {
      // Simplified query to avoid index requirements
      const q = query(
        collection(db, 'orders'),
        where('storeId', '==', this.storeId),
        limit(100)
      )
      const snapshot = await getDocs(q)
      const orders = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
        updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
        processedAt: doc.data().processedAt?.toDate?.()?.toISOString() || doc.data().processedAt,
        shippedAt: doc.data().shippedAt?.toDate?.()?.toISOString() || doc.data().shippedAt,
        deliveredAt: doc.data().deliveredAt?.toDate?.()?.toISOString() || doc.data().deliveredAt
      } as Order))

      // Sort on client side to avoid index requirement
      return orders.sort((a, b) => {
        const dateA = new Date(a.createdAt || 0).getTime()
        const dateB = new Date(b.createdAt || 0).getTime()
        return dateB - dateA // Descending order
      })
    } catch (error) {
      console.error('Error fetching orders:', error)
      return []
    }
  }

  async getOrder(id: string): Promise<Order | null> {
    if (!this.useFirebase) {
      console.warn('Firebase not configured')
      return null
    }

    try {
      const docRef = doc(db, 'orders', id)
      const docSnap = await getDoc(docRef)

      if (!docSnap.exists()) {
        return null
      }

      const data = docSnap.data()
      return {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
        processedAt: data.processedAt?.toDate?.()?.toISOString() || data.processedAt,
        shippedAt: data.shippedAt?.toDate?.()?.toISOString() || data.shippedAt,
        deliveredAt: data.deliveredAt?.toDate?.()?.toISOString() || data.deliveredAt
      } as Order
    } catch (error) {
      console.error('Error fetching order:', error)
      return null
    }
  }

  // Customers
  async getCustomers(): Promise<Customer[]> {
    if (!this.useFirebase) {
      console.warn('Firebase not configured, returning empty array')
      return []
    }

    try {
      console.log('🔍 Fetching customers for store:', this.storeId)

      // Use store-specific customers collection
      const q = query(
        collection(db, 'stores', this.storeId, 'customers'),
        orderBy('createdAt', 'desc')
      )
      const snapshot = await getDocs(q)

      const customers = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
        updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
        dateOfBirth: doc.data().dateOfBirth?.toDate?.()?.toISOString() || doc.data().dateOfBirth
      } as Customer))

      console.log('✅ Fetched', customers.length, 'customers')
      return customers
    } catch (error) {
      console.error('❌ Error fetching customers:', error)
      return []
    }
  }

  async getCustomer(id: string): Promise<Customer | null> {
    if (!this.useFirebase) {
      console.warn('Firebase not configured')
      return null
    }

    try {
      console.log('🔍 Fetching customer:', id, 'for store:', this.storeId)

      // Use store-specific customer document
      const docRef = doc(db, 'stores', this.storeId, 'customers', id)
      const docSnap = await getDoc(docRef)

      if (!docSnap.exists()) {
        console.log('❌ Customer not found:', id)
        return null
      }

      const data = docSnap.data()
      const customer = {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
        dateOfBirth: data.dateOfBirth?.toDate?.()?.toISOString() || data.dateOfBirth
      } as Customer
    } catch (error) {
      console.error('Error fetching customer:', error)
      return null
    }
  }
  // Categories
  async getCategories(): Promise<Category[]> {
    if (!this.useFirebase) {
      console.warn('Firebase not configured, returning empty array')
      return []
    }

    try {
      const q = query(
        collection(db, 'categories'),
        where('storeId', '==', this.storeId),
        orderBy('sortOrder', 'asc')
      )
      const snapshot = await getDocs(q)
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
        updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt
      } as Category))
    } catch (error) {
      console.error('Error fetching categories:', error)
      return []
    }
  }

  // Real-time subscriptions with enhanced error handling
  subscribeToProducts(callback: (products: Product[]) => void): () => void {
    if (!this.useFirebase) {
      console.warn('Firebase not configured, using mock data')
      // Return mock data for development
      setTimeout(() => callback([]), 100)
      return () => {}
    }

    try {
      // Simplified query to avoid index requirements
      const q = query(
        collection(db, 'products'),
        where('storeId', '==', this.storeId),
        limit(100)
      )

      return onSnapshot(q, (snapshot) => {
        try {
          const products = snapshot.docs.map(doc => {
            const data = doc.data()
            return {
              id: doc.id,
              ...data,
              createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
              updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
              publishedAt: data.publishedAt?.toDate?.()?.toISOString() || data.publishedAt
            } as Product
          })

          // Sort on client side to avoid index requirement
          const sortedProducts = products.sort((a, b) => {
            const dateA = new Date(a.createdAt || 0).getTime()
            const dateB = new Date(b.createdAt || 0).getTime()
            return dateB - dateA // Descending order
          })

          console.log(`📦 Real-time products update: ${sortedProducts.length} items`)
          callback(sortedProducts)
        } catch (error) {
          console.error('Error processing products snapshot:', error)
          callback([])
        }
      }, (error) => {
        console.error('Error in products subscription:', error)
        // Return empty array on permission errors
        if (error.code === 'permission-denied') {
          console.warn('⚠️ Products subscription: Permission denied. Deploy Firestore rules.')
        }
        callback([])
      })
    } catch (error) {
      console.error('Error setting up products subscription:', error)
      callback([])
      return () => {}
    }
  }

  subscribeToOrders(callback: (orders: Order[]) => void): () => void {
    if (!this.useFirebase) {
      console.warn('Firebase not configured, using mock data')
      setTimeout(() => callback([]), 100)
      return () => {}
    }

    try {
      // Simplified query to avoid index requirements
      const q = query(
        collection(db, 'orders'),
        where('storeId', '==', this.storeId),
        limit(100)
      )

      return onSnapshot(q, (snapshot) => {
        try {
          const orders = snapshot.docs.map(doc => {
            const data = doc.data()
            return {
              id: doc.id,
              ...data,
              createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
              updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
              processedAt: data.processedAt?.toDate?.()?.toISOString() || data.processedAt,
              shippedAt: data.shippedAt?.toDate?.()?.toISOString() || data.shippedAt,
              deliveredAt: data.deliveredAt?.toDate?.()?.toISOString() || data.deliveredAt
            } as Order
          })

          // Sort on client side to avoid index requirement
          const sortedOrders = orders.sort((a, b) => {
            const dateA = new Date(a.createdAt || 0).getTime()
            const dateB = new Date(b.createdAt || 0).getTime()
            return dateB - dateA // Descending order
          })

          console.log(`🛒 Real-time orders update: ${sortedOrders.length} items`)
          callback(sortedOrders)
        } catch (error) {
          console.error('Error processing orders snapshot:', error)
          callback([])
        }
      }, (error) => {
        console.error('Error in orders subscription:', error)
        // Return empty array on permission errors
        if (error.code === 'permission-denied') {
          console.warn('⚠️ Orders subscription: Permission denied. Deploy Firestore rules.')
        }
        callback([])
      })
    } catch (error) {
      console.error('Error setting up orders subscription:', error)
      callback([])
      return () => {}
    }
  }

  // Dashboard stats
  async getDashboardStats() {
    if (!this.useFirebase) {
      return {
        totalRevenue: 0,
        totalOrders: 0,
        totalCustomers: 0,
        totalProducts: 0,
        recentOrders: [],
        topProducts: []
      }
    }

    try {
      const [products, orders, customers] = await Promise.all([
        this.getProducts(),
        this.getOrders(),
        this.getCustomers()
      ])

      const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0)
      const recentOrders = orders.slice(0, 5)
      const topProducts = products.filter(p => p.isFeatured).slice(0, 5)

      return {
        totalRevenue,
        totalOrders: orders.length,
        totalCustomers: customers.length,
        totalProducts: products.length,
        recentOrders,
        topProducts
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
      return {
        totalRevenue: 0,
        totalOrders: 0,
        totalCustomers: 0,
        totalProducts: 0,
        recentOrders: [],
        topProducts: []
      }
    }
  }
}

// Export the data service instance
export const dataService = new DataService()
