import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'

import { useFirebaseAuthStore } from '../../store/firebase-auth'
import { DataTable, Column } from '../../components/ui/data-table'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { cn, formatCurrency, formatDateTime } from '../../lib/utils'
import { useAutoClose } from '../../hooks/use-click-outside'
import {
  Plus,
  ArrowLeft,
  Download,
  ChevronDown,
  RefreshCw,
  Percent,
  DollarSign,
  Calendar,
  TrendingUp,
  Image,
  Eye,
  Edit,
  Trash2,
  Play,
  Pause,
  Clock
} from 'lucide-react'

interface Promotion {
  id: string
  title: string
  description: string
  promoType: 'site_wide' | 'category' | 'product'
  categoryId?: string
  productIds?: string[]
  discountType: 'percentage' | 'fixed'
  discountValue: number
  image?: string
  startDate: string
  endDate: string
  status: 'active' | 'scheduled' | 'paused' | 'expired'
  priority: number
  createdAt: string
  createdBy: string
}

export function PromotionsPage() {
  const navigate = useNavigate()
  const { user, hasPermission } = useFirebaseAuthStore()
  const [loading, setLoading] = useState(true)
  const [promotions, setPromotions] = useState<Promotion[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')
  const [filterStatus, setFilterStatus] = useState('all')
  const [showExportDropdown, setShowExportDropdown] = useState(false)

  const canEdit = hasPermission(['super_admin', 'admin', 'editor'])
  const canDelete = hasPermission(['super_admin', 'admin'])

  // Auto-close functionality for export dropdown
  const exportDropdownRef = useAutoClose(() => setShowExportDropdown(false), showExportDropdown)

  useEffect(() => {
    fetchPromotions()
  }, [user])

  const fetchPromotions = async () => {
    try {
      setLoading(true)

      // Mock data for development
      const mockPromotions: Promotion[] = [
        {
          id: '1',
          title: 'Summer Jewelry Sale',
          description: 'Flat 20% off on all rings and necklaces',
          promoType: 'category',
          categoryId: 'jewelry',
          discountType: 'percentage',
          discountValue: 20,
          image: 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400',
          startDate: '2025-07-22T00:00:00Z',
          endDate: '2025-07-28T23:59:59Z',
          status: 'active',
          priority: 1,
          createdAt: '2025-07-20T10:00:00Z',
          createdBy: '<EMAIL>'
        },
        {
          id: '2',
          title: 'Flash Sale Weekend',
          description: 'Site-wide 15% discount for weekend shoppers',
          promoType: 'site_wide',
          discountType: 'percentage',
          discountValue: 15,
          startDate: '2025-07-26T00:00:00Z',
          endDate: '2025-07-28T23:59:59Z',
          status: 'scheduled',
          priority: 2,
          createdAt: '2025-07-21T14:30:00Z',
          createdBy: '<EMAIL>'
        },
        {
          id: '3',
          title: 'New Collection Launch',
          description: 'Rs. 500 off on new arrivals',
          promoType: 'product',
          productIds: ['prod1', 'prod2', 'prod3'],
          discountType: 'fixed',
          discountValue: 500,
          image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400',
          startDate: '2025-07-25T00:00:00Z',
          endDate: '2025-08-25T23:59:59Z',
          status: 'scheduled',
          priority: 3,
          createdAt: '2025-07-21T16:00:00Z',
          createdBy: '<EMAIL>'
        }
      ]

      setPromotions(mockPromotions)
    } catch (error) {
      console.error('Error fetching promotions:', error)
      toast.error('Failed to load promotions')
    } finally {
      setLoading(false)
    }
  }

  const handleExport = (format: 'csv' | 'json' | 'xlsx') => {
    toast.success(`Exporting promotions as ${format.toUpperCase()}...`)
    // TODO: Implement actual export functionality
  }

  const handleDeletePromotion = async (promotionId: string) => {
    if (!canDelete) {
      toast.error('You do not have permission to delete promotions')
      return
    }

    if (confirm('Are you sure you want to delete this promotion?')) {
      try {
        // TODO: Implement actual delete functionality
        setPromotions(prev => prev.filter(p => p.id !== promotionId))
        toast.success('Promotion deleted successfully')
      } catch (error) {
        console.error('Error deleting promotion:', error)
        toast.error('Failed to delete promotion')
      }
    }
  }

  const handleTogglePromotion = async (promotionId: string, currentStatus: string) => {
    if (!canEdit) {
      toast.error('You do not have permission to modify promotions')
      return
    }

    try {
      const newStatus = currentStatus === 'active' ? 'paused' : 'active'
      setPromotions(prev => prev.map(p => 
        p.id === promotionId ? { ...p, status: newStatus as any } : p
      ))
      toast.success(`Promotion ${newStatus === 'active' ? 'activated' : 'paused'} successfully`)
    } catch (error) {
      console.error('Error toggling promotion:', error)
      toast.error('Failed to update promotion status')
    }
  }

  const getStatusBadge = (promotion: Promotion) => {
    const now = new Date()
    const startDate = new Date(promotion.startDate)
    const endDate = new Date(promotion.endDate)

    switch (promotion.status) {
      case 'active':
        if (now > endDate) {
          return <Badge className="bg-red-100 text-red-800">Expired</Badge>
        }
        return <Badge className="bg-green-100 text-green-800">Active</Badge>
      case 'scheduled':
        return <Badge className="bg-blue-100 text-blue-800">Scheduled</Badge>
      case 'paused':
        return <Badge className="bg-yellow-100 text-yellow-800">Paused</Badge>
      case 'expired':
        return <Badge className="bg-red-100 text-red-800">Expired</Badge>
      default:
        return <Badge variant="outline">{promotion.status}</Badge>
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'site_wide':
        return 'Site-wide'
      case 'category':
        return 'Category'
      case 'product':
        return 'Product'
      default:
        return type
    }
  }

  const filteredPromotions = promotions.filter(promotion => {
    const matchesSearch = promotion.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         promotion.description.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesType = filterType === 'all' || promotion.promoType === filterType

    const matchesStatus = filterStatus === 'all' || promotion.status === filterStatus

    return matchesSearch && matchesType && matchesStatus
  })

  const columns: Column<Promotion>[] = [
    {
      key: 'title',
      title: 'Promotion',
      sortable: true,
      render: (promotion) => (
        <div className="flex items-center gap-3">
          {promotion.image ? (
            <img 
              src={promotion.image} 
              alt={promotion.title}
              className="w-12 h-12 object-cover rounded-lg"
            />
          ) : (
            <div className="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
              <Image className="h-6 w-6 text-gray-400" />
            </div>
          )}
          <div>
            <div className="font-medium text-gray-900 dark:text-gray-100">{promotion.title}</div>
            <div className="text-sm text-gray-500 dark:text-gray-400 line-clamp-1">
              {promotion.description}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'type',
      title: 'Type',
      sortable: true,
      render: (promotion) => {
        if (!promotion) return <div>-</div>

        return (
          <div>
            <Badge variant="outline">{getTypeLabel(promotion.promoType)}</Badge>
            <div className="text-xs text-gray-500 mt-1">
              Priority: {promotion.priority || 0}
            </div>
          </div>
        )
      }
    },
    {
      key: 'discount',
      title: 'Discount',
      sortable: true,
      render: (promotion) => {
        if (!promotion) return <div>-</div>

        return (
          <div className="font-medium">
            {promotion.discountType === 'percentage' ? (
              <span>{promotion.discountValue || 0}% OFF</span>
            ) : (
              <span>{formatCurrency(promotion.discountValue || 0)} OFF</span>
            )}
          </div>
        )
      }
    },
    {
      key: 'schedule',
      title: 'Schedule',
      sortable: true,
      render: (promotion) => {
        if (!promotion) return <div>-</div>

        return (
          <div className="text-sm">
            <div>Start: {formatDateTime(promotion.startDate)}</div>
            <div>End: {formatDateTime(promotion.endDate)}</div>
          </div>
        )
      }
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      render: (promotion) => {
        if (!promotion) return <div>-</div>
        return getStatusBadge(promotion)
      }
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (promotion) => (
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/admin/marketing/promotions/${promotion.id}`)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          {canEdit && (
            <>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate(`/admin/marketing/promotions/${promotion.id}/edit`)}
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleTogglePromotion(promotion.id, promotion.status)}
                className={promotion.status === 'active' ? 'text-yellow-600' : 'text-green-600'}
              >
                {promotion.status === 'active' ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>
            </>
          )}
          {canDelete && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDeletePromotion(promotion.id)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      )
    }
  ]

  return (
    <div className="page-container">
      {/* Header */}
      <div className="page-header flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/admin/marketing')}
            className="hover:bg-gray-100"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="page-title">Promotions</h1>
            <p className="page-subtitle">Manage site-wide and category promotions</p>
          </div>
        </div>
        {canEdit && (
          <Button onClick={() => navigate('/admin/marketing/promotions/new')}>
            <Plus className="h-4 w-4 mr-2" />
            New Promotion
          </Button>
        )}
      </div>

      {/* Stats Cards */}
      <div className="stats-grid-optimized">
        <Card className="card-optimized">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Promotions</CardTitle>
            <Percent className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{promotions.length}</div>
            <p className="text-xs text-gray-500">
              {promotions.filter(p => p.status === 'active').length} active
            </p>
          </CardContent>
        </Card>

        <Card className="card-optimized">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Promotions</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {promotions.filter(p => p.status === 'active').length}
            </div>
            <p className="text-xs text-gray-500">
              Currently running
            </p>
          </CardContent>
        </Card>

        <Card className="card-optimized">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
            <Clock className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {promotions.filter(p => p.status === 'scheduled').length}
            </div>
            <p className="text-xs text-gray-500">
              Upcoming promotions
            </p>
          </CardContent>
        </Card>

        <Card className="card-optimized">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Discount</CardTitle>
            <DollarSign className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(promotions.reduce((sum, p) => sum + p.discountValue, 0) / promotions.length)}
              {promotions.some(p => p.discountType === 'percentage') ? '%' : ''}
            </div>
            <p className="text-xs text-gray-500">
              Average discount value
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Data Table */}
      <DataTable
        data={filteredPromotions}
        columns={columns}
        loading={loading}
        searchable={{
          placeholder: 'Search promotions...',
          value: searchTerm,
          onChange: setSearchTerm
        }}
        filterable={{
          filters: [
            {
              key: 'type',
              label: 'Type',
              value: filterType,
              onChange: setFilterType,
              options: [
                { label: 'All Types', value: 'all' },
                { label: 'Site-wide', value: 'site_wide' },
                { label: 'Category', value: 'category' },
                { label: 'Product', value: 'product' }
              ]
            },
            {
              key: 'status',
              label: 'Status',
              value: filterStatus,
              onChange: setFilterStatus,
              options: [
                { label: 'All Status', value: 'all' },
                { label: 'Active', value: 'active' },
                { label: 'Scheduled', value: 'scheduled' },
                { label: 'Paused', value: 'paused' },
                { label: 'Expired', value: 'expired' }
              ]
            }
          ]
        }}
        actions={
          <div className="flex gap-2">
            <div className="relative" ref={exportDropdownRef}>
              <Button 
                variant="outline" 
                size="sm" 
                className="flex items-center gap-2"
                onClick={() => setShowExportDropdown(!showExportDropdown)}
              >
                <Download className="h-4 w-4" />
                Export
                <ChevronDown className={cn("h-3 w-3 transition-transform", showExportDropdown && "rotate-180")} />
              </Button>
              {showExportDropdown && (
                <div className="absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 min-w-[140px] theme-dropdown">
                  <button
                    onClick={() => {
                      handleExport('csv')
                      setShowExportDropdown(false)
                    }}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 first:rounded-t-lg theme-dropdown-item"
                  >
                    Export as CSV
                  </button>
                  <button
                    onClick={() => {
                      handleExport('json')
                      setShowExportDropdown(false)
                    }}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 theme-dropdown-item"
                  >
                    Export as JSON
                  </button>
                  <button
                    onClick={() => {
                      handleExport('xlsx')
                      setShowExportDropdown(false)
                    }}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 last:rounded-b-lg theme-dropdown-item"
                  >
                    Export as Excel
                  </button>
                </div>
              )}
            </div>
            <Button variant="outline" size="sm" onClick={fetchPromotions}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        }
        emptyState={{
          title: 'No promotions found',
          description: searchTerm || filterType !== 'all' || filterStatus !== 'all'
            ? 'No promotions match your current filters.'
            : 'Create your first promotion to start driving sales.',
          icon: <Percent className="h-6 w-6 text-gray-400" />
        }}
      />
    </div>
  )
}
