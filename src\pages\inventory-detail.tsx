import React, { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { toast } from 'react-hot-toast'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { InventoryService } from '../lib/inventory-service'
import { dataService } from '../lib/data-service'
import { Button } from '../components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import { Input } from '../components/ui/input'
import { Label } from '../components/ui/label'
import { Switch } from '../components/ui/switch'
import { formatDateTime } from '../lib/utils'
import {
  ArrowLeft,
  Edit,
  Package,
  Clock,
  User,
  TrendingUp,
  TrendingDown,
  RotateCcw,
  Settings,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react'
import type { ProductWithInventory, InventoryLog } from '../types/inventory'

export default function InventoryDetailPage() {
  const navigate = useNavigate()
  const { productId, variantId } = useParams()
  const { user } = useFirebaseAuthStore()

  // State
  const [product, setProduct] = useState<ProductWithInventory | null>(null)
  const [logs, setLogs] = useState<InventoryLog[]>([])
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [lowStockThreshold, setLowStockThreshold] = useState(0)
  const [trackInventory, setTrackInventory] = useState(true)

  // Initialize service
  const storeId = user?.storeIds?.[0] || 'womanza-jewelry-store'
  const inventoryService = new InventoryService(storeId)

  // Load product and logs
  const loadData = async () => {
    if (!productId) return

    try {
      setLoading(true)

      // Check if this is a sample product
      if (productId.startsWith('sample-product-')) {
        // Create sample product data
        const sampleProduct = getSampleProduct(productId)
        if (sampleProduct) {
          setProduct(sampleProduct)
          setLowStockThreshold(sampleProduct.lowStockThreshold)
          setTrackInventory(sampleProduct.trackInventory)

          // Set sample logs
          setLogs(getSampleLogs(productId))
        } else {
          toast.error('Sample product not found')
        }
      } else {
        // Load real product from Firebase
        const productData = await dataService.getProduct(productId)
        if (productData) {
          const productWithInventory = {
            ...productData,
            // Set default inventory values if not present
            trackInventory: productData.trackInventory ?? true,
            inventoryQuantity: productData.inventoryQuantity ?? 0,
            lowStockThreshold: productData.lowStockThreshold ?? 5,
            isOutOfStock: productData.isOutOfStock ?? false,
            hasVariants: productData.hasVariants ?? false,
            totalStock: productData.totalStock ?? productData.inventoryQuantity ?? 0,
            lastStockUpdate: productData.lastStockUpdate ?? productData.updatedAt
          } as ProductWithInventory

          setProduct(productWithInventory)

          // Set initial form values
          if (variantId && productWithInventory.variants) {
            const variant = productWithInventory.variants.find(v => v.id === variantId)
            if (variant) {
              setLowStockThreshold(variant.lowStockThreshold ?? 5)
              setTrackInventory(variant.trackInventory ?? true)
            }
          } else {
            setLowStockThreshold(productWithInventory.lowStockThreshold)
            setTrackInventory(productWithInventory.trackInventory)
          }

          // Load inventory logs (handle gracefully if it fails)
          try {
            const logsData = await inventoryService.getInventoryLogs(productId, variantId)
            setLogs(logsData)
          } catch (logError) {
            console.warn('Could not load inventory logs:', logError)
            setLogs([]) // Set empty logs instead of failing
          }
        } else {
          toast.error('Product not found')
        }
      }
    } catch (error) {
      console.error('Error loading data:', error)
      toast.error('Failed to load inventory data')
    } finally {
      setLoading(false)
    }
  }

  // Get sample product data
  const getSampleProduct = (productId: string): ProductWithInventory | null => {
    // Get stored stock from localStorage
    const getStoredStock = (productId: string, defaultStock: number) => {
      const storageKey = `sample-inventory-${productId}`
      const stored = localStorage.getItem(storageKey)
      return stored ? JSON.parse(stored).stock || defaultStock : defaultStock
    }

    const sampleProducts = {
      'sample-product-1': {
        id: 'sample-product-1',
        name: 'Gold Earrings',
        sku: 'GOLD-EAR-001',
        price: 12000,
        inventoryQuantity: getStoredStock('sample-product-1', 15),
        lowStockThreshold: 5,
        trackInventory: true,
        isOutOfStock: getStoredStock('sample-product-1', 15) === 0,
        hasVariants: false,
        totalStock: getStoredStock('sample-product-1', 15),
        lastStockUpdate: new Date().toISOString(),
        images: [],
        categoryName: 'Jewelry',
        status: 'active'
      },
      'sample-product-2': {
        id: 'sample-product-2',
        name: 'Silver Necklace',
        sku: 'SILVER-NECK-001',
        price: 8000,
        inventoryQuantity: getStoredStock('sample-product-2', 3),
        lowStockThreshold: 5,
        trackInventory: true,
        isOutOfStock: getStoredStock('sample-product-2', 3) === 0,
        hasVariants: false,
        totalStock: getStoredStock('sample-product-2', 3),
        lastStockUpdate: new Date().toISOString(),
        images: [],
        categoryName: 'Jewelry',
        status: 'active'
      },
      'sample-product-3': {
        id: 'sample-product-3',
        name: 'Diamond Ring',
        sku: 'DIAMOND-RING-001',
        price: 25000,
        inventoryQuantity: getStoredStock('sample-product-3', 0),
        lowStockThreshold: 2,
        trackInventory: true,
        isOutOfStock: getStoredStock('sample-product-3', 0) === 0,
        hasVariants: false,
        totalStock: getStoredStock('sample-product-3', 0),
        lastStockUpdate: new Date().toISOString(),
        images: [],
        categoryName: 'Jewelry',
        status: 'active'
      },
      'sample-product-4': {
        id: 'sample-product-4',
        name: 'Pearl Bracelet',
        sku: 'PEARL-BRAC-001',
        price: 6500,
        inventoryQuantity: getStoredStock('sample-product-4', 8),
        lowStockThreshold: 10,
        trackInventory: true,
        isOutOfStock: getStoredStock('sample-product-4', 8) === 0,
        hasVariants: false,
        totalStock: getStoredStock('sample-product-4', 8),
        lastStockUpdate: new Date().toISOString(),
        images: [],
        categoryName: 'Jewelry',
        status: 'active'
      }
    }

    return sampleProducts[productId as keyof typeof sampleProducts] || null
  }

  // Get sample logs
  const getSampleLogs = (productId: string): InventoryLog[] => {
    return [
      {
        id: `log-${productId}-1`,
        productId,
        action: 'adjustment',
        type: 'increase',
        quantity: 5,
        previousQuantity: 10,
        newQuantity: 15,
        reason: 'restock',
        note: 'Weekly restock from supplier',
        timestamp: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        userId: 'admin',
        userName: 'Admin User'
      },
      {
        id: `log-${productId}-2`,
        productId,
        action: 'sale',
        type: 'decrease',
        quantity: 2,
        previousQuantity: 15,
        newQuantity: 13,
        reason: 'sale',
        note: 'Sold to customer',
        timestamp: new Date(Date.now() - 43200000).toISOString(), // 12 hours ago
        userId: 'admin',
        userName: 'Admin User'
      }
    ]
  }

  // Update low stock threshold
  const handleUpdateThreshold = async () => {
    if (!productId) return

    try {
      setUpdating(true)
      await inventoryService.updateLowStockThreshold(productId, lowStockThreshold, variantId)
      toast.success('Low stock threshold updated')
      await loadData() // Refresh data
    } catch (error) {
      console.error('Error updating threshold:', error)
      toast.error('Failed to update threshold')
    } finally {
      setUpdating(false)
    }
  }

  // Toggle inventory tracking
  const handleToggleTracking = async (enabled: boolean) => {
    if (!productId) return

    try {
      setUpdating(true)
      await inventoryService.toggleInventoryTracking(productId, enabled, variantId)
      setTrackInventory(enabled)
      toast.success(`Inventory tracking ${enabled ? 'enabled' : 'disabled'}`)
      await loadData() // Refresh data
    } catch (error) {
      console.error('Error toggling tracking:', error)
      toast.error('Failed to update inventory tracking')
    } finally {
      setUpdating(false)
    }
  }

  // Get current stock and item info
  const getCurrentInfo = () => {
    if (!product) return { stock: 0, name: '', sku: '', isOutOfStock: false }

    if (variantId && product.variants) {
      const variant = product.variants.find(v => v.id === variantId)
      return {
        stock: variant?.stock || 0,
        name: `${product.name} - ${variant?.name || 'Unknown Variant'}`,
        sku: variant?.sku || '',
        isOutOfStock: variant?.isOutOfStock || false
      }
    }

    return {
      stock: product.inventoryQuantity || 0,
      name: product.name,
      sku: product.sku || '',
      isOutOfStock: product.isOutOfStock || false
    }
  }

  // Get action icon
  const getActionIcon = (action: string) => {
    switch (action) {
      case 'restock':
      case 'adjustment':
        return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'sale':
        return <TrendingDown className="h-4 w-4 text-red-600" />
      case 'return':
        return <RotateCcw className="h-4 w-4 text-blue-600" />
      default:
        return <Package className="h-4 w-4 text-gray-600" />
    }
  }

  // Get stock status
  const getStockStatus = () => {
    const { stock, isOutOfStock } = getCurrentInfo()
    
    if (isOutOfStock) {
      return {
        badge: <Badge variant="destructive">Out of Stock</Badge>,
        icon: <XCircle className="h-5 w-5 text-red-500" />
      }
    } else if (stock <= lowStockThreshold) {
      return {
        badge: <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Low Stock</Badge>,
        icon: <AlertTriangle className="h-5 w-5 text-yellow-500" />
      }
    } else {
      return {
        badge: <Badge variant="default" className="bg-green-100 text-green-800">In Stock</Badge>,
        icon: <CheckCircle className="h-5 w-5 text-green-500" />
      }
    }
  }

  // Load data on mount
  useEffect(() => {
    loadData()
  }, [productId, variantId])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading inventory details...</p>
        </div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="text-center py-8">
        <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          Product not found
        </h3>
        <p className="text-gray-500 dark:text-gray-400 mb-4">
          The requested product could not be found.
        </p>
        <Button onClick={() => navigate('/admin/inventory')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Inventory
        </Button>
      </div>
    )
  }

  const { stock, name, sku, isOutOfStock } = getCurrentInfo()
  const { badge: statusBadge, icon: statusIcon } = getStockStatus()

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          onClick={() => navigate('/admin/inventory')}
          className="hover:bg-gray-100"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex-1">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
            Inventory Details
          </h1>
          <p className="text-gray-500 dark:text-gray-400">
            {name}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={() => navigate(`/admin/inventory/${productId}/adjust${variantId ? `?variant=${variantId}` : ''}`)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Edit className="h-4 w-4 mr-2" />
            Adjust Stock
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Product Info & Current Stock */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {statusIcon}
              Current Stock
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-3">
              {product.images?.[0] ? (
                <img 
                  src={product.images[0]} 
                  alt={product.name}
                  className="w-16 h-16 rounded-lg object-cover"
                />
              ) : (
                <div className="w-16 h-16 rounded-lg bg-gray-100 flex items-center justify-center">
                  <Package className="h-8 w-8 text-gray-400" />
                </div>
              )}
              <div className="flex-1">
                <div className="font-medium text-gray-900 dark:text-gray-100">
                  {name}
                </div>
                <div className="text-sm text-gray-500">
                  SKU: {sku}
                </div>
                {statusBadge}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 pt-4 border-t">
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                  {stock}
                </div>
                <div className="text-sm text-gray-500">Current Stock</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-600">
                  ≤ {lowStockThreshold}
                </div>
                <div className="text-sm text-gray-500">Low Stock Alert</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Inventory Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Track Inventory Toggle */}
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">Track Inventory</Label>
                <p className="text-xs text-gray-500">Enable stock tracking for this item</p>
              </div>
              <Switch
                checked={trackInventory}
                onCheckedChange={handleToggleTracking}
                disabled={updating}
              />
            </div>

            {/* Low Stock Threshold */}
            <div>
              <Label htmlFor="threshold" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Low Stock Threshold
              </Label>
              <div className="flex items-center gap-2 mt-1">
                <Input
                  id="threshold"
                  type="number"
                  min="0"
                  value={lowStockThreshold}
                  onChange={(e) => setLowStockThreshold(parseInt(e.target.value) || 0)}
                  className="flex-1"
                />
                <Button
                  size="sm"
                  onClick={handleUpdateThreshold}
                  disabled={updating}
                >
                  {updating ? 'Saving...' : 'Save'}
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Get notified when stock falls below this level
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {logs.length > 0 ? (
                logs.slice(0, 10).map((log) => (
                  <div key={log.id} className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="mt-0.5">
                      {getActionIcon(log.action)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 text-sm">
                        <span className="font-medium capitalize">{log.action}</span>
                        <span className={`font-semibold ${
                          log.quantity > 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {log.quantity > 0 ? '+' : ''}{log.quantity}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {log.previousStock} → {log.newStock}
                      </div>
                      <div className="flex items-center gap-2 text-xs text-gray-400 mt-1">
                        <User className="h-3 w-3" />
                        {log.userName}
                        <Clock className="h-3 w-3 ml-2" />
                        {formatDateTime(log.timestamp)}
                      </div>
                      {log.note && (
                        <div className="text-xs text-gray-600 mt-1 italic">
                          "{log.note}"
                        </div>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-gray-500">
                  <Clock className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm">No activity recorded yet</p>
                </div>
              )}
            </div>
            {logs.length > 10 && (
              <div className="text-center pt-3 border-t">
                <Button variant="ghost" size="sm">
                  View all {logs.length} entries
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
