import { useState, useEffect } from 'react'
import { useFirebaseAuthStore } from '../../store/firebase-auth'
import { useNotifications } from '../../hooks/useNotifications'

import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { cn } from '../../lib/utils'
import { useAutoClose } from '../../hooks/use-click-outside'
import {
  Bell,
  Search,
  Settings,
  User,
  LogOut,
  Menu,
  X,
  ChevronDown,
  Store,
  Palette,
  HelpCircle,
  Moon,
  Sun,
  Globe,
  Activity,
  MessageSquare
} from 'lucide-react'
import { Link } from 'react-router-dom'

export function Header() {
  const { user, signOut } = useFirebaseAuthStore()
  const { notifications, unreadCount, markAllAsRead } = useNotifications()
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  const [showHelp, setShowHelp] = useState(false)
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  // Auto-close functionality for dropdowns
  const userMenuRef = useAutoClose(() => setShowUserMenu(false), showUserMenu)
  const notificationsRef = useAutoClose(() => setShowNotifications(false), showNotifications)
  const helpRef = useAutoClose(() => setShowHelp(false), showHelp)

  // Mock notifications removed - now using realtime notifications from store

  const helpResources = [
    { title: 'Getting Started Guide', description: 'Learn the basics of managing your store', type: 'guide' },
    { title: 'Video Tutorials', description: 'Watch step-by-step video guides', type: 'video' },
    { title: 'API Documentation', description: 'Technical documentation for developers', type: 'docs' },
    { title: 'Contact Support', description: 'Get help from our support team', type: 'support' },
  ]

  // unreadCount now comes from realtime store

  // Initialize theme from localStorage
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme')
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches

    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
      setIsDarkMode(true)
      document.documentElement.classList.add('dark')
      document.body.classList.add('dark')
    } else {
      setIsDarkMode(false)
      document.documentElement.classList.remove('dark')
      document.body.classList.remove('dark')
    }
  }, [])

  const handleSignOut = async () => {
    await signOut()
  }

  const toggleTheme = () => {
    const newTheme = !isDarkMode
    setIsDarkMode(newTheme)

    // Toggle dark mode classes
    if (newTheme) {
      document.documentElement.classList.add('dark')
      document.body.classList.add('dark')
      localStorage.setItem('theme', 'dark')
    } else {
      document.documentElement.classList.remove('dark')
      document.body.classList.remove('dark')
      localStorage.setItem('theme', 'light')
    }

    // Force a re-render by updating a CSS custom property
    document.documentElement.style.setProperty('--theme-updated', Date.now().toString())
  }

  const handleViewStore = () => {
    // Open store in new tab
    window.open('https://womanza.com', '_blank')
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'order': return '🛒'
      case 'inventory': return '📦'
      case 'payment': return '💳'
      case 'customer': return '👤'
      case 'system': return '⚙️'
      default: return '🔔'
    }
  }

  return (
    <header className="bg-white dark:bg-gray-900 border-b border-primary-200 dark:border-primary-700 shadow-sm sticky top-0 z-50">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Left side - Enhanced Search */}
        <div className="flex items-center gap-4 flex-1">
          <div className="relative max-w-lg w-full">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Quick search across products, orders, customers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-gray-50 focus:bg-white transition-colors"
            />
            {searchQuery && (
              <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                <div className="p-2 text-sm text-gray-500">
                  Search results will appear here...
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right side - Enhanced Actions */}
        <div className="flex items-center gap-2">
          {/* Development Mode Indicator */}
          <div className="hidden lg:flex items-center gap-3">
            <Badge variant="outline" className="text-xs">
              Dev Mode
            </Badge>
          </div>

          {/* Quick Actions */}
          <Button
            variant="ghost"
            size="sm"
            className="hidden md:flex"
            onClick={handleViewStore}
          >
            <Store className="h-4 w-4 mr-2" />
            View Store
          </Button>

          {/* Theme Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleTheme}
            title={isDarkMode ? "Switch to Light Mode" : "Switch to Dark Mode"}
          >
            {isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
          </Button>

          {/* Help Button */}
          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowHelp(!showHelp)}
            >
              <HelpCircle className="h-4 w-4" />
            </Button>

            {/* Help Dropdown */}
            {showHelp && (
              <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                <div className="p-4 border-b border-gray-200">
                  <h3 className="font-semibold text-gray-900">Help & Support</h3>
                  <p className="text-sm text-gray-500">Get help and learn more about your admin panel</p>
                </div>
                <div className="p-2">
                  {helpResources.map((resource, index) => (
                    <button
                      key={index}
                      className="w-full text-left p-3 hover:bg-gray-50 rounded-lg transition-colors"
                    >
                      <div className="font-medium text-gray-900 text-sm">{resource.title}</div>
                      <div className="text-xs text-gray-500 mt-1">{resource.description}</div>
                    </button>
                  ))}
                </div>
                <div className="p-4 border-t border-gray-200 bg-gray-50">
                  <Button variant="outline" size="sm" className="w-full">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Contact Support
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Enhanced Notifications */}
          <div className="relative" ref={notificationsRef}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative"
            >
              <Bell className="h-5 w-5" />
              {unreadCount > 0 && (
                <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center text-xs bg-red-500 text-white animate-pulse">
                  {unreadCount}
                </Badge>
              )}
            </Button>

            {/* Enhanced Notifications Dropdown */}
            {showNotifications && (
              <div className="absolute right-0 mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50 theme-dropdown">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-gray-100">Notifications</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">{unreadCount} unread notifications</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-xs"
                        onClick={markAllAsRead}
                      >
                        Mark all read
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowNotifications(false)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Notification Filters */}
                <div className="p-3 border-b border-gray-100">
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm" className="text-xs">All</Button>
                    <Button variant="ghost" size="sm" className="text-xs">Orders</Button>
                    <Button variant="ghost" size="sm" className="text-xs">Inventory</Button>
                    <Button variant="ghost" size="sm" className="text-xs">System</Button>
                  </div>
                </div>

                <div className="max-h-96 overflow-y-auto">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={cn(
                        "p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors",
                        !notification.read && "bg-blue-50 border-l-4 border-l-blue-500"
                      )}
                    >
                      <div className="flex items-start gap-3">
                        <div className="text-lg">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <p className="font-medium text-gray-900 text-sm">
                              {notification.title}
                            </p>
                            {!notification.read && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full" />
                            )}
                          </div>
                          <p className="text-gray-600 text-sm mt-1">
                            {notification.message}
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <p className="text-gray-400 text-xs">
                              {new Date(notification.timestamp).toLocaleTimeString()}
                            </p>
                            <Badge variant="secondary" className="text-xs">
                              {notification.type}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="p-4 border-t border-gray-200 bg-gray-50">
                  <Button variant="outline" size="sm" className="w-full">
                    <Bell className="h-4 w-4 mr-2" />
                    View all notifications
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Enhanced User Menu */}
          <div className="relative" ref={userMenuRef}>
            <Button
              variant="ghost"
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center gap-3 px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <div className="w-9 h-9 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center ring-2 ring-white shadow-sm">
                <span className="text-sm font-medium text-white">
                  {user?.email?.charAt(0).toUpperCase() || 'U'}
                </span>
              </div>
              <div className="hidden md:block text-left">
                <p className="text-sm font-medium text-gray-900">
                  {user?.email?.split('@')[0] || 'User'}
                </p>
                <p className="text-xs text-gray-500">
                  {user?.role?.replace('_', ' ').toUpperCase() || 'USER'}
                </p>
              </div>
              <ChevronDown className="h-4 w-4 text-gray-400" />
            </Button>

            {/* Enhanced User Dropdown */}
            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-72 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50 theme-dropdown">
                {/* User Info Header */}
                <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                      <span className="text-lg font-medium text-white">
                        {user?.email?.charAt(0).toUpperCase() || 'U'}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-gray-100">{user?.email}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="secondary">
                          {user?.role?.replace('_', ' ').toUpperCase() || 'USER'}
                        </Badge>
                        <div className="flex items-center gap-1">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <span className="text-xs text-green-600 dark:text-green-400 font-medium">Active</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>



                {/* Menu Items */}
                <div className="py-2">
                  <Link
                    to="/admin/profile"
                    className="flex items-center gap-3 w-full px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    onClick={() => setShowUserMenu(false)}
                  >
                    <User className="h-4 w-4" />
                    <div className="text-left">
                      <div className="font-medium">Profile Settings</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">Manage your account</div>
                    </div>
                  </Link>
                  <button className="flex items-center gap-3 w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                    <Settings className="h-4 w-4" />
                    <div className="text-left">
                      <div className="font-medium">Preferences</div>
                      <div className="text-xs text-gray-500">Customize your experience</div>
                    </div>
                  </button>
                  <button className="flex items-center gap-3 w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                    <Globe className="h-4 w-4" />
                    <div className="text-left">
                      <div className="font-medium">Language & Region</div>
                      <div className="text-xs text-gray-500">English (US)</div>
                    </div>
                  </button>
                </div>

                {/* Sign Out */}
                <div className="border-t border-gray-200 p-2">
                  <button
                    onClick={handleSignOut}
                    className="flex items-center gap-3 w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                  >
                    <LogOut className="h-4 w-4" />
                    <div className="text-left">
                      <div className="font-medium">Sign Out</div>
                      <div className="text-xs text-red-500">End your session</div>
                    </div>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
