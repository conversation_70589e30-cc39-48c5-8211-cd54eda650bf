import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { InventoryService } from '../lib/inventory-service'
import { DataTable, Column } from '../components/ui/data-table'
import { Button } from '../components/ui/button'
import { Badge } from '../components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { formatCurrency } from '../lib/utils'
import {
  Package,
  AlertTriangle,
  TrendingDown,
  TrendingUp,
  RefreshCw,
  Settings,
  Plus,
  Minus,
  Edit,
  Eye,
  Warehouse,
  ShoppingCart,
  DollarSign,
  Activity
} from 'lucide-react'
import type { InventoryItem, InventoryStats, LowStockAlert } from '../types/inventory'

export default function InventoryPage() {
  const navigate = useNavigate()
  const { user } = useFirebaseAuthStore()

  // State
  const [items, setItems] = useState<InventoryItem[]>([])
  const [stats, setStats] = useState<InventoryStats | null>(null)
  const [alerts, setAlerts] = useState<LowStockAlert[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  // Initialize service
  const storeId = user?.storeIds?.[0] || 'womanza-jewelry-store'
  const inventoryService = new InventoryService(storeId)

  // Load inventory data
  const loadInventoryData = async () => {
    try {
      console.log('🔄 Loading inventory data...')
      setLoading(true)
      const data = await inventoryService.getInventoryOverview()
      console.log('📊 Inventory data loaded:', data)
      setItems(data.items)
      setStats(data.stats)
      setAlerts(data.alerts)
    } catch (error) {
      console.error('❌ Error loading inventory:', error)
      toast.error('Failed to load inventory data', {
        style: {
          background: '#EF4444',
          color: 'white',
        }
      })
    } finally {
      setLoading(false)
    }
  }

  // Refresh data
  const handleRefresh = async () => {
    try {
      setRefreshing(true)
      await loadInventoryData()
      toast.success('Inventory data refreshed', {
        style: {
          background: '#10B981',
          color: 'white',
        }
      })
    } catch (error) {
      toast.error('Failed to refresh data', {
        style: {
          background: '#EF4444',
          color: 'white',
        }
      })
    } finally {
      setRefreshing(false)
    }
  }

  // Load data on mount
  useEffect(() => {
    loadInventoryData()
  }, [])

  // Initialize sample inventory data
  const handleInitializeSampleData = async () => {
    toast.info('Sample data feature will be available when you add products with inventory tracking enabled.')
    navigate('/admin/products/new')
  }



  // Get stock status badge
  const getStockStatusBadge = (item: InventoryItem) => {
    if (item.isOutOfStock) {
      return (
        <Badge variant="destructive" className="bg-red-100 text-red-800 border-red-200">
          Out of Stock
        </Badge>
      )
    } else if (item.currentStock <= item.lowStockThreshold) {
      return (
        <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-200">
          Low Stock
        </Badge>
      )
    } else {
      return (
        <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
          In Stock
        </Badge>
      )
    }
  }

  // Table columns
  const columns: Column<InventoryItem>[] = [
    {
      key: 'product',
      title: 'Product',
      sortable: true,
      render: (_, item) => (
        <div className="flex items-center gap-3">
          {item.image ? (
            <img 
              src={item.image} 
              alt={item.name}
              className="w-10 h-10 rounded-lg object-cover"
            />
          ) : (
            <div className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center">
              <Package className="h-5 w-5 text-gray-400" />
            </div>
          )}
          <div>
            <div className="font-medium text-gray-900 dark:text-gray-100">
              {item.name}
            </div>
            <div className="text-sm text-gray-500">
              SKU: {item.sku}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'category',
      title: 'Category',
      sortable: true,
      render: (_, item) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {item.category || 'Uncategorized'}
        </span>
      )
    },
    {
      key: 'currentStock',
      title: 'Stock',
      sortable: true,
      render: (_, item) => (
        <div className="text-center">
          <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {item.currentStock}
          </div>
          <div className="text-xs text-gray-500">
            {item.unit}
          </div>
        </div>
      )
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      render: (_, item) => getStockStatusBadge(item)
    },
    {
      key: 'value',
      title: 'Value',
      sortable: true,
      render: (_, item) => (
        <div className="text-right">
          <div className="font-medium text-gray-900 dark:text-gray-100">
            {formatCurrency(item.currentStock * item.price)}
          </div>
          <div className="text-xs text-gray-500">
            @ {formatCurrency(item.price)}
          </div>
        </div>
      )
    },
    {
      key: 'threshold',
      title: 'Low Stock Alert',
      sortable: true,
      render: (_, item) => (
        <div className="text-center text-sm text-gray-600 dark:text-gray-400">
          ≤ {item.lowStockThreshold}
        </div>
      )
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (_, item) => (
        <div className="flex items-center gap-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => navigate(`/admin/inventory/${item.productId}${item.variantId ? `/${item.variantId}` : ''}`)}
            className="h-8 w-8 p-0"
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => navigate(`/admin/inventory/${item.productId}/adjust${item.variantId ? `?variant=${item.variantId}` : ''}`)}
            className="h-8 w-8 p-0"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => navigate(`/admin/products/${item.productId}/edit`)}
            className="h-8 w-8 p-0"
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ]

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
            Inventory Management
          </h1>
          <p className="text-gray-500 dark:text-gray-400">
            Track and manage your product stock levels
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            variant="outline"
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          {items.length === 0 && (
            <Button
              onClick={handleInitializeSampleData}
              variant="outline"
              disabled={loading}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Sample Data
            </Button>
          )}
          <Button
            onClick={() => navigate('/admin/inventory/bulk-adjust')}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Edit className="h-4 w-4 mr-2" />
            Bulk Adjust
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-blue-900 dark:text-blue-100">
                Total Products
              </CardTitle>
              <div className="p-2 bg-blue-100 dark:bg-blue-800/50 rounded-lg">
                <Package className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                {stats.totalProducts}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border-purple-200 dark:border-purple-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-purple-900 dark:text-purple-100">
                Total Variants
              </CardTitle>
              <div className="p-2 bg-purple-100 dark:bg-purple-800/50 rounded-lg">
                <Warehouse className="h-4 w-4 text-purple-600 dark:text-purple-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                {stats.totalVariants}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 border-yellow-200 dark:border-yellow-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-yellow-900 dark:text-yellow-100">
                Low Stock Items
              </CardTitle>
              <div className="p-2 bg-yellow-100 dark:bg-yellow-800/50 rounded-lg">
                <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                {stats.lowStockItems}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 border-red-200 dark:border-red-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-red-900 dark:text-red-100">
                Out of Stock
              </CardTitle>
              <div className="p-2 bg-red-100 dark:bg-red-800/50 rounded-lg">
                <TrendingDown className="h-4 w-4 text-red-600 dark:text-red-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-900 dark:text-red-100">
                {stats.outOfStockItems}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-green-900 dark:text-green-100">
                Total Value
              </CardTitle>
              <div className="p-2 bg-green-100 dark:bg-green-800/50 rounded-lg">
                <DollarSign className="h-4 w-4 text-green-600 dark:text-green-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                {formatCurrency(stats.totalValue)}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20 border-indigo-200 dark:border-indigo-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-indigo-900 dark:text-indigo-100">
                Recent Changes
              </CardTitle>
              <div className="p-2 bg-indigo-100 dark:bg-indigo-800/50 rounded-lg">
                <Activity className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-indigo-900 dark:text-indigo-100">
                {stats.recentAdjustments}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Low Stock Alerts */}
      {alerts.length > 0 && (
        <Card className="border-yellow-200 bg-yellow-50 dark:bg-yellow-900/10">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-800 dark:text-yellow-200">
              <AlertTriangle className="h-5 w-5" />
              Low Stock Alerts ({alerts.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {alerts.slice(0, 5).map((alert) => (
                <div key={alert.id} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg border">
                  <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100">
                      {alert.productName}
                    </div>
                    <div className="text-sm text-gray-500">
                      SKU: {alert.sku} • Stock: {alert.currentStock} • Threshold: {alert.threshold}
                    </div>
                  </div>
                  <Button
                    size="sm"
                    onClick={() => navigate(`/admin/inventory/${alert.productId}/adjust${alert.variantId ? `?variant=${alert.variantId}` : ''}`)}
                  >
                    Restock
                  </Button>
                </div>
              ))}
              {alerts.length > 5 && (
                <div className="text-center pt-2">
                  <Button variant="ghost" size="sm">
                    View all {alerts.length} alerts
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Inventory Table */}
      <DataTable
        data={items}
        columns={columns}
        loading={loading}
        searchable={true}
        filterable={true}
        exportable={true}
        emptyState={{
          title: 'No inventory items found',
          description: 'Start by adding products with inventory tracking enabled.',
          icon: <Package className="h-6 w-6 text-gray-400" />
        }}
      />
    </div>
  )
}
