import {
  collection,
  doc,
  getDocs,
  getDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  serverTimestamp,
  writeBatch
} from 'firebase/firestore'
import { db, isFirebaseConfigured } from './firebase'
import { settingsService } from './settings-service'

interface StoreCleanupResult {
  duplicatesFound: number
  duplicatesRemoved: number
  settingsInitialized: number
  errors: string[]
}

class StoreCleanupService {
  /**
   * Analyze store structure and find duplicates
   */
  async analyzeStores(): Promise<{
    stores: Array<{ id: string; name: string; hasSettings: boolean; hasUsers: boolean }>
    duplicates: Array<{ primary: string; duplicates: string[] }>
  }> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      console.log('🔍 Analyzing store structure...')
      
      const storesRef = collection(db, 'stores')
      const storesSnapshot = await getDocs(storesRef)
      
      const stores = []
      const storesByName = new Map<string, string[]>()
      
      for (const storeDoc of storesSnapshot.docs) {
        const storeData = storeDoc.data()
        const storeId = storeDoc.id
        
        // Check if store has settings
        const settingsDoc = await getDoc(doc(db, 'stores', storeId, 'settings', 'general'))
        const hasSettings = settingsDoc.exists()
        
        // Check if store has users
        const usersSnapshot = await getDocs(collection(db, 'stores', storeId, 'users'))
        const hasUsers = !usersSnapshot.empty
        
        stores.push({
          id: storeId,
          name: storeData.name || 'Unnamed Store',
          hasSettings,
          hasUsers
        })
        
        // Group by name to find duplicates
        const storeName = storeData.name || 'Unnamed Store'
        if (!storesByName.has(storeName)) {
          storesByName.set(storeName, [])
        }
        storesByName.get(storeName)!.push(storeId)
      }
      
      // Find duplicates
      const duplicates = []
      for (const [name, storeIds] of storesByName.entries()) {
        if (storeIds.length > 1) {
          // Choose primary store (prefer one with settings and users)
          const storesWithData = stores.filter(s => storeIds.includes(s.id))
          const primary = storesWithData.find(s => s.hasSettings && s.hasUsers) ||
                         storesWithData.find(s => s.hasSettings) ||
                         storesWithData.find(s => s.hasUsers) ||
                         storesWithData[0]
          
          duplicates.push({
            primary: primary.id,
            duplicates: storeIds.filter(id => id !== primary.id)
          })
        }
      }
      
      return { stores, duplicates }
    } catch (error) {
      console.error('❌ Error analyzing stores:', error)
      throw new Error('Failed to analyze store structure')
    }
  }

  /**
   * Clean up duplicate stores
   */
  async cleanupDuplicates(dryRun: boolean = true): Promise<StoreCleanupResult> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    const result: StoreCleanupResult = {
      duplicatesFound: 0,
      duplicatesRemoved: 0,
      settingsInitialized: 0,
      errors: []
    }

    try {
      console.log('🧹 Starting store cleanup...', dryRun ? '(DRY RUN)' : '(LIVE)')
      
      const analysis = await this.analyzeStores()
      result.duplicatesFound = analysis.duplicates.length

      if (dryRun) {
        console.log('📋 DRY RUN - Would perform the following actions:')
        for (const duplicate of analysis.duplicates) {
          console.log(`- Keep: ${duplicate.primary}`)
          console.log(`- Remove: ${duplicate.duplicates.join(', ')}`)
        }
        return result
      }

      // Perform actual cleanup
      const batch = writeBatch(db)
      
      for (const duplicate of analysis.duplicates) {
        try {
          // Merge data from duplicates into primary store
          const primaryRef = doc(db, 'stores', duplicate.primary)
          const primaryDoc = await getDoc(primaryRef)
          let primaryData = primaryDoc.data() || {}

          // Collect data from duplicates
          for (const duplicateId of duplicate.duplicates) {
            const duplicateRef = doc(db, 'stores', duplicateId)
            const duplicateDoc = await getDoc(duplicateRef)
            
            if (duplicateDoc.exists()) {
              const duplicateData = duplicateDoc.data()
              
              // Merge non-conflicting data
              for (const [key, value] of Object.entries(duplicateData)) {
                if (!primaryData[key] && value) {
                  primaryData[key] = value
                }
              }
              
              // Schedule duplicate for deletion
              batch.delete(duplicateRef)
              result.duplicatesRemoved++
            }
          }

          // Update primary store with merged data
          batch.update(primaryRef, {
            ...primaryData,
            updatedAt: serverTimestamp()
          })

          console.log(`✅ Merged ${duplicate.duplicates.length} duplicates into ${duplicate.primary}`)
        } catch (error) {
          const errorMsg = `Failed to cleanup duplicate ${duplicate.primary}: ${error.message}`
          console.error('❌', errorMsg)
          result.errors.push(errorMsg)
        }
      }

      // Commit batch operations
      if (result.duplicatesRemoved > 0) {
        await batch.commit()
        console.log(`✅ Removed ${result.duplicatesRemoved} duplicate stores`)
      }

      // Initialize settings for stores that don't have them
      for (const store of analysis.stores) {
        if (!store.hasSettings) {
          try {
            await settingsService.initializeSettings(store.id, store.name)
            result.settingsInitialized++
            console.log(`✅ Initialized settings for store: ${store.id}`)
          } catch (error) {
            const errorMsg = `Failed to initialize settings for ${store.id}: ${error.message}`
            console.error('❌', errorMsg)
            result.errors.push(errorMsg)
          }
        }
      }

      console.log('🎉 Store cleanup completed successfully')
      return result
    } catch (error) {
      console.error('❌ Error during store cleanup:', error)
      result.errors.push(error.message)
      return result
    }
  }

  /**
   * Ensure proper store structure for a specific store
   */
  async ensureStoreStructure(storeId: string): Promise<void> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      console.log('🔧 Ensuring proper structure for store:', storeId)
      
      // Check if main store document exists
      const storeRef = doc(db, 'stores', storeId)
      const storeDoc = await getDoc(storeRef)
      
      if (!storeDoc.exists()) {
        throw new Error(`Store document ${storeId} does not exist`)
      }

      const storeData = storeDoc.data()
      
      // Ensure settings subcollection exists
      const settingsRef = doc(db, 'stores', storeId, 'settings', 'general')
      const settingsDoc = await getDoc(settingsRef)
      
      if (!settingsDoc.exists()) {
        console.log('📝 Creating settings document for store:', storeId)
        await settingsService.initializeSettings(storeId, storeData.name)
      }

      // Ensure required collections exist
      const requiredCollections = ['users', 'products', 'orders', 'categories']
      
      for (const collectionName of requiredCollections) {
        const collectionRef = collection(db, 'stores', storeId, collectionName)
        const snapshot = await getDocs(query(collectionRef))
        
        if (snapshot.empty && collectionName === 'users') {
          // Create a placeholder document to ensure collection exists
          const placeholderRef = doc(collectionRef, '_placeholder')
          await setDoc(placeholderRef, {
            _placeholder: true,
            created_at: serverTimestamp()
          })
          console.log(`✅ Created ${collectionName} collection for store: ${storeId}`)
        }
      }

      console.log('✅ Store structure ensured for:', storeId)
    } catch (error) {
      console.error('❌ Error ensuring store structure:', error)
      throw new Error(`Failed to ensure store structure: ${error.message}`)
    }
  }

  /**
   * Get store health report
   */
  async getStoreHealthReport(storeId: string): Promise<{
    storeExists: boolean
    hasSettings: boolean
    hasUsers: boolean
    hasProducts: boolean
    settingsFieldCount: number
    issues: string[]
    recommendations: string[]
  }> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    const report = {
      storeExists: false,
      hasSettings: false,
      hasUsers: false,
      hasProducts: false,
      settingsFieldCount: 0,
      issues: [],
      recommendations: []
    }

    try {
      // Check main store document
      const storeDoc = await getDoc(doc(db, 'stores', storeId))
      report.storeExists = storeDoc.exists()
      
      if (!report.storeExists) {
        report.issues.push('Main store document does not exist')
        return report
      }

      // Check settings
      const settingsDoc = await getDoc(doc(db, 'stores', storeId, 'settings', 'general'))
      report.hasSettings = settingsDoc.exists()
      
      if (report.hasSettings) {
        report.settingsFieldCount = Object.keys(settingsDoc.data() || {}).length
      } else {
        report.issues.push('Settings document missing')
        report.recommendations.push('Initialize store settings')
      }

      // Check users
      const usersSnapshot = await getDocs(collection(db, 'stores', storeId, 'users'))
      report.hasUsers = !usersSnapshot.empty

      if (!report.hasUsers) {
        report.issues.push('No users found in store')
        report.recommendations.push('Add store users')
      }

      // Check products
      const productsSnapshot = await getDocs(collection(db, 'stores', storeId, 'products'))
      report.hasProducts = !productsSnapshot.empty

      if (!report.hasProducts) {
        report.recommendations.push('Add products to store')
      }

      // Additional checks
      if (report.settingsFieldCount < 50) {
        report.issues.push('Settings document appears incomplete')
        report.recommendations.push('Reinitialize settings with full schema')
      }

      console.log('📊 Store health report generated for:', storeId)
      return report
    } catch (error) {
      console.error('❌ Error generating store health report:', error)
      report.issues.push(`Error generating report: ${error.message}`)
      return report
    }
  }
}

export const storeCleanupService = new StoreCleanupService()
