import { create } from 'zustand'
import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore'
import { db } from '../lib/firebase'
import { useStoreManagement } from './store-management'

export interface Product {
  id: string
  name: string
  description: string
  price: number
  compareAtPrice?: number
  cost?: number
  sku: string
  barcode?: string
  trackQuantity: boolean
  quantity: number
  lowStockThreshold: number
  category: string
  tags: string[]
  images: string[]
  weight?: number
  dimensions?: {
    length: number
    width: number
    height: number
  }
  status: 'active' | 'draft' | 'archived'
  vendor?: string
  type?: string
  seoTitle?: string
  seoDescription?: string
  storeId: string
  createdAt: string
  updatedAt: string
  createdBy: string
}

interface ProductsState {
  products: Product[]
  loading: boolean
  error: string | null
  // Actions
  fetchProducts: () => Promise<void>
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => Promise<string>
  updateProduct: (id: string, updates: Partial<Product>) => Promise<void>
  deleteProduct: (id: string) => Promise<void>
  getProduct: (id: string) => Promise<Product | null>
  // Real-time subscription
  subscribeToProducts: () => () => void
  clearError: () => void
}

export const useProductsStore = create<ProductsState>((set, get) => ({
  products: [],
  loading: false,
  error: null,

  clearError: () => set({ error: null }),

  fetchProducts: async () => {
    const { currentStoreId } = useStoreManagement.getState()

    if (!currentStoreId) {
      set({ products: [], loading: false, error: 'No store selected' })
      return
    }

    set({ loading: true, error: null })
    try {
      // Use store-specific products collection
      const q = query(
        collection(db, 'stores', currentStoreId, 'products'),
        orderBy('createdAt', 'desc')
      )

      const querySnapshot = await getDocs(q)
      const products: Product[] = []

      querySnapshot.forEach((doc) => {
        const data = doc.data()

        // Map Firebase data to our Product interface
        products.push({
          id: doc.id,
          name: data.name || 'Unnamed Product',
          description: data.description || data.careInstructions || '',
          price: data.price || 0,
          compareAtPrice: data.comparePrice || undefined,
          cost: data.cost || undefined,
          sku: data.sku || doc.id,
          barcode: data.barcode || undefined,
          trackQuantity: data.trackQuantity ?? true,
          quantity: data.quantity || 0,
          lowStockThreshold: data.lowStockThreshold || 10,
          category: data.categoryName || data.category || 'Uncategorized',
          tags: data.tags || [],
          images: data.images || [],
          weight: data.caratWeight || data.weight || undefined,
          dimensions: data.dimensions || undefined,
          status: data.status || 'active',
          vendor: data.vendor || undefined,
          type: data.type || undefined,
          seoTitle: data.seoTitle || undefined,
          seoDescription: data.seoDescription || undefined,
          storeId: currentStoreId,
          createdAt: data.createdAt?.toDate?.()?.toISOString() || new Date().toISOString(),
          updatedAt: data.updatedAt?.toDate?.()?.toISOString() || new Date().toISOString(),
        } as Product)
      })

      set({ products, loading: false })
    } catch (error: any) {
      console.error('Error fetching products:', error)
      set({ error: error.message, loading: false })
    }
  },

  addProduct: async (productData) => {
    const { currentStoreId } = useStoreManagement.getState()

    if (!currentStoreId) {
      set({ error: 'No store selected' })
      return
    }

    set({ error: null })
    try {
      const newProduct = {
        ...productData,
        storeId: currentStoreId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      }

      const docRef = await addDoc(collection(db, 'stores', currentStoreId, 'products'), newProduct)
      
      // Add to local state immediately for optimistic updates
      const optimisticProduct: Product = {
        ...productData,
        id: docRef.id,
        storeId: currentStoreId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      set(state => ({
        products: [optimisticProduct, ...state.products]
      }))
      
      return docRef.id
    } catch (error: any) {
      console.error('Error adding product:', error)
      set({ error: error.message })
      throw error
    }
  },

  updateProduct: async (id, updates) => {
    const { currentStoreId } = useStoreManagement.getState()

    if (!currentStoreId) {
      set({ error: 'No store selected' })
      return
    }

    set({ error: null })
    try {
      const updateData = {
        ...updates,
        updatedAt: serverTimestamp()
      }

      await updateDoc(doc(db, 'stores', currentStoreId, 'products', id), updateData)
      
      // Update local state immediately for optimistic updates
      set(state => ({
        products: state.products.map(product =>
          product.id === id
            ? { ...product, ...updates, updatedAt: new Date().toISOString() }
            : product
        )
      }))
    } catch (error: any) {
      console.error('Error updating product:', error)
      set({ error: error.message })
      throw error
    }
  },

  deleteProduct: async (id) => {
    const { currentStoreId } = useStoreManagement.getState()

    if (!currentStoreId) {
      set({ error: 'No store selected' })
      return
    }

    set({ error: null })
    try {
      await deleteDoc(doc(db, 'stores', currentStoreId, 'products', id))
      
      // Remove from local state immediately
      set(state => ({
        products: state.products.filter(product => product.id !== id)
      }))
    } catch (error: any) {
      console.error('Error deleting product:', error)
      set({ error: error.message })
      throw error
    }
  },

  getProduct: async (id) => {
    const { currentStoreId } = useStoreManagement.getState()

    if (!currentStoreId) {
      set({ error: 'No store selected' })
      return null
    }

    try {
      const docSnap = await getDoc(doc(db, 'stores', currentStoreId, 'products', id))

      if (docSnap.exists()) {
        const data = docSnap.data()
        return {
          id: docSnap.id,
          name: data.name || 'Unnamed Product',
          description: data.description || data.careInstructions || '',
          price: data.price || 0,
          compareAtPrice: data.comparePrice || undefined,
          cost: data.cost || undefined,
          sku: data.sku || docSnap.id,
          barcode: data.barcode || undefined,
          trackQuantity: data.trackQuantity ?? true,
          quantity: data.quantity || 0,
          lowStockThreshold: data.lowStockThreshold || 10,
          category: data.categoryName || data.category || 'Uncategorized',
          tags: data.tags || [],
          images: data.images || [],
          weight: data.caratWeight || data.weight || undefined,
          dimensions: data.dimensions || undefined,
          status: data.status || 'active',
          vendor: data.vendor || undefined,
          type: data.type || undefined,
          seoTitle: data.seoTitle || undefined,
          seoDescription: data.seoDescription || undefined,
          storeId: STORE_ID,
          createdAt: data.createdAt?.toDate?.()?.toISOString() || new Date().toISOString(),
          updatedAt: data.updatedAt?.toDate?.()?.toISOString() || new Date().toISOString(),
        } as Product
      }

      return null
    } catch (error: any) {
      console.error('Error getting product:', error)
      set({ error: error.message })
      return null
    }
  },

  subscribeToProducts: () => {
    const { currentStoreId } = useStoreManagement.getState()

    if (!currentStoreId) {
      console.warn('No store selected for products subscription')
      return () => {}
    }

    const q = query(
      collection(db, 'stores', currentStoreId, 'products'),
      orderBy('createdAt', 'desc')
    )

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const products: Product[] = []

      querySnapshot.forEach((doc) => {
        const data = doc.data()
        products.push({
          id: doc.id,
          name: data.name || 'Unnamed Product',
          description: data.description || data.careInstructions || '',
          price: data.price || 0,
          compareAtPrice: data.comparePrice || undefined,
          cost: data.cost || undefined,
          sku: data.sku || doc.id,
          barcode: data.barcode || undefined,
          trackQuantity: data.trackQuantity ?? true,
          quantity: data.quantity || 0,
          lowStockThreshold: data.lowStockThreshold || 10,
          category: data.categoryName || data.category || 'Uncategorized',
          tags: data.tags || [],
          images: data.images || [],
          weight: data.caratWeight || data.weight || undefined,
          dimensions: data.dimensions || undefined,
          status: data.status || 'active',
          vendor: data.vendor || undefined,
          type: data.type || undefined,
          seoTitle: data.seoTitle || undefined,
          seoDescription: data.seoDescription || undefined,
          storeId: currentStoreId,
          createdAt: data.createdAt?.toDate?.()?.toISOString() || new Date().toISOString(),
          updatedAt: data.updatedAt?.toDate?.()?.toISOString() || new Date().toISOString(),
        } as Product)
      })

      set({ products, loading: false })
    }, (error) => {
      console.error('Error in products subscription:', error)
      set({ error: error.message, loading: false })
    })

    return unsubscribe
  }
}))

// Auto-fetch products on store creation
useProductsStore.getState().fetchProducts()
