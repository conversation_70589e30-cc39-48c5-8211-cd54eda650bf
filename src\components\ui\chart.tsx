import React, { useMemo } from 'react'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import { formatCurrency, formatNumber } from '../../lib/utils'

interface ChartProps {
  data: any[]
  height?: number
  className?: string
}

interface SalesChartProps extends ChartProps {
  dataKey?: string
  strokeColor?: string
  fillColor?: string
}

export function SalesChart({
  data,
  height = 300,
  className = "",
  dataKey = "revenue",
  strokeColor = "#3b82f6",
  fillColor = "#3b82f6"
}: SalesChartProps) {
  // Memoize data to prevent unnecessary re-renders
  const memoizedData = useMemo(() => data, [JSON.stringify(data)])

  const formatTooltipValue = useMemo(() => (value: number, name: string) => {
    if (name === 'revenue') {
      return [formatCurrency(value), 'Revenue']
    }
    return [formatNumber(value), name]
  }, [])

  const tooltipStyle = useMemo(() => ({
    backgroundColor: '#ffffff',
    border: '1px solid #e5e7eb',
    borderRadius: '6px'
  }), [])

  return (
    <div className={className}>
      <ResponsiveContainer width="100%" height={height}>
        <AreaChart data={memoizedData}>
          <defs>
            <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={fillColor} stopOpacity={0.3}/>
              <stop offset="95%" stopColor={fillColor} stopOpacity={0}/>
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
          <XAxis
            dataKey="date"
            tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
            fontSize={12}
          />
          <YAxis
            tickFormatter={(value) => dataKey === 'revenue' ? formatCurrency(value) : formatNumber(value)}
            fontSize={12}
          />
          <Tooltip
            formatter={formatTooltipValue}
            labelFormatter={(value) => new Date(value).toLocaleDateString('en-US', {
              weekday: 'short',
              month: 'short',
              day: 'numeric'
            })}
            contentStyle={tooltipStyle}
          />
          <Area
            type="monotone"
            dataKey={dataKey}
            stroke={strokeColor}
            fillOpacity={1}
            fill="url(#colorRevenue)"
            strokeWidth={2}
            isAnimationActive={false}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  )
}

export function OrdersChart({
  data,
  height = 300,
  className = "",
  strokeColor = "#10b981"
}: ChartProps & { strokeColor?: string }) {
  // Memoize data to prevent unnecessary re-renders
  const memoizedData = useMemo(() => data, [JSON.stringify(data)])

  const tooltipStyle = useMemo(() => ({
    backgroundColor: '#ffffff',
    border: '1px solid #e5e7eb',
    borderRadius: '6px'
  }), [])

  const dotStyle = useMemo(() => ({
    fill: strokeColor,
    strokeWidth: 2,
    r: 4
  }), [strokeColor])

  const activeDotStyle = useMemo(() => ({ r: 6 }), [])

  return (
    <div className={className}>
      <ResponsiveContainer width="100%" height={height}>
        <LineChart data={memoizedData}>
          <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
          <XAxis
            dataKey="date"
            tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
            fontSize={12}
          />
          <YAxis
            tickFormatter={formatNumber}
            fontSize={12}
          />
          <Tooltip
            formatter={(value: number) => [formatNumber(value), 'Orders']}
            labelFormatter={(value) => new Date(value).toLocaleDateString('en-US', {
              weekday: 'short',
              month: 'short',
              day: 'numeric'
            })}
            contentStyle={tooltipStyle}
          />
          <Line
            type="monotone"
            dataKey="orders"
            stroke={strokeColor}
            strokeWidth={2}
            dot={dotStyle}
            activeDot={activeDotStyle}
            isAnimationActive={false}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  )
}

interface ProductSalesChartProps extends ChartProps {
  products: Array<{
    name: string
    sales_count: number
    revenue: number
  }>
}

export function ProductSalesChart({ 
  products, 
  height = 300, 
  className = ""
}: ProductSalesChartProps) {
  const COLORS = useMemo(() => ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'], [])

  const chartData = useMemo(() =>
    products.map((product, index) => ({
      ...product,
      fill: COLORS[index % COLORS.length]
    })), [products, COLORS]
  )

  return (
    <div className={className}>
      <ResponsiveContainer width="100%" height={height}>
        <BarChart data={chartData} layout="horizontal">
          <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
          <XAxis
            type="number"
            tickFormatter={formatNumber}
            fontSize={12}
          />
          <YAxis
            type="category"
            dataKey="name"
            width={100}
            fontSize={12}
            tickFormatter={(value) => value.length > 15 ? value.substring(0, 15) + '...' : value}
          />
          <Tooltip
            formatter={(value: number, name: string) => {
              if (name === 'revenue') return [formatCurrency(value), 'Revenue']
              if (name === 'sales_count') return [formatNumber(value), 'Units Sold']
              return [value, name]
            }}
            contentStyle={{
              backgroundColor: '#ffffff',
              border: '1px solid #e5e7eb',
              borderRadius: '6px'
            }}
          />
          <Bar
            dataKey="revenue"
            fill="#3b82f6"
            radius={[0, 4, 4, 0]}
            isAnimationActive={false}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}

interface StatusDistributionProps {
  data: Array<{
    name: string
    value: number
    color: string
  }>
  height?: number
  className?: string
}

export function StatusDistributionChart({
  data,
  height = 300,
  className = ""
}: StatusDistributionProps) {
  // Memoize data to prevent unnecessary re-renders
  const memoizedData = useMemo(() => data, [JSON.stringify(data)])

  const renderCustomizedLabel = useMemo(() => ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (percent < 0.05) return null // Don't show labels for slices smaller than 5%

    const RADIAN = Math.PI / 180
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5
    const x = cx + radius * Math.cos(-midAngle * RADIAN)
    const y = cy + radius * Math.sin(-midAngle * RADIAN)

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        fontSize={12}
        fontWeight="medium"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    )
  }, [])

  const tooltipStyle = useMemo(() => ({
    backgroundColor: '#ffffff',
    border: '1px solid #e5e7eb',
    borderRadius: '6px'
  }), [])

  return (
    <div className={className}>
      <ResponsiveContainer width="100%" height={height}>
        <PieChart>
          <Pie
            data={memoizedData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={renderCustomizedLabel}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
            isAnimationActive={false}
          >
            {memoizedData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip
            formatter={(value: number) => [formatNumber(value), 'Count']}
            contentStyle={tooltipStyle}
          />
        </PieChart>
      </ResponsiveContainer>
    </div>
  )
}
