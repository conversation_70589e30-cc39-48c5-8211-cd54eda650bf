import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Alert, AlertDescription } from '../ui/alert'
import { storeCleanupService } from '../../lib/store-cleanup-service'
import { analyzeFirebaseStores, cleanupFirebaseStores } from '../../utils/firebase-cleanup-utility'
import { useFirebaseAuthStore } from '../../store/firebase-auth'
import { toast } from 'react-hot-toast'
import {
  Database,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Trash2,
  Settings,
  Users,
  Package,
  TrendingUp,
  Shield
} from 'lucide-react'

interface StoreHealthPanelProps {
  storeId: string
}

export function StoreHealthPanel({ storeId }: StoreHealthPanelProps) {
  const { user } = useFirebaseAuthStore()
  const [healthReport, setHealthReport] = useState<any>(null)
  const [storeAnalysis, setStoreAnalysis] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [cleanupLoading, setCleanupLoading] = useState(false)

  // Only show for super admins
  if (user?.role !== 'super_admin') {
    return null
  }

  const loadHealthReport = async () => {
    try {
      setLoading(true)
      const report = await storeCleanupService.getStoreHealthReport(storeId)
      setHealthReport(report)
    } catch (error) {
      console.error('Failed to load health report:', error)
      toast.error('Failed to load store health report')
    } finally {
      setLoading(false)
    }
  }

  const loadStoreAnalysis = async () => {
    try {
      setLoading(true)
      const analysis = await storeCleanupService.analyzeStores()
      setStoreAnalysis(analysis)
    } catch (error) {
      console.error('Failed to analyze stores:', error)
      toast.error('Failed to analyze store structure')
    } finally {
      setLoading(false)
    }
  }

  const performCleanup = async (dryRun: boolean = true) => {
    try {
      setCleanupLoading(true)
      const result = await storeCleanupService.cleanupDuplicates(dryRun)
      
      if (dryRun) {
        toast.success(`Analysis complete: ${result.duplicatesFound} duplicates found`)
      } else {
        toast.success(`Cleanup complete: ${result.duplicatesRemoved} duplicates removed, ${result.settingsInitialized} settings initialized`)
        // Reload data after cleanup
        await loadHealthReport()
        await loadStoreAnalysis()
      }
    } catch (error) {
      console.error('Cleanup failed:', error)
      toast.error('Store cleanup failed')
    } finally {
      setCleanupLoading(false)
    }
  }

  const ensureStoreStructure = async () => {
    try {
      setLoading(true)
      await storeCleanupService.ensureStoreStructure(storeId)
      toast.success('Store structure ensured successfully')
      await loadHealthReport()
    } catch (error) {
      console.error('Failed to ensure store structure:', error)
      toast.error('Failed to ensure store structure')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadHealthReport()
    loadStoreAnalysis()
  }, [storeId])

  const getHealthIcon = (healthy: boolean) => {
    return healthy ? (
      <CheckCircle className="h-4 w-4 text-green-600" />
    ) : (
      <AlertTriangle className="h-4 w-4 text-red-600" />
    )
  }

  return (
    <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Store Health & Management
        </CardTitle>
        <CardDescription>
          Monitor and maintain store data integrity (Super Admin Only)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Store Health */}
        {healthReport && (
          <div className="space-y-4">
            <h4 className="font-medium">Current Store Health: {storeId}</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="flex items-center gap-2">
                {getHealthIcon(healthReport.storeExists)}
                <span className="text-sm">Store Document</span>
              </div>
              <div className="flex items-center gap-2">
                {getHealthIcon(healthReport.hasSettings)}
                <span className="text-sm">Settings</span>
              </div>
              <div className="flex items-center gap-2">
                {getHealthIcon(healthReport.hasUsers)}
                <span className="text-sm">Users</span>
              </div>
              <div className="flex items-center gap-2">
                {getHealthIcon(healthReport.hasProducts)}
                <span className="text-sm">Products</span>
              </div>
            </div>

            {healthReport.settingsFieldCount > 0 && (
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Settings fields: {healthReport.settingsFieldCount}
              </div>
            )}

            {healthReport.issues.length > 0 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Issues found:</strong>
                  <ul className="list-disc list-inside mt-1">
                    {healthReport.issues.map((issue, index) => (
                      <li key={index}>{issue}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {healthReport.recommendations.length > 0 && (
              <Alert>
                <TrendingUp className="h-4 w-4" />
                <AlertDescription>
                  <strong>Recommendations:</strong>
                  <ul className="list-disc list-inside mt-1">
                    {healthReport.recommendations.map((rec, index) => (
                      <li key={index}>{rec}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {/* Store Analysis */}
        {storeAnalysis && (
          <div className="space-y-4">
            <h4 className="font-medium">Store Structure Analysis</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {storeAnalysis.stores.length}
                </div>
                <div className="text-sm text-gray-500">Total Stores</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {storeAnalysis.stores.filter(s => s.hasSettings).length}
                </div>
                <div className="text-sm text-gray-500">With Settings</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {storeAnalysis.duplicates.length}
                </div>
                <div className="text-sm text-gray-500">Duplicates</div>
              </div>
            </div>

            {storeAnalysis.duplicates.length > 0 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Duplicate stores found:</strong>
                  {storeAnalysis.duplicates.map((dup, index) => (
                    <div key={index} className="mt-1">
                      Primary: {dup.primary}, Duplicates: {dup.duplicates.join(', ')}
                    </div>
                  ))}
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="space-y-4">
          <h4 className="font-medium">Management Actions</h4>
          <div className="flex flex-wrap gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={loadHealthReport}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh Health
            </Button>
            
            <Button
              size="sm"
              variant="outline"
              onClick={ensureStoreStructure}
              disabled={loading}
            >
              <Settings className="h-4 w-4 mr-2" />
              Fix Structure
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={() => performCleanup(true)}
              disabled={cleanupLoading}
            >
              <Database className="h-4 w-4 mr-2" />
              Analyze Duplicates
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={async () => {
                try {
                  setCleanupLoading(true)
                  const result = await cleanupFirebaseStores(true)
                  toast.success(`Analysis complete: ${result.duplicatesFound} duplicates found`)
                } catch (error) {
                  toast.error('Analysis failed')
                } finally {
                  setCleanupLoading(false)
                }
              }}
              disabled={cleanupLoading}
            >
              <Database className="h-4 w-4 mr-2" />
              Quick Analysis
            </Button>

            {storeAnalysis?.duplicates?.length > 0 && (
              <Button
                size="sm"
                variant="destructive"
                onClick={() => {
                  if (confirm('Are you sure you want to remove duplicate stores? This action cannot be undone.')) {
                    performCleanup(false)
                  }
                }}
                disabled={cleanupLoading}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {cleanupLoading ? 'Cleaning...' : 'Remove Duplicates'}
              </Button>
            )}

            <Button
              size="sm"
              variant="default"
              onClick={async () => {
                if (confirm('Run complete Firebase cleanup? This will fix duplicates and missing settings.')) {
                  try {
                    setCleanupLoading(true)
                    const result = await cleanupFirebaseStores(false)
                    toast.success(`Cleanup complete! Removed ${result.duplicatesRemoved} duplicates, initialized ${result.settingsInitialized} settings`)
                    await loadHealthReport()
                    await loadStoreAnalysis()
                  } catch (error) {
                    toast.error('Cleanup failed')
                  } finally {
                    setCleanupLoading(false)
                  }
                }
              }}
              disabled={cleanupLoading}
            >
              <Settings className="h-4 w-4 mr-2" />
              {cleanupLoading ? 'Running...' : 'Run Full Cleanup'}
            </Button>
          </div>
        </div>

        {/* Store List */}
        {storeAnalysis?.stores && (
          <div className="space-y-2">
            <h4 className="font-medium">All Stores</h4>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {storeAnalysis.stores.map((store) => (
                <div
                  key={store.id}
                  className="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded border"
                >
                  <div>
                    <span className="font-medium">{store.name}</span>
                    <span className="text-sm text-gray-500 ml-2">({store.id})</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {store.hasSettings && (
                      <Badge variant="outline" className="text-xs">
                        <Settings className="h-3 w-3 mr-1" />
                        Settings
                      </Badge>
                    )}
                    {store.hasUsers && (
                      <Badge variant="outline" className="text-xs">
                        <Users className="h-3 w-3 mr-1" />
                        Users
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
