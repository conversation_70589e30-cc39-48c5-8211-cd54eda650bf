import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  onSnapshot,
  writeBatch
} from 'firebase/firestore'
import { db, isFirebaseConfigured } from './firebase'
import type { CustomerGroup, CustomerGroupFormData } from '../types/customer-group'
import type { Customer } from './firebase'

// Default customer groups to initialize
const DEFAULT_CUSTOMER_GROUPS = [
  {
    name: 'VIP',
    description: 'High-value customers with premium benefits',
    color: '#FFD700',
    icon: 'crown',
    priority: 1,
    criteria: {
      minLifetimeValue: 50000,
      minOrderCount: 10
    },
    benefits: ['Priority support', 'Exclusive offers', 'Free shipping']
  },
  {
    name: 'Loyal',
    description: 'Regular customers who make repeat purchases',
    color: '#4F46E5',
    icon: 'heart',
    priority: 2,
    criteria: {
      minOrderCount: 3,
      minLifetimeValue: 10000
    },
    benefits: ['Loyalty rewards', 'Birthday discounts']
  },
  {
    name: 'Wholesale',
    description: 'Bulk buyers and business customers',
    color: '#7C3AED',
    icon: 'building',
    priority: 3,
    criteria: {
      minOrderValue: 25000,
      minOrderCount: 2
    },
    benefits: ['Volume discounts', 'Net payment terms']
  },
  {
    name: 'Retail',
    description: 'Individual customers making regular purchases',
    color: '#059669',
    icon: 'user',
    priority: 4,
    criteria: {
      maxOrderValue: 25000,
      minOrderCount: 1
    },
    benefits: ['Standard pricing', 'Regular promotions']
  },
  {
    name: 'New',
    description: 'Recently acquired customers',
    color: '#10B981',
    icon: 'star',
    priority: 5,
    criteria: {
      maxDaysSinceFirstOrder: 30
    },
    benefits: ['Welcome discount', 'Onboarding support']
  },
  {
    name: 'At Risk',
    description: 'Customers who haven\'t ordered recently',
    color: '#EF4444',
    icon: 'alert-triangle',
    priority: 6,
    criteria: {
      minDaysSinceLastOrder: 90
    },
    benefits: ['Win-back offers', 'Re-engagement campaigns']
  }
]

class CustomerGroupsService {
  private storeId: string

  constructor(storeId: string) {
    this.storeId = storeId
  }

  // Get all customer groups
  async getCustomerGroups(): Promise<CustomerGroup[]> {
    if (!isFirebaseConfigured) {
      console.warn('Firebase not configured, returning empty groups')
      return []
    }

    try {
      const groupsRef = collection(db, 'stores', this.storeId, 'customerGroups')
      const snapshot = await getDocs(groupsRef)

      const groups = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as CustomerGroup[]

      // Sort in memory to avoid index requirement
      return groups.sort((a, b) => {
        // Sort by priority first, then by name
        if (a.priority !== b.priority) {
          return (a.priority || 999) - (b.priority || 999)
        }
        return (a.name || '').localeCompare(b.name || '')
      })
    } catch (error) {
      console.error('Error fetching customer groups:', error)
      throw new Error('Failed to fetch customer groups')
    }
  }

  // Get a single customer group
  async getCustomerGroup(groupId: string): Promise<CustomerGroup | null> {
    if (!isFirebaseConfigured) {
      return null
    }

    try {
      const groupRef = doc(db, 'stores', this.storeId, 'customerGroups', groupId)
      const snapshot = await getDoc(groupRef)
      
      if (snapshot.exists()) {
        return {
          id: snapshot.id,
          ...snapshot.data()
        } as CustomerGroup
      }
      
      return null
    } catch (error) {
      console.error('Error fetching customer group:', error)
      throw new Error('Failed to fetch customer group')
    }
  }

  // Create a new customer group
  async createCustomerGroup(groupData: CustomerGroupFormData, createdBy: string): Promise<string> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      const newGroup = {
        ...groupData,
        customerCount: 0,
        totalRevenue: 0,
        averageOrderValue: 0,
        conversionRate: 0,
        retentionRate: 0,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        createdBy
      }

      const groupsRef = collection(db, 'stores', this.storeId, 'customerGroups')
      const docRef = await addDoc(groupsRef, newGroup)
      
      console.log('✅ Customer group created with ID:', docRef.id)
      return docRef.id
    } catch (error) {
      console.error('❌ Error creating customer group:', error)
      throw new Error('Failed to create customer group')
    }
  }

  // Update a customer group
  async updateCustomerGroup(groupId: string, updates: Partial<CustomerGroupFormData>): Promise<void> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      const groupRef = doc(db, 'stores', this.storeId, 'customerGroups', groupId)
      await updateDoc(groupRef, {
        ...updates,
        updatedAt: serverTimestamp()
      })
      
      console.log('✅ Customer group updated:', groupId)
    } catch (error) {
      console.error('❌ Error updating customer group:', error)
      throw new Error('Failed to update customer group')
    }
  }

  // Delete a customer group
  async deleteCustomerGroup(groupId: string): Promise<void> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      const groupRef = doc(db, 'stores', this.storeId, 'customerGroups', groupId)
      await deleteDoc(groupRef)
      
      console.log('✅ Customer group deleted:', groupId)
    } catch (error) {
      console.error('❌ Error deleting customer group:', error)
      throw new Error('Failed to delete customer group')
    }
  }

  // Initialize default customer groups
  async initializeDefaultGroups(createdBy: string): Promise<void> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      const batch = writeBatch(db)
      const groupsRef = collection(db, 'stores', this.storeId, 'customerGroups')

      // Check if groups already exist
      const existingGroups = await this.getCustomerGroups()
      if (existingGroups.length > 0) {
        console.log('Customer groups already exist, skipping initialization')
        return
      }

      // Create default groups
      DEFAULT_CUSTOMER_GROUPS.forEach(groupData => {
        const newGroupRef = doc(groupsRef)
        batch.set(newGroupRef, {
          ...groupData,
          customerCount: 0,
          totalRevenue: 0,
          averageOrderValue: 0,
          conversionRate: 0,
          retentionRate: 0,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          createdBy
        })
      })

      await batch.commit()
      console.log('✅ Default customer groups initialized')
    } catch (error) {
      console.error('❌ Error initializing default groups:', error)
      throw new Error('Failed to initialize default groups')
    }
  }

  // Auto-assign customers to groups based on criteria
  async autoAssignCustomerGroups(customers: Customer[]): Promise<void> {
    if (!isFirebaseConfigured) {
      return
    }

    try {
      const groups = await this.getCustomerGroups()
      const automaticGroups = groups.filter(g => g.isAutomatic && g.status === 'active')
        .sort((a, b) => a.priority - b.priority) // Higher priority first

      const batch = writeBatch(db)
      let batchCount = 0

      for (const customer of customers) {
        const assignedGroup = this.findBestGroupForCustomer(customer, automaticGroups)
        
        if (assignedGroup && customer.group !== assignedGroup.name) {
          const customerRef = doc(db, 'stores', this.storeId, 'customers', customer.id)
          batch.update(customerRef, {
            group: assignedGroup.name,
            updatedAt: serverTimestamp()
          })
          batchCount++

          // Firestore batch limit is 500 operations
          if (batchCount >= 450) {
            await batch.commit()
            batchCount = 0
          }
        }
      }

      if (batchCount > 0) {
        await batch.commit()
      }

      console.log('✅ Customer groups auto-assigned')
    } catch (error) {
      console.error('❌ Error auto-assigning customer groups:', error)
      throw new Error('Failed to auto-assign customer groups')
    }
  }

  // Find the best group for a customer based on criteria
  private findBestGroupForCustomer(customer: Customer, groups: CustomerGroup[]): CustomerGroup | null {
    for (const group of groups) {
      if (this.customerMatchesGroupCriteria(customer, group)) {
        return group
      }
    }
    return null
  }

  // Check if customer matches group criteria
  private customerMatchesGroupCriteria(customer: Customer, group: CustomerGroup): boolean {
    const criteria = group.criteria
    if (!criteria) return false

    const lifetimeValue = customer.lifetimeValue || customer.totalSpent || 0
    const orderCount = customer.orderCount || 0
    const daysSinceLastOrder = customer.lastOrderDate 
      ? Math.floor((Date.now() - new Date(customer.lastOrderDate).getTime()) / (1000 * 60 * 60 * 24))
      : Infinity

    // Check lifetime value range
    if (criteria.minLifetimeValue !== undefined && lifetimeValue < criteria.minLifetimeValue) {
      return false
    }
    if (criteria.maxLifetimeValue !== undefined && lifetimeValue > criteria.maxLifetimeValue) {
      return false
    }

    // Check order count range
    if (criteria.minOrderCount !== undefined && orderCount < criteria.minOrderCount) {
      return false
    }
    if (criteria.maxOrderCount !== undefined && orderCount > criteria.maxOrderCount) {
      return false
    }

    // Check days since last order
    if (criteria.minDaysSinceLastOrder !== undefined && daysSinceLastOrder < criteria.minDaysSinceLastOrder) {
      return false
    }
    if (criteria.maxDaysSinceLastOrder !== undefined && daysSinceLastOrder > criteria.maxDaysSinceLastOrder) {
      return false
    }

    // Check required tags
    if (criteria.requiredTags && criteria.requiredTags.length > 0) {
      const customerTags = customer.tags || []
      const hasAllRequiredTags = criteria.requiredTags.every(tag => customerTags.includes(tag))
      if (!hasAllRequiredTags) {
        return false
      }
    }

    // Check excluded tags
    if (criteria.excludedTags && criteria.excludedTags.length > 0) {
      const customerTags = customer.tags || []
      const hasExcludedTag = criteria.excludedTags.some(tag => customerTags.includes(tag))
      if (hasExcludedTag) {
        return false
      }
    }

    return true
  }

  // Subscribe to real-time customer groups updates
  subscribeToCustomerGroups(callback: (groups: CustomerGroup[]) => void): () => void {
    if (!isFirebaseConfigured) {
      console.warn('Firebase not configured, returning empty subscription')
      return () => {}
    }

    const groupsRef = collection(db, 'stores', this.storeId, 'customerGroups')

    return onSnapshot(groupsRef, (snapshot) => {
      const groups = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as CustomerGroup[]

      // Sort in memory to avoid index requirement
      const sortedGroups = groups.sort((a, b) => {
        // Sort by priority first, then by name
        if (a.priority !== b.priority) {
          return (a.priority || 999) - (b.priority || 999)
        }
        return (a.name || '').localeCompare(b.name || '')
      })

      callback(sortedGroups)
    }, (error) => {
      console.error('Error in customer groups subscription:', error)
    })
  }
}

export { CustomerGroupsService }
