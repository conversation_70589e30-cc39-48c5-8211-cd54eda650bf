import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'

import { useFirebaseAuthStore } from '../../store/firebase-auth'
import { marketingService, type Coupon } from '../../lib/marketing-service'
import { DataTable, Column } from '../../components/ui/data-table'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { cn, formatCurrency, formatDateTime } from '../../lib/utils'
import { useAutoClose } from '../../hooks/use-click-outside'
import {
  Plus,
  ArrowLeft,
  Download,
  ChevronDown,
  RefreshCw,
  Gift,
  Percent,
  DollarSign,
  Users,
  Calendar,
  Copy
} from 'lucide-react'

interface Coupon {
  id: string
  code: string
  type: 'percentage' | 'fixed' | 'free_shipping'
  value: number
  minOrderValue: number
  usageLimit: number
  usedCount: number
  targetGroups: string[]
  validFrom: string
  validTo: string
  active: boolean
  createdAt: string
  createdBy: string
}

export function CouponsPage() {
  const navigate = useNavigate()
  const { user, hasPermission } = useFirebaseAuthStore()
  const [loading, setLoading] = useState(true)
  const [coupons, setCoupons] = useState<Coupon[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')
  const [filterStatus, setFilterStatus] = useState('all')
  const [showExportDropdown, setShowExportDropdown] = useState(false)

  const canEdit = hasPermission(['super_admin', 'admin', 'editor'])
  const canDelete = hasPermission(['super_admin', 'admin'])

  // Auto-close functionality for export dropdown
  const exportDropdownRef = useAutoClose<HTMLDivElement>(() => setShowExportDropdown(false), showExportDropdown)

  useEffect(() => {
    fetchCoupons()
  }, [user])

  const fetchCoupons = async () => {
    if (!user?.storeId) return

    try {
      setLoading(true)

      // Fetch real coupons from Firebase
      const couponsData = await marketingService.getCoupons()
      setCoupons(couponsData)

      // Legacy mock data - remove after Firebase integration is complete
      const mockCoupons: Coupon[] = [
        {
          id: '1',
          code: 'WELCOME10',
          type: 'percentage',
          value: 10,
          minOrderValue: 1000,
          usageLimit: 100,
          usedCount: 25,
          targetGroups: ['first_time'],
          validFrom: '2025-07-01T00:00:00Z',
          validTo: '2025-07-31T23:59:59Z',
          active: true,
          createdAt: '2025-07-01T10:00:00Z',
          createdBy: '<EMAIL>'
        },
        {
          id: '2',
          code: 'SUMMER50',
          type: 'fixed',
          value: 500,
          minOrderValue: 2000,
          usageLimit: 50,
          usedCount: 12,
          targetGroups: ['vip'],
          validFrom: '2025-07-15T00:00:00Z',
          validTo: '2025-08-15T23:59:59Z',
          active: true,
          createdAt: '2025-07-15T14:30:00Z',
          createdBy: '<EMAIL>'
        },
        {
          id: '3',
          code: 'FREESHIP',
          type: 'free_shipping',
          value: 0,
          minOrderValue: 1500,
          usageLimit: 200,
          usedCount: 89,
          targetGroups: ['all'],
          validFrom: '2025-07-01T00:00:00Z',
          validTo: '2025-12-31T23:59:59Z',
          active: true,
          createdAt: '2025-07-01T09:00:00Z',
          createdBy: '<EMAIL>'
        }
      ]

      setCoupons(mockCoupons)
    } catch (error) {
      console.error('Error fetching coupons:', error)
      toast.error('Failed to load coupons')
    } finally {
      setLoading(false)
    }
  }

  const handleExport = (format: 'csv' | 'json' | 'xlsx') => {
    toast.success(`Exporting coupons as ${format.toUpperCase()}...`)
    // TODO: Implement actual export functionality
  }

  const handleDeleteCoupon = async (couponId: string) => {
    if (!canDelete) {
      toast.error('You do not have permission to delete coupons')
      return
    }

    if (confirm('Are you sure you want to delete this coupon?')) {
      try {
        // TODO: Implement actual delete functionality
        setCoupons(prev => prev.filter(c => c.id !== couponId))
        toast.success('Coupon deleted successfully')
      } catch (error) {
        console.error('Error deleting coupon:', error)
        toast.error('Failed to delete coupon')
      }
    }
  }

  const handleCopyCouponCode = (code: string) => {
    navigator.clipboard.writeText(code)
    toast.success('Coupon code copied to clipboard')
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'percentage':
        return <Percent className="h-4 w-4" />
      case 'fixed':
        return <DollarSign className="h-4 w-4" />
      case 'free_shipping':
        return <Gift className="h-4 w-4" />
      default:
        return <Gift className="h-4 w-4" />
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'percentage':
        return 'Percentage'
      case 'fixed':
        return 'Fixed Amount'
      case 'free_shipping':
        return 'Free Shipping'
      default:
        return type
    }
  }

  const getStatusBadge = (coupon: Coupon) => {
    const now = new Date()
    const validFrom = new Date(coupon.validFrom)
    const validTo = new Date(coupon.validTo)

    if (!coupon.active) {
      return <Badge variant="outline" className="bg-gray-100 text-gray-800">Inactive</Badge>
    }

    if (now < validFrom) {
      return <Badge className="bg-blue-100 text-blue-800">Scheduled</Badge>
    }

    if (now > validTo) {
      return <Badge className="bg-red-100 text-red-800">Expired</Badge>
    }

    if (coupon.usedCount >= coupon.usageLimit) {
      return <Badge className="bg-orange-100 text-orange-800">Limit Reached</Badge>
    }

    return <Badge className="bg-green-100 text-green-800">Active</Badge>
  }

  const filteredCoupons = coupons.filter(coupon => {
    const matchesSearch = coupon.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         coupon.targetGroups.some(group => group.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesType = filterType === 'all' || coupon.type === filterType

    const matchesStatus = filterStatus === 'all' || 
                         (filterStatus === 'active' && coupon.active) ||
                         (filterStatus === 'inactive' && !coupon.active)

    return matchesSearch && matchesType && matchesStatus
  })

  const columns: Column<Coupon>[] = [
    {
      key: 'code',
      title: 'Coupon Code',
      sortable: true,
      render: (coupon) => {
        if (!coupon) return <div>-</div>

        return (
          <div className="flex items-center gap-2">
            <div className="p-1 bg-gray-100 dark:bg-gray-700 rounded">
              {getTypeIcon(coupon.type)}
            </div>
            <div>
              <div className="flex items-center gap-2">
                <span className="font-mono font-medium">{coupon.code || '-'}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleCopyCouponCode(coupon.code || '')}
                  className="h-6 w-6 p-0"
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
              <div className="text-xs text-gray-500">{getTypeLabel(coupon.type)}</div>
            </div>
          </div>
        )
      }
    },
    {
      key: 'value',
      title: 'Discount',
      sortable: true,
      render: (coupon) => {
        if (!coupon) return <div>-</div>

        return (
          <div>
            {coupon.type === 'percentage' ? (
              <span className="font-medium">{coupon.value || 0}%</span>
            ) : coupon.type === 'fixed' ? (
              <span className="font-medium">{formatCurrency(coupon.value || 0)}</span>
            ) : (
              <span className="font-medium">Free Shipping</span>
            )}
            <div className="text-xs text-gray-500">
              Min: {formatCurrency(coupon.minOrderValue || 0)}
            </div>
          </div>
        )
      }
    },
    {
      key: 'usage',
      title: 'Usage',
      sortable: true,
      render: (coupon) => {
        if (!coupon) return <div>-</div>
        const usedCount = coupon.usedCount || 0
        const usageLimit = coupon.usageLimit || 0
        const percentage = usageLimit > 0 ? (usedCount / usageLimit) * 100 : 0

        return (
          <div>
            <div className="font-medium">{usedCount} / {usageLimit}</div>
            <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
              <div
                className="bg-blue-600 h-1.5 rounded-full"
                style={{ width: `${percentage}%` }}
              ></div>
            </div>
          </div>
        )
      }
    },
    {
      key: 'validity',
      title: 'Validity',
      sortable: true,
      render: (coupon) => {
        if (!coupon) return <div>-</div>

        return (
          <div className="text-sm">
            <div>From: {formatDateTime(coupon.validFrom)}</div>
            <div>To: {formatDateTime(coupon.validTo)}</div>
          </div>
        )
      }
    },
    {
      key: 'targetGroups',
      title: 'Target Groups',
      render: (coupon) => {
        if (!coupon || !coupon.targetGroups) return <div>-</div>

        return (
          <div className="flex flex-wrap gap-1">
            {coupon.targetGroups.map((group, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {group.replace('_', ' ')}
              </Badge>
            ))}
          </div>
        )
      }
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      render: (coupon) => {
        if (!coupon) return <div>-</div>
        return getStatusBadge(coupon)
      }
    }
  ]

  return (
    <div className="page-container">
      {/* Header */}
      <div className="page-header flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/admin/marketing')}
            className="hover:bg-gray-100"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="page-title">Coupon Management</h1>
            <p className="page-subtitle">Create and manage discount coupons</p>
          </div>
        </div>
        {canEdit && (
          <Button onClick={() => navigate('/admin/marketing/coupons/new')}>
            <Plus className="h-4 w-4 mr-2" />
            New Coupon
          </Button>
        )}
      </div>

      {/* Stats Cards */}
      <div className="stats-grid-optimized">
        <Card className="card-optimized">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Coupons</CardTitle>
            <Gift className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{coupons.length}</div>
            <p className="text-xs text-gray-500">
              {coupons.filter(c => c.active).length} active
            </p>
          </CardContent>
        </Card>

        <Card className="card-optimized">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Redemptions</CardTitle>
            <Users className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {coupons.reduce((sum, c) => sum + c.usedCount, 0)}
            </div>
            <p className="text-xs text-gray-500">
              Across all coupons
            </p>
          </CardContent>
        </Card>

        <Card className="card-optimized">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Usage Rate</CardTitle>
            <Percent className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round((coupons.reduce((sum, c) => sum + c.usedCount, 0) / 
                          coupons.reduce((sum, c) => sum + c.usageLimit, 0)) * 100)}%
            </div>
            <p className="text-xs text-gray-500">
              Average usage rate
            </p>
          </CardContent>
        </Card>

        <Card className="card-optimized">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expiring Soon</CardTitle>
            <Calendar className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {coupons.filter(c => {
                const daysUntilExpiry = Math.ceil((new Date(c.validTo).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
                return daysUntilExpiry <= 7 && daysUntilExpiry > 0
              }).length}
            </div>
            <p className="text-xs text-gray-500">
              Within 7 days
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between mb-6">
        <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
          <div className="relative">
            <input
              type="text"
              placeholder="Search coupons..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full sm:w-64 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
            />
          </div>
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          >
            <option value="all">All Types</option>
            <option value="percentage">Percentage</option>
            <option value="fixed">Fixed Amount</option>
            <option value="free_shipping">Free Shipping</option>
          </select>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
        <div className="flex gap-2">
          <div className="relative" ref={exportDropdownRef}>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
              onClick={() => setShowExportDropdown(!showExportDropdown)}
            >
              <Download className="h-4 w-4" />
              Export
              <ChevronDown className={cn("h-3 w-3 transition-transform", showExportDropdown && "rotate-180")} />
            </Button>
            {showExportDropdown && (
              <div className="absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 min-w-[140px] theme-dropdown">
                <button
                  onClick={() => {
                    handleExport('csv')
                    setShowExportDropdown(false)
                  }}
                  className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 first:rounded-t-lg theme-dropdown-item"
                >
                  Export as CSV
                </button>
                <button
                  onClick={() => {
                    handleExport('json')
                    setShowExportDropdown(false)
                  }}
                  className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 theme-dropdown-item"
                >
                  Export as JSON
                </button>
                <button
                  onClick={() => {
                    handleExport('xlsx')
                    setShowExportDropdown(false)
                  }}
                  className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 last:rounded-b-lg theme-dropdown-item"
                >
                  Export as Excel
                </button>
              </div>
            )}
          </div>
          <Button variant="outline" size="sm" onClick={fetchCoupons}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        data={filteredCoupons}
        columns={columns}
        loading={loading}
        searchable={false}
        filterable={false}
        actions={{
          view: (coupon) => navigate(`/admin/marketing/coupons/${coupon.id}`),
          edit: canEdit ? (coupon) => navigate(`/admin/marketing/coupons/${coupon.id}/edit`) : undefined,
          delete: canDelete ? (coupon) => handleDeleteCoupon(coupon.id) : undefined
        }}
        emptyState={{
          title: 'No coupons found',
          description: searchTerm || filterType !== 'all' || filterStatus !== 'all'
            ? 'No coupons match your current filters.'
            : 'Create your first coupon to start offering discounts.',
          icon: <Gift className="h-6 w-6 text-gray-400" />
        }}
      />
    </div>
  )
}
