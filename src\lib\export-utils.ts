// Export utilities for customer management system

export interface ExportOptions {
  filename: string
  format: 'csv' | 'json' | 'xlsx'
  data: any[]
  columns?: string[]
}

// Convert data to CSV format
export function convertToCSV(data: any[], columns?: string[]): string {
  if (data.length === 0) return ''
  
  // Get headers from first object or use provided columns
  const headers = columns || Object.keys(data[0])
  
  // Create CSV header row
  const csvHeaders = headers.join(',')
  
  // Create CSV data rows
  const csvRows = data.map(row => {
    return headers.map(header => {
      const value = getNestedValue(row, header)
      // Escape commas and quotes in values
      if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
        return `"${value.replace(/"/g, '""')}"`
      }
      return value || ''
    }).join(',')
  })
  
  return [csvHeaders, ...csvRows].join('\n')
}

// Convert data to JSON format
export function convertToJSON(data: any[]): string {
  return JSON.stringify(data, null, 2)
}

// Get nested object value by dot notation
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}

// Download file with given content
export function downloadFile(content: string, filename: string, mimeType: string): void {
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  
  // Cleanup
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

// Main export function
export function exportData(options: ExportOptions): void {
  const { filename, format, data, columns } = options
  
  if (data.length === 0) {
    throw new Error('No data to export')
  }
  
  let content: string
  let mimeType: string
  let fileExtension: string
  
  switch (format) {
    case 'csv':
      content = convertToCSV(data, columns)
      mimeType = 'text/csv;charset=utf-8;'
      fileExtension = '.csv'
      break
      
    case 'json':
      content = convertToJSON(data)
      mimeType = 'application/json;charset=utf-8;'
      fileExtension = '.json'
      break
      
    case 'xlsx':
      // For now, export as CSV (XLSX would require additional library like xlsx)
      content = convertToCSV(data, columns)
      mimeType = 'text/csv;charset=utf-8;'
      fileExtension = '.csv'
      break
      
    default:
      throw new Error(`Unsupported export format: ${format}`)
  }
  
  const fullFilename = filename.includes('.') ? filename : `${filename}${fileExtension}`
  downloadFile(content, fullFilename, mimeType)
}

// Flatten customer data for export
export function flattenCustomerData(customers: any[]): any[] {
  return customers.map(customer => ({
    id: customer.id,
    name: customer.name,
    email: customer.email,
    phone: customer.phone || '',
    shipping_address: `${customer.address?.shipping?.line1 || ''}, ${customer.address?.shipping?.city || ''}, ${customer.address?.shipping?.zip || ''}`,
    billing_address: `${customer.address?.billing?.line1 || ''}, ${customer.address?.billing?.city || ''}, ${customer.address?.billing?.zip || ''}`,
    groups: customer.groups?.join(', ') || '',
    marketing_opt_in: customer.marketingOptIn ? 'Yes' : 'No',
    total_spend: customer.totalSpend || 0,
    lifetime_value: customer.lifetimeValue || 0,
    average_order_value: customer.averageOrderValue || 0,
    total_orders: customer.totalOrders || 0,
    wishlist_items: customer.wishlist?.length || 0,
    last_login: customer.lastLogin || '',
    created_at: customer.createdAt || '',
    status: customer.status || '',
    recent_purchase: customer.recentPurchase ? 'Yes' : 'No',
    active_cart: customer.activeCart ? 'Yes' : 'No',
    returning_customer: customer.isReturningCustomer ? 'Yes' : 'No',
    notes: customer.notes || ''
  }))
}

// Flatten customer group data for export
export function flattenGroupData(groups: any[]): any[] {
  return groups.map(group => ({
    id: group.id,
    name: group.name,
    description: group.description,
    status: group.status,
    customer_count: group.customerCount || 0,
    total_revenue: group.totalRevenue || 0,
    discount_rate: group.discountRate || 0,
    min_spend: group.rules?.minSpend || '',
    min_orders: group.rules?.orderCount || '',
    registration_days: group.rules?.registrationDays || '',
    last_activity_days: group.rules?.lastActivityDays || '',
    created_at: group.createdAt || '',
    updated_at: group.updatedAt || ''
  }))
}
