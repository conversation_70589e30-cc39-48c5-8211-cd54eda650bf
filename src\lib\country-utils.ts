// Country-specific utilities for formatting and validation

export interface CountryInfo {
  code: string
  name: string
  currency: string
  phoneCode: string
  phoneFormat: string
  phoneExample: string
  timezone: string
  locale: string
}

export const COUNTRIES: Record<string, CountryInfo> = {
  PK: {
    code: 'PK',
    name: 'Pakistan',
    currency: 'PKR',
    phoneCode: '+92',
    phoneFormat: '+92 XXX XXXXXXX',
    phoneExample: '+92 300 1234567',
    timezone: 'Asia/Karachi',
    locale: 'en-PK'
  },
  US: {
    code: 'US',
    name: 'United States',
    currency: 'USD',
    phoneCode: '+1',
    phoneFormat: '+1 (XXX) XXX-XXXX',
    phoneExample: '+****************',
    timezone: 'America/New_York',
    locale: 'en-US'
  },
  GB: {
    code: 'GB',
    name: 'United Kingdom',
    currency: 'GBP',
    phoneCode: '+44',
    phoneFormat: '+44 XXXX XXXXXX',
    phoneExample: '+44 7700 900123',
    timezone: 'Europe/London',
    locale: 'en-GB'
  },
  IN: {
    code: 'IN',
    name: 'India',
    currency: 'INR',
    phoneCode: '+91',
    phoneFormat: '+91 XXXXX XXXXX',
    phoneExample: '+91 98765 43210',
    timezone: 'Asia/Kolkata',
    locale: 'en-IN'
  },
  CA: {
    code: 'CA',
    name: 'Canada',
    currency: 'CAD',
    phoneCode: '+1',
    phoneFormat: '+1 (XXX) XXX-XXXX',
    phoneExample: '+****************',
    timezone: 'America/Toronto',
    locale: 'en-CA'
  },
  AU: {
    code: 'AU',
    name: 'Australia',
    currency: 'AUD',
    phoneCode: '+61',
    phoneFormat: '+61 X XXXX XXXX',
    phoneExample: '+61 4 1234 5678',
    timezone: 'Australia/Sydney',
    locale: 'en-AU'
  },
  AE: {
    code: 'AE',
    name: 'United Arab Emirates',
    currency: 'AED',
    phoneCode: '+971',
    phoneFormat: '+971 XX XXX XXXX',
    phoneExample: '+971 50 123 4567',
    timezone: 'Asia/Dubai',
    locale: 'en-AE'
  },
  SA: {
    code: 'SA',
    name: 'Saudi Arabia',
    currency: 'SAR',
    phoneCode: '+966',
    phoneFormat: '+966 XX XXX XXXX',
    phoneExample: '+966 50 123 4567',
    timezone: 'Asia/Riyadh',
    locale: 'ar-SA'
  }
}

/**
 * Get country info by country code
 */
export function getCountryInfo(countryCode: string): CountryInfo | null {
  return COUNTRIES[countryCode.toUpperCase()] || null
}

/**
 * Get country info by currency
 */
export function getCountryByCurrency(currency: string): CountryInfo | null {
  const country = Object.values(COUNTRIES).find(c => c.currency === currency.toUpperCase())
  return country || null
}

/**
 * Format phone number according to country format
 */
export function formatPhoneNumber(phone: string, countryCode: string): string {
  if (!phone) return ''
  
  const country = getCountryInfo(countryCode)
  if (!country) return phone

  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '')
  
  // Remove country code if present
  let localNumber = digits
  const phoneCodeDigits = country.phoneCode.replace(/\D/g, '')
  if (digits.startsWith(phoneCodeDigits)) {
    localNumber = digits.slice(phoneCodeDigits.length)
  }

  // Format based on country
  switch (countryCode.toUpperCase()) {
    case 'PK':
      // Pakistan: +92 XXX XXXXXXX
      if (localNumber.length >= 10) {
        return `${country.phoneCode} ${localNumber.slice(0, 3)} ${localNumber.slice(3)}`
      }
      break
    case 'US':
    case 'CA':
      // US/Canada: +1 (XXX) XXX-XXXX
      if (localNumber.length >= 10) {
        return `${country.phoneCode} (${localNumber.slice(0, 3)}) ${localNumber.slice(3, 6)}-${localNumber.slice(6)}`
      }
      break
    case 'GB':
      // UK: +44 XXXX XXXXXX
      if (localNumber.length >= 10) {
        return `${country.phoneCode} ${localNumber.slice(0, 4)} ${localNumber.slice(4)}`
      }
      break
    case 'IN':
      // India: +91 XXXXX XXXXX
      if (localNumber.length >= 10) {
        return `${country.phoneCode} ${localNumber.slice(0, 5)} ${localNumber.slice(5)}`
      }
      break
    case 'AU':
      // Australia: +61 X XXXX XXXX
      if (localNumber.length >= 9) {
        return `${country.phoneCode} ${localNumber.slice(0, 1)} ${localNumber.slice(1, 5)} ${localNumber.slice(5)}`
      }
      break
    case 'AE':
    case 'SA':
      // UAE/Saudi: +XXX XX XXX XXXX
      if (localNumber.length >= 9) {
        return `${country.phoneCode} ${localNumber.slice(0, 2)} ${localNumber.slice(2, 5)} ${localNumber.slice(5)}`
      }
      break
  }

  // Fallback: just add country code
  return `${country.phoneCode} ${localNumber}`
}

/**
 * Validate phone number for a specific country
 */
export function validatePhoneNumber(phone: string, countryCode: string): boolean {
  if (!phone) return false
  
  const country = getCountryInfo(countryCode)
  if (!country) return false

  const digits = phone.replace(/\D/g, '')
  const phoneCodeDigits = country.phoneCode.replace(/\D/g, '')
  
  // Check if it starts with country code
  let localNumber = digits
  if (digits.startsWith(phoneCodeDigits)) {
    localNumber = digits.slice(phoneCodeDigits.length)
  }

  // Country-specific validation
  switch (countryCode.toUpperCase()) {
    case 'PK':
      return localNumber.length === 10 && localNumber.startsWith('3')
    case 'US':
    case 'CA':
      return localNumber.length === 10
    case 'GB':
      return localNumber.length >= 10 && localNumber.length <= 11
    case 'IN':
      return localNumber.length === 10
    case 'AU':
      return localNumber.length >= 9 && localNumber.length <= 10
    case 'AE':
      return localNumber.length === 9
    case 'SA':
      return localNumber.length === 9
    default:
      return localNumber.length >= 7 && localNumber.length <= 15
  }
}

/**
 * Get phone input placeholder for country
 */
export function getPhoneInputPlaceholder(countryCode: string): string {
  const country = getCountryInfo(countryCode)
  return country?.phoneExample || 'Enter phone number'
}

/**
 * Get currency symbol
 */
export function getCurrencySymbol(currency: string): string {
  const symbols: Record<string, string> = {
    PKR: 'Rs',
    USD: '$',
    GBP: '£',
    EUR: '€',
    INR: '₹',
    CAD: 'C$',
    AUD: 'A$',
    AED: 'د.إ',
    SAR: 'ر.س'
  }
  return symbols[currency.toUpperCase()] || currency
}

/**
 * Format currency amount
 */
export function formatCurrency(amount: number, currency: string, locale?: string): string {
  const country = getCountryByCurrency(currency)
  const formatLocale = locale || country?.locale || 'en-US'
  
  try {
    return new Intl.NumberFormat(formatLocale, {
      style: 'currency',
      currency: currency.toUpperCase()
    }).format(amount)
  } catch {
    // Fallback formatting
    const symbol = getCurrencySymbol(currency)
    return `${symbol} ${amount.toLocaleString()}`
  }
}

/**
 * Get timezone options for a country
 */
export function getTimezoneOptions(countryCode: string): string[] {
  const country = getCountryInfo(countryCode)
  if (!country) return ['UTC']
  
  // Return common timezones for the country
  switch (countryCode.toUpperCase()) {
    case 'PK':
      return ['Asia/Karachi']
    case 'US':
      return [
        'America/New_York',
        'America/Chicago', 
        'America/Denver',
        'America/Los_Angeles',
        'America/Anchorage',
        'Pacific/Honolulu'
      ]
    case 'CA':
      return [
        'America/Toronto',
        'America/Vancouver',
        'America/Edmonton',
        'America/Winnipeg',
        'America/Halifax'
      ]
    case 'AU':
      return [
        'Australia/Sydney',
        'Australia/Melbourne',
        'Australia/Brisbane',
        'Australia/Perth',
        'Australia/Adelaide'
      ]
    default:
      return [country.timezone]
  }
}
