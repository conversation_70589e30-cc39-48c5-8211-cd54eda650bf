import React, { useState, useEffect } from 'react'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { toast } from 'react-hot-toast'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { InventoryService } from '../lib/inventory-service'
import { dataService } from '../lib/data-service'
import { Button } from '../components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Input } from '../components/ui/input'
import { Label } from '../components/ui/label'
import { Textarea } from '../components/ui/textarea'
import { Select } from '../components/ui/select'
import { ArrowLeft, Save, Package, Plus, Minus, RotateCcw } from 'lucide-react'
import type { InventoryAdjustment, ProductWithInventory } from '../types/inventory'

export default function InventoryAdjustPage() {
  const navigate = useNavigate()
  const { productId } = useParams()
  const [searchParams] = useSearchParams()
  const variantId = searchParams.get('variant')
  const { user } = useFirebaseAuthStore()

  // State
  const [product, setProduct] = useState<ProductWithInventory | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState<InventoryAdjustment>({
    productId: productId || '',
    variantId: variantId || undefined,
    action: 'increase',
    quantity: 0,
    note: '',
    reason: 'restock'
  })

  // Initialize service
  const storeId = 'womanza-jewelry-store' // Use default store for demo
  const inventoryService = new InventoryService(storeId)

  // Load product data
  const loadProduct = async () => {
    if (!productId) return

    try {
      setLoading(true)

      // Check if this is a sample product
      if (productId.startsWith('sample-product-')) {
        const sampleProduct = getSampleProduct(productId)
        if (sampleProduct) {
          setProduct(sampleProduct)
          setFormData(prev => ({
            ...prev,
            productId: productId,
            variantId: variantId || undefined
          }))
        } else {
          toast.error('Sample product not found')
        }
      } else {
        // Load real product from Firebase
        const productData = await dataService.getProduct(productId)
        if (productData) {
          const productWithInventory = {
            ...productData,
            // Set default inventory values if not present
            trackInventory: productData.trackInventory ?? true,
            inventoryQuantity: productData.inventoryQuantity ?? 0,
            lowStockThreshold: productData.lowStockThreshold ?? 5,
            isOutOfStock: productData.isOutOfStock ?? false,
            hasVariants: productData.hasVariants ?? false,
            totalStock: productData.totalStock ?? productData.inventoryQuantity ?? 0,
            lastStockUpdate: productData.lastStockUpdate ?? productData.updatedAt
          } as ProductWithInventory

          setProduct(productWithInventory)
          setFormData(prev => ({
            ...prev,
            productId: productData.id!,
            variantId: variantId || undefined
          }))
        } else {
          toast.error('Product not found')
        }
      }
    } catch (error) {
      console.error('Error loading product:', error)
      toast.error('Failed to load product data')
    } finally {
      setLoading(false)
    }
  }

  // Get sample product data
  const getSampleProduct = (productId: string): ProductWithInventory | null => {
    // Get stored stock from localStorage
    const getStoredStock = (productId: string, defaultStock: number) => {
      const storageKey = `sample-inventory-${productId}`
      const stored = localStorage.getItem(storageKey)
      return stored ? JSON.parse(stored).stock || defaultStock : defaultStock
    }

    const sampleProducts = {
      'sample-product-1': {
        id: 'sample-product-1',
        name: 'Gold Earrings',
        sku: 'GOLD-EAR-001',
        price: 12000,
        inventoryQuantity: getStoredStock('sample-product-1', 15),
        lowStockThreshold: 5,
        trackInventory: true,
        isOutOfStock: getStoredStock('sample-product-1', 15) === 0,
        hasVariants: false,
        totalStock: getStoredStock('sample-product-1', 15),
        lastStockUpdate: new Date().toISOString(),
        images: [],
        categoryName: 'Jewelry',
        status: 'active'
      },
      'sample-product-2': {
        id: 'sample-product-2',
        name: 'Silver Necklace',
        sku: 'SILVER-NECK-001',
        price: 8000,
        inventoryQuantity: getStoredStock('sample-product-2', 3),
        lowStockThreshold: 5,
        trackInventory: true,
        isOutOfStock: getStoredStock('sample-product-2', 3) === 0,
        hasVariants: false,
        totalStock: getStoredStock('sample-product-2', 3),
        lastStockUpdate: new Date().toISOString(),
        images: [],
        categoryName: 'Jewelry',
        status: 'active'
      },
      'sample-product-3': {
        id: 'sample-product-3',
        name: 'Diamond Ring',
        sku: 'DIAMOND-RING-001',
        price: 25000,
        inventoryQuantity: getStoredStock('sample-product-3', 0),
        lowStockThreshold: 2,
        trackInventory: true,
        isOutOfStock: getStoredStock('sample-product-3', 0) === 0,
        hasVariants: false,
        totalStock: getStoredStock('sample-product-3', 0),
        lastStockUpdate: new Date().toISOString(),
        images: [],
        categoryName: 'Jewelry',
        status: 'active'
      },
      'sample-product-4': {
        id: 'sample-product-4',
        name: 'Pearl Bracelet',
        sku: 'PEARL-BRAC-001',
        price: 6500,
        inventoryQuantity: getStoredStock('sample-product-4', 8),
        lowStockThreshold: 10,
        trackInventory: true,
        isOutOfStock: getStoredStock('sample-product-4', 8) === 0,
        hasVariants: false,
        totalStock: getStoredStock('sample-product-4', 8),
        lastStockUpdate: new Date().toISOString(),
        images: [],
        categoryName: 'Jewelry',
        status: 'active'
      }
    }

    return sampleProducts[productId as keyof typeof sampleProducts] || null
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.quantity || formData.quantity <= 0) {
      toast.error('Please enter a valid quantity')
      return
    }

    try {
      setSaving(true)
      await inventoryService.adjustInventory(
        formData,
        'admin', // Use default admin user for demo
        'Admin User'
      )

      toast.success('Inventory adjusted successfully', {
        style: {
          background: '#10B981',
          color: 'white',
        }
      })

      // Navigate back to inventory detail page
      navigate(`/admin/inventory/${productId}${variantId ? `?variant=${variantId}` : ''}`)
    } catch (error) {
      console.error('Error adjusting inventory:', error)
      toast.error('Failed to adjust inventory', {
        style: {
          background: '#EF4444',
          color: 'white',
        }
      })
    } finally {
      setSaving(false)
    }
  }

  // Get current stock for selected item
  const getCurrentStock = () => {
    if (!product) return 0

    if (variantId && product.variants) {
      const variant = product.variants.find(v => v.id === variantId)
      return variant?.stock ?? 0
    }

    return product.inventoryQuantity ?? 0
  }

  // Get item name
  const getItemName = () => {
    if (!product) return ''

    if (variantId && product.variants) {
      const variant = product.variants.find(v => v.id === variantId)
      return `${product.name} - ${variant?.name || 'Unknown Variant'}`
    }

    return product.name
  }

  // Calculate new stock based on action
  const calculateNewStock = () => {
    const currentStock = getCurrentStock()
    
    switch (formData.action) {
      case 'increase':
        return currentStock + formData.quantity
      case 'decrease':
        return Math.max(0, currentStock - formData.quantity)
      case 'set':
        return formData.quantity
      default:
        return currentStock
    }
  }

  // Load data on mount
  useEffect(() => {
    loadProduct()
  }, [productId, variantId])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading product data...</p>
        </div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="text-center py-8">
        <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          Product not found
        </h3>
        <p className="text-gray-500 dark:text-gray-400 mb-4">
          The requested product could not be found.
        </p>
        <Button onClick={() => navigate('/admin/inventory')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Inventory
        </Button>
      </div>
    )
  }

  const currentStock = getCurrentStock()
  const newStock = calculateNewStock()

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          onClick={() => navigate('/admin/inventory')}
          className="hover:bg-gray-100"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
            Adjust Inventory
          </h1>
          <p className="text-gray-500 dark:text-gray-400">
            Update stock levels for {getItemName()}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Product Info */}
        <Card>
          <CardHeader>
            <CardTitle>Product Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-3">
              {product.images?.[0] ? (
                <img 
                  src={product.images[0]} 
                  alt={product.name}
                  className="w-16 h-16 rounded-lg object-cover"
                />
              ) : (
                <div className="w-16 h-16 rounded-lg bg-gray-100 flex items-center justify-center">
                  <Package className="h-8 w-8 text-gray-400" />
                </div>
              )}
              <div>
                <div className="font-medium text-gray-900 dark:text-gray-100">
                  {getItemName()}
                </div>
                <div className="text-sm text-gray-500">
                  SKU: {variantId && product.variants ? 
                    product.variants.find(v => v.id === variantId)?.sku : 
                    product.sku}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 pt-4 border-t">
              <div>
                <Label className="text-sm font-medium text-gray-600">Current Stock</Label>
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {currentStock}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-600">New Stock</Label>
                <div className={`text-2xl font-bold ${
                  newStock > currentStock ? 'text-green-600' : 
                  newStock < currentStock ? 'text-red-600' : 
                  'text-gray-900 dark:text-gray-100'
                }`}>
                  {newStock}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Adjustment Form */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Inventory Adjustment</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Action Type */}
              <div>
                <Label htmlFor="action" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Action Type
                </Label>
                <Select
                  value={formData.action}
                  onValueChange={(value: 'increase' | 'decrease' | 'set') =>
                    setFormData(prev => ({ ...prev, action: value }))
                  }
                  options={[
                    { value: 'increase', label: '📈 Increase Stock' },
                    { value: 'decrease', label: '📉 Decrease Stock' },
                    { value: 'set', label: '🔄 Set Exact Amount' }
                  ]}
                  className="mt-1"
                />
              </div>

              {/* Quantity */}
              <div>
                <Label htmlFor="quantity" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {formData.action === 'set' ? 'New Stock Amount' : 'Quantity'}
                </Label>
                <Input
                  id="quantity"
                  type="number"
                  min="0"
                  value={formData.quantity}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    quantity: parseInt(e.target.value) || 0 
                  }))}
                  className="mt-1"
                  placeholder="Enter quantity"
                  required
                />
              </div>

              {/* Reason */}
              <div>
                <Label htmlFor="reason" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Reason
                </Label>
                <Select
                  value={formData.reason}
                  onValueChange={(value: any) =>
                    setFormData(prev => ({ ...prev, reason: value }))
                  }
                  options={[
                    { value: 'restock', label: 'Restock' },
                    { value: 'damage', label: 'Damage' },
                    { value: 'theft', label: 'Theft' },
                    { value: 'correction', label: 'Correction' },
                    { value: 'return', label: 'Return' },
                    { value: 'transfer', label: 'Transfer' },
                    { value: 'other', label: 'Other' }
                  ]}
                  className="mt-1"
                />
              </div>

              {/* Note */}
              <div>
                <Label htmlFor="note" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Note (Optional)
                </Label>
                <Textarea
                  id="note"
                  value={formData.note}
                  onChange={(e) => setFormData(prev => ({ ...prev, note: e.target.value }))}
                  className="mt-1"
                  placeholder="Add a note about this adjustment..."
                  rows={3}
                />
              </div>

              {/* Actions */}
              <div className="flex items-center justify-end gap-3 pt-6 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate('/admin/inventory')}
                  disabled={saving}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={saving}>
                  {saving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Adjusting...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Adjust Inventory
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
