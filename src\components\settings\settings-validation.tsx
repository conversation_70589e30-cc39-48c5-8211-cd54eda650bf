import React from 'react'
import { Alert, AlertDescription } from '../ui/alert'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import {
  AlertTriangle,
  CheckCircle,
  Info,
  RefreshCw,
  Shield,
  Database,
  Clock
} from 'lucide-react'

interface ValidationError {
  field: string
  message: string
  severity: 'error' | 'warning' | 'info'
}

interface SettingsValidationProps {
  errors: ValidationError[]
  isValid: boolean
  lastSaved?: string
  onValidate?: () => void
  onRetry?: () => void
}

export function SettingsValidation({ 
  errors, 
  isValid, 
  lastSaved, 
  onValidate, 
  onRetry 
}: SettingsValidationProps) {
  const errorCount = errors.filter(e => e.severity === 'error').length
  const warningCount = errors.filter(e => e.severity === 'warning').length
  const infoCount = errors.filter(e => e.severity === 'info').length

  const getIcon = (severity: string) => {
    switch (severity) {
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />
      default:
        return <Info className="h-4 w-4 text-gray-500" />
    }
  }

  const getAlertVariant = (severity: string) => {
    switch (severity) {
      case 'error':
        return 'destructive'
      case 'warning':
        return 'default'
      case 'info':
        return 'default'
      default:
        return 'default'
    }
  }

  if (isValid && errors.length === 0) {
    return (
      <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
        <CardContent className="pt-6">
          <div className="flex items-center gap-3">
            <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
            <div>
              <h3 className="font-semibold text-green-900 dark:text-green-100">
                Settings Valid
              </h3>
              <p className="text-sm text-green-700 dark:text-green-300">
                All settings are properly configured and saved.
                {lastSaved && (
                  <span className="ml-2">
                    Last saved: {new Date(lastSaved).toLocaleString()}
                  </span>
                )}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* Summary Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Settings Validation
          </CardTitle>
          <CardDescription>
            Review and fix any issues with your store settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-4">
            {errorCount > 0 && (
              <Badge variant="destructive" className="flex items-center gap-1">
                <AlertTriangle className="h-3 w-3" />
                {errorCount} Error{errorCount !== 1 ? 's' : ''}
              </Badge>
            )}
            {warningCount > 0 && (
              <Badge variant="secondary" className="flex items-center gap-1 bg-yellow-100 text-yellow-800">
                <AlertTriangle className="h-3 w-3" />
                {warningCount} Warning{warningCount !== 1 ? 's' : ''}
              </Badge>
            )}
            {infoCount > 0 && (
              <Badge variant="outline" className="flex items-center gap-1">
                <Info className="h-3 w-3" />
                {infoCount} Info
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-2">
            {onValidate && (
              <Button variant="outline" size="sm" onClick={onValidate}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Re-validate
              </Button>
            )}
            {onRetry && errorCount > 0 && (
              <Button variant="outline" size="sm" onClick={onRetry}>
                <Database className="h-4 w-4 mr-2" />
                Retry Save
              </Button>
            )}
            {lastSaved && (
              <div className="flex items-center gap-1 text-sm text-gray-500">
                <Clock className="h-4 w-4" />
                Last saved: {new Date(lastSaved).toLocaleString()}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Error Details */}
      {errors.length > 0 && (
        <div className="space-y-2">
          {errors.map((error, index) => (
            <Alert key={index} variant={getAlertVariant(error.severity)}>
              <div className="flex items-start gap-2">
                {getIcon(error.severity)}
                <div className="flex-1">
                  <AlertDescription>
                    <span className="font-medium">{error.field}:</span> {error.message}
                  </AlertDescription>
                </div>
              </div>
            </Alert>
          ))}
        </div>
      )}

      {/* Recommendations */}
      {errors.length > 0 && (
        <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
          <CardHeader>
            <CardTitle className="text-sm flex items-center gap-2">
              <Info className="h-4 w-4" />
              Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              {errorCount > 0 && (
                <li>• Fix all errors before saving settings to ensure proper functionality</li>
              )}
              {warningCount > 0 && (
                <li>• Address warnings to optimize your store configuration</li>
              )}
              <li>• Use the validation feature regularly to maintain data quality</li>
              <li>• Settings are automatically backed up when changes are saved</li>
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Hook for validating settings
export function useSettingsValidation(settings: any) {
  const [errors, setErrors] = React.useState<ValidationError[]>([])
  const [isValid, setIsValid] = React.useState(true)

  const validateSettings = React.useCallback(() => {
    const validationErrors: ValidationError[] = []

    if (!settings) {
      setErrors([])
      setIsValid(true)
      return
    }

    // Validate required fields
    if (!settings.name || settings.name.trim().length < 2) {
      validationErrors.push({
        field: 'Store Name',
        message: 'Must be at least 2 characters long',
        severity: 'error'
      })
    }

    if (!settings.currency) {
      validationErrors.push({
        field: 'Currency',
        message: 'Currency selection is required',
        severity: 'error'
      })
    }

    if (!settings.timezone) {
      validationErrors.push({
        field: 'Timezone',
        message: 'Timezone selection is required',
        severity: 'error'
      })
    }

    // Validate email format
    if (settings.store_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(settings.store_email)) {
      validationErrors.push({
        field: 'Store Email',
        message: 'Must be a valid email address',
        severity: 'error'
      })
    }

    // Validate website URL
    if (settings.store_website) {
      try {
        new URL(settings.store_website)
      } catch {
        validationErrors.push({
          field: 'Store Website',
          message: 'Must be a valid URL (include http:// or https://)',
          severity: 'error'
        })
      }
    }

    // Validate tax rate
    if (settings.tax_rate < 0 || settings.tax_rate > 100) {
      validationErrors.push({
        field: 'Tax Rate',
        message: 'Must be between 0 and 100',
        severity: 'error'
      })
    }

    // Warnings for optional but recommended fields
    if (!settings.description || settings.description.trim().length < 10) {
      validationErrors.push({
        field: 'Store Description',
        message: 'A detailed description helps customers understand your store',
        severity: 'warning'
      })
    }

    if (!settings.store_address) {
      validationErrors.push({
        field: 'Store Address',
        message: 'Adding an address builds customer trust',
        severity: 'warning'
      })
    }

    if (!settings.store_phone) {
      validationErrors.push({
        field: 'Store Phone',
        message: 'A contact phone number improves customer confidence',
        severity: 'warning'
      })
    }

    // Info messages
    if (!settings.google_analytics_id) {
      validationErrors.push({
        field: 'Google Analytics',
        message: 'Consider adding Google Analytics to track store performance',
        severity: 'info'
      })
    }

    setErrors(validationErrors)
    setIsValid(validationErrors.filter(e => e.severity === 'error').length === 0)
  }, [settings])

  // Remove the circular dependency - validation happens automatically when settings change

  return { errors, isValid, validateSettings }
}
