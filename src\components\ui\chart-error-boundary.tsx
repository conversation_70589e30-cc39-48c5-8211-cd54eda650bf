import React, { Component, ReactNode } from 'react'
import { Alert<PERSON>riangle, RefreshCw } from 'lucide-react'
import { Button } from './button'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
}

export class ChartErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Chart Error Boundary caught an error:', error, errorInfo)
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg border border-gray-200">
          <AlertTriangle className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Chart Error</h3>
          <p className="text-sm text-gray-500 mb-4 text-center max-w-sm">
            There was an issue rendering this chart. This might be due to data formatting or animation conflicts.
          </p>
          <Button 
            onClick={this.handleRetry}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Retry
          </Button>
        </div>
      )
    }

    return this.props.children
  }
}

// Wrapper component for easy use
interface ChartWrapperProps {
  children: ReactNode
  title?: string
}

export function ChartWrapper({ children, title }: ChartWrapperProps) {
  return (
    <ChartErrorBoundary
      fallback={
        <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg border border-gray-200">
          <AlertTriangle className="h-8 w-8 text-gray-400 mb-3" />
          <h4 className="text-md font-medium text-gray-700 mb-1">
            {title ? `${title} Unavailable` : 'Chart Unavailable'}
          </h4>
          <p className="text-sm text-gray-500 text-center">
            Chart data is temporarily unavailable
          </p>
        </div>
      }
    >
      {children}
    </ChartErrorBoundary>
  )
}
