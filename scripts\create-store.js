#!/usr/bin/env node

/**
 * Womanza Admin Panel - Store Creation Script
 * 
 * This script creates a store in Firestore for testing purposes.
 */

import { initializeApp } from 'firebase/app'
import { getFirestore, doc, setDoc, getDoc, serverTimestamp } from 'firebase/firestore'
import { getAuth, signInWithEmailAndPassword } from 'firebase/auth'
import readline from 'readline'

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAxCHZm5Sywq6m8GDfy8VhgInuVG8V5jE4",
  authDomain: "womanza-store.firebaseapp.com",
  projectId: "womanza-store",
  storageBucket: "womanza-store.firebasestorage.app",
  messagingSenderId: "133271608005",
  appId: "1:133271608005:web:576cdefabd87ea7e886eac"
}

// Initialize Firebase
const app = initializeApp(firebaseConfig)
const db = getFirestore(app)
const auth = getAuth(app)

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

// Utility function to prompt user input
function prompt(question) {
  return new Promise((resolve) => {
    rl.question(question, resolve)
  })
}

async function createStore(storeId, storeData) {
  try {
    // Check if store already exists
    const storeRef = doc(db, 'stores', storeId)
    const storeSnap = await getDoc(storeRef)
    
    if (storeSnap.exists()) {
      console.log(`✅ Store '${storeId}' already exists`)
      return true
    }

    // Create store document
    await setDoc(storeRef, {
      id: storeId,
      name: storeData.name,
      slug: storeId,
      description: storeData.description,
      currency: storeData.currency,
      country: storeData.country,
      status: 'active',
      ownerId: auth.currentUser?.uid || 'system',
      contactEmail: storeData.contactEmail,
      contactPhone: storeData.contactPhone || null,
      logoUrl: null,
      primaryColor: '#8B5CF6',
      secondaryColor: '#06B6D4',
      taxRate: 0,
      settings: {
        timezone: 'America/New_York',
        language: 'en',
        theme: 'light'
      },
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    })

    console.log(`✅ Store '${storeId}' created successfully`)
    return true

  } catch (error) {
    console.error(`❌ Error creating store '${storeId}':`, error.message)
    return false
  }
}

async function main() {
  console.log('🚀 Womanza Admin Panel - Store Creation Script')
  console.log('============================================================\n')

  try {
    // Get admin credentials
    const email = await prompt('Enter admin email: ')
    const password = await prompt('Enter admin password: ')

    console.log('\n🔐 Authenticating...')
    await signInWithEmailAndPassword(auth, email, password)
    console.log('✅ Authentication successful')

    // Use default store details
    const storeId = 'womanza-jewelry-store'
    const storeData = {
      name: 'Womanza Jewelry Store',
      description: 'Premium handcrafted jewelry and accessories',
      currency: 'USD',
      country: 'US',
      contactEmail: '<EMAIL>'
    }

    console.log('\n🔄 Creating store...')
    const success = await createStore(storeId, storeData)

    if (success) {
      console.log('\n🎉 Store creation completed successfully!')
      console.log('\n📋 Store Details:')
      console.log(`   ID: ${storeId}`)
      console.log(`   Name: ${storeData.name}`)
      console.log(`   Description: ${storeData.description}`)
      console.log(`   Currency: ${storeData.currency}`)
      console.log(`   Country: ${storeData.country}`)
      console.log(`   Contact Email: ${storeData.contactEmail}`)
      console.log('\n📋 Next steps:')
      console.log('1. Run: npm run init:settings')
      console.log('2. Initialize store settings')
      console.log('3. Start your development server: npm run dev')
      console.log('4. Open http://localhost:5174')
    } else {
      console.log('\n⚠️  Store creation failed.')
    }

  } catch (error) {
    console.error('\n❌ Script failed:', error.message)
    
    if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
      console.log('\n💡 Authentication failed. Please check your credentials.')
    } else if (error.code === 'permission-denied') {
      console.log('\n💡 Permission denied. Please ensure:')
      console.log('   - Your user has admin/editor role')
      console.log('   - Firestore rules allow store write access')
    }
  } finally {
    rl.close()
    process.exit(0)
  }
}

// Run the script
main().catch(console.error)
