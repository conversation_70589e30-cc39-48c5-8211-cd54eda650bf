import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

import { formatPrice } from './currency'

export function formatCurrency(amount: number | string | null | undefined): string {
  // Handle null/undefined/empty values
  if (amount === null || amount === undefined || amount === '') {
    return formatPrice(0)
  }

  // Convert to number
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount

  // Handle invalid numbers
  if (isNaN(numAmount)) {
    return formatPrice(0)
  }

  return formatPrice(numAmount)
}

export function formatDate(date: string | Date) {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(new Date(date))
}

export function formatDateTime(date: string | Date) {
  if (!date) return 'N/A'

  const dateObj = new Date(date)
  if (isNaN(dateObj.getTime())) return 'Invalid Date'

  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(dateObj)
}

export function formatNumber(num: number): string {
  return new Intl.NumberFormat('en-US').format(num)
}

export function formatPercentage(num: number): string {
  return `${num >= 0 ? '+' : ''}${num.toFixed(1)}%`
}

export function getStatusColor(status: string): string {
  const statusColors: { [key: string]: string } = {
    // Order statuses
    pending: 'warning',
    processing: 'info',
    shipped: 'info',
    delivered: 'success',
    cancelled: 'destructive',
    completed: 'success',

    // Payment statuses
    paid: 'success',
    unpaid: 'warning',
    refunded: 'destructive',

    // Product statuses
    active: 'success',
    inactive: 'secondary',
    draft: 'warning',

    // Customer statuses
    verified: 'success',
    unverified: 'warning',
    blocked: 'destructive',
  }

  return statusColors[status.toLowerCase()] || 'secondary'
}

export function getStatusLabel(status: string): string {
  return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()
}
