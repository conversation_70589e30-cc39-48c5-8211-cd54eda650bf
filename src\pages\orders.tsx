import { useState, useEffect } from 'react'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { dataService } from '../lib/data-service'
import { DataTable, Column } from '../components/ui/data-table'
import { Button } from '../components/ui/button'
import { Badge } from '../components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { cn, formatCurrency, formatDateTime } from '../lib/utils'
import {
  ShoppingCart,
  Plus,
  Search,
  Filter,
  Download,
  Edit,
  Trash2,
  Eye,
  DollarSign,
  Package,
  Users,
  BarChart3,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  CreditCard,
  Truck,
  RefreshCw
} from 'lucide-react'

interface Order {
  id: string
  order_number: string
  customer_id?: string
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded'
  financial_status: 'pending' | 'paid' | 'partially_paid' | 'refunded' | 'voided'
  total_amount: number
  subtotal_amount?: number
  tax_amount?: number
  shipping_amount?: number
  discount_amount?: number
  currency: string
  payment_method?: string
  shipping_address?: any
  billing_address?: any
  notes?: string
  created_at: string
  updated_at: string
  customer_name?: string
  customer_email?: string
  items_count?: number
}

export function OrdersPage() {
  const { user, hasPermission } = useFirebaseAuthStore()
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    processing: 0,
    shipped: 0,
    delivered: 0,
    cancelled: 0,
    totalRevenue: 0,
    avgOrderValue: 0
  })

  const canEdit = hasPermission(['super_admin', 'admin', 'editor'])
  const canDelete = hasPermission(['super_admin', 'admin'])

  useEffect(() => {
    fetchOrders()
  }, [user])

  const fetchOrders = async () => {
    if (!user?.storeId) return

    try {
      setLoading(true)

      // Fetch real orders from Firebase
      const ordersData = await dataService.getOrders()
      setOrders(ordersData)

      // If no orders exist, show empty state instead of mock data
      if (ordersData.length === 0) {
        setOrders([])
        return
      }

      // Minimal mock data for development
      const mockOrders: Order[] = [
        {
          id: 'ord-001',
          order_number: 'ORD-2024-001',
          customer_id: 'cust-001',
          customer_name: 'John Doe',
          customer_email: '<EMAIL>',
          status: 'processing',
          financial_status: 'paid',
          total_amount: 299.99,
          subtotal_amount: 249.99,
          tax_amount: 25.00,
          shipping_amount: 25.00,
          discount_amount: 0,
          currency: 'USD',
          payment_method: 'Credit Card',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          updated_at: new Date().toISOString(),
          items_count: 1,
          shipping_address: {
            name: 'John Doe',
            street: '123 Main St',
            city: 'New York',
            state: 'NY',
            zipCode: '10001',
            country: 'USA'
          },
          notes: 'Customer requested expedited shipping'
        },
        {
          id: 'ord-002',
          order_number: 'ORD-2024-002',
          customer_id: 'cust-002',
          customer_name: 'Jane Smith',
          customer_email: '<EMAIL>',
          status: 'shipped',
          financial_status: 'paid',
          total_amount: 199.99,
          subtotal_amount: 179.99,
          tax_amount: 20.00,
          shipping_amount: 0,
          discount_amount: 10.00,
          currency: 'USD',
          payment_method: 'PayPal',
          created_at: new Date(Date.now() - *********).toISOString(),
          updated_at: new Date(Date.now() - 86400000).toISOString(),
          items_count: 2,
          shipping_address: {
            name: 'Jane Smith',
            street: '456 Oak Ave',
            city: 'Los Angeles',
            state: 'CA',
            zipCode: '90210',
            country: 'USA'
          }
        }
      ]

      setOrders(mockOrders)

      // Calculate comprehensive stats
      const total = mockOrders.length
      const pending = mockOrders.filter(o => o.status === 'pending').length
      const processing = mockOrders.filter(o => o.status === 'processing').length
      const shipped = mockOrders.filter(o => o.status === 'shipped').length
      const delivered = mockOrders.filter(o => o.status === 'delivered').length
      const cancelled = mockOrders.filter(o => o.status === 'cancelled').length
      const totalRevenue = mockOrders
        .filter(o => o.financial_status === 'paid')
        .reduce((sum, order) => sum + (order.total_amount || 0), 0)
      const avgOrderValue = total > 0 ? totalRevenue / total : 0

      setStats({ total, pending, processing, shipped, delivered, cancelled, totalRevenue, avgOrderValue })
    } catch (error) {
      console.error('Error fetching orders:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateOrderStatus = async (orderId: string, status: string) => {
    try {
      await dataService.updateOrder(orderId, { status, updatedAt: new Date().toISOString() })

      setOrders(prev => prev.map(order =>
        order.id === orderId ? { ...order, status: status as any } : order
      ))
    } catch (error) {
      console.error('Error updating order status:', error)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary"><Clock className="h-3 w-3 mr-1" />Pending</Badge>
      case 'processing':
        return <Badge className="bg-blue-100 text-blue-800"><RefreshCw className="h-3 w-3 mr-1" />Processing</Badge>
      case 'shipped':
        return <Badge className="bg-purple-100 text-purple-800"><Truck className="h-3 w-3 mr-1" />Shipped</Badge>
      case 'delivered':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Delivered</Badge>
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800"><XCircle className="h-3 w-3 mr-1" />Cancelled</Badge>
      case 'refunded':
        return <Badge className="bg-orange-100 text-orange-800"><RefreshCw className="h-3 w-3 mr-1" />Refunded</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getFinancialStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Paid</Badge>
      case 'pending':
        return <Badge variant="secondary"><Clock className="h-3 w-3 mr-1" />Pending</Badge>
      case 'partially_paid':
        return <Badge className="bg-yellow-100 text-yellow-800"><AlertTriangle className="h-3 w-3 mr-1" />Partial</Badge>
      case 'refunded':
        return <Badge className="bg-orange-100 text-orange-800"><RefreshCw className="h-3 w-3 mr-1" />Refunded</Badge>
      case 'voided':
        return <Badge className="bg-red-100 text-red-800"><XCircle className="h-3 w-3 mr-1" />Voided</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const columns: Column<Order>[] = [
    {
      key: 'order_number',
      title: 'Order',
      sortable: true,
      render: (_, order) => (
        <div>
          <p className="font-medium text-gray-900">{order.order_number}</p>
          <p className="text-sm text-gray-500">{formatDateTime(order.created_at)}</p>
        </div>
      )
    },
    {
      key: 'customer_name',
      title: 'Customer',
      sortable: true,
      render: (_, order) => (
        <div>
          <p className="font-medium text-gray-900">{order.customer_name}</p>
          <p className="text-sm text-gray-500">{order.customer_email}</p>
        </div>
      )
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      render: (status) => getStatusBadge(status)
    },
    {
      key: 'financial_status',
      title: 'Payment',
      sortable: true,
      render: (status) => getFinancialStatusBadge(status)
    },
    {
      key: 'total_amount',
      title: 'Total',
      sortable: true,
      render: (amount) => (
        <div className="font-medium">
          {formatCurrency(amount || 0)}
        </div>
      )
    },
    {
      key: 'items_count',
      title: 'Items',
      render: (count) => `${count || 0} items`
    }
  ]

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Orders</h1>
          <p className="text-gray-500">Manage customer orders and fulfillment</p>
        </div>
        <Button onClick={() => fetchOrders()}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing</CardTitle>
            <RefreshCw className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.processing}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Shipped</CardTitle>
            <Truck className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{stats.shipped}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delivered</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.delivered}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cancelled</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.cancelled}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatCurrency(stats.totalRevenue)}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Order</CardTitle>
            <BarChart3 className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{formatCurrency(stats.avgOrderValue)}</div>
          </CardContent>
        </Card>
      </div>

      {/* Orders Table */}
      <DataTable
        data={orders}
        columns={columns}
        loading={loading}
        searchable={true}
        filterable={true}
        exportable={true}
        actions={{
          view: (order) => setSelectedOrder(order),
          edit: canEdit ? (order) => {
            console.log('Edit order:', order)
            // TODO: Implement order editing
          } : undefined,
          delete: canDelete ? (order) => {
            if (window.confirm('Are you sure you want to delete this order?')) {
              console.log('Delete order:', order)
              // TODO: Implement order deletion
            }
          } : undefined
        }}
        emptyState={{
          title: 'No orders found',
          description: 'Orders will appear here once customers start purchasing.',
          icon: <ShoppingCart className="h-6 w-6 text-gray-400" />
        }}
      />

      {/* Order Detail Modal */}
      {selectedOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Modal Header */}
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    Order Details
                  </h2>
                  <p className="text-gray-500">{selectedOrder.order_number}</p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedOrder(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </Button>
              </div>

              {/* Order Information Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                {/* Order Summary */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <ShoppingCart className="h-5 w-5" />
                      Order Summary
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Order Number:</span>
                      <span className="font-medium">{selectedOrder.order_number}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Status:</span>
                      {getStatusBadge(selectedOrder.status)}
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Payment Status:</span>
                      {getFinancialStatusBadge(selectedOrder.financial_status)}
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Payment Method:</span>
                      <span className="font-medium">{selectedOrder.payment_method || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Order Date:</span>
                      <span className="font-medium">{formatDateTime(selectedOrder.created_at)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Last Updated:</span>
                      <span className="font-medium">{formatDateTime(selectedOrder.updated_at)}</span>
                    </div>
                  </CardContent>
                </Card>

                {/* Customer Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      Customer Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Name:</span>
                      <span className="font-medium">{selectedOrder.customer_name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Email:</span>
                      <span className="font-medium">{selectedOrder.customer_email}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Customer ID:</span>
                      <span className="font-mono text-sm">{selectedOrder.customer_id}</span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Pricing Breakdown */}
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Pricing Breakdown
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Subtotal:</span>
                      <span>{formatCurrency(selectedOrder.subtotal_amount || 0)}</span>
                    </div>
                    {selectedOrder.discount_amount && selectedOrder.discount_amount > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>Discount:</span>
                        <span>-{formatCurrency(selectedOrder.discount_amount)}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-gray-600">Tax:</span>
                      <span>{formatCurrency(selectedOrder.tax_amount || 0)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Shipping:</span>
                      <span>{formatCurrency(selectedOrder.shipping_amount || 0)}</span>
                    </div>
                    <div className="border-t pt-2 flex justify-between font-bold text-lg">
                      <span>Total:</span>
                      <span>{formatCurrency(selectedOrder.total_amount)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Shipping Address */}
              {selectedOrder.shipping_address && (
                <Card className="mb-6">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Truck className="h-5 w-5" />
                      Shipping Address
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-sm">
                      <p className="font-medium">{selectedOrder.shipping_address.name}</p>
                      <p>{selectedOrder.shipping_address.street}</p>
                      <p>
                        {selectedOrder.shipping_address.city}, {selectedOrder.shipping_address.state} {selectedOrder.shipping_address.zipCode}
                      </p>
                      <p>{selectedOrder.shipping_address.country}</p>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Notes */}
              {selectedOrder.notes && (
                <Card className="mb-6">
                  <CardHeader>
                    <CardTitle>Order Notes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-700 dark:text-gray-300">{selectedOrder.notes}</p>
                  </CardContent>
                </Card>
              )}

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4 border-t">
                <Button
                  onClick={() => {
                    console.log('Update order status for:', selectedOrder.id)
                    // TODO: Implement status update
                  }}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Update Status
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    console.log('Print order:', selectedOrder.id)
                    // TODO: Implement print functionality
                  }}
                >
                  Print Order
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    console.log('Send email for order:', selectedOrder.id)
                    // TODO: Implement email functionality
                  }}
                >
                  Send Email
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setSelectedOrder(null)}
                >
                  Close
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
