import { useState, useEffect } from 'react'
import { doc, onSnapshot } from 'firebase/firestore'
import { db } from '../lib/firebase'
import { useStoreManagement } from '../store/store-management'
import { useFirebaseAuthStore } from '../store/firebase-auth'

interface StoreNameData {
  storeName: string
  storeSlug: string
  loading: boolean
  error: string | null
}

/**
 * Hook to get real-time store name from Firebase settings
 * Falls back to store document name if settings not available
 */
export function useStoreName(): StoreNameData {
  const { currentStoreId, currentStore } = useStoreManagement()
  const { user } = useFirebaseAuthStore()
  const [storeName, setStoreName] = useState<string>('')
  const [storeSlug, setStoreSlug] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Determine store ID
  const resolvedStoreId = currentStoreId || user?.primaryStoreId || user?.storeId || 'womanza-jewelry-store'

  useEffect(() => {
    if (!resolvedStoreId) {
      setLoading(false)
      setError('No store ID available')
      return
    }

    setLoading(true)
    setError(null)

    // First, try to get store name from general settings (real-time)
    const settingsRef = doc(db, 'stores', resolvedStoreId, 'config', 'settings')

    const unsubscribeSettings = onSnapshot(
      settingsRef,
      (settingsDoc) => {
        if (settingsDoc.exists()) {
          const data = settingsDoc.data()
          if (data.general?.storeName) {
            setStoreName(data.general.storeName)
            setStoreSlug(data.general.storeSlug || '')
            setLoading(false)
            console.log('📡 Store name updated from settings:', data.general.storeName)
            return
          }
        }

        // Fallback to main store document if settings don't have store name
        console.log('📡 Settings not found, falling back to store document')
        const storeRef = doc(db, 'stores', resolvedStoreId)
        onSnapshot(
          storeRef,
          (storeDoc) => {
            if (storeDoc.exists()) {
              const storeData = storeDoc.data()
              setStoreName(storeData.name || 'Womanza Store')
              setStoreSlug(storeData.slug || '')
              console.log('📡 Store name updated from store document:', storeData.name)
            } else {
              setStoreName('Womanza Store')
              setStoreSlug('')
              setError('Store not found')
            }
            setLoading(false)
          },
          (error) => {
            console.error('Error fetching store document:', error)
            setStoreName('Womanza Store')
            setStoreSlug('')
            setError('Failed to fetch store name')
            setLoading(false)
          }
        )
      },
      (error) => {
        console.error('Error fetching store settings:', error)
        // Fallback to store document on error
        const storeRef = doc(db, 'stores', resolvedStoreId)
        onSnapshot(
          storeRef,
          (storeDoc) => {
            if (storeDoc.exists()) {
              const storeData = storeDoc.data()
              setStoreName(storeData.name || 'Womanza Store')
              setStoreSlug(storeData.slug || '')
              console.log('📡 Store name updated from store document:', storeData.name)
            } else {
              setStoreName('Womanza Store')
              setStoreSlug('')
              setError('Store not found')
            }
            setLoading(false)
          },
          (error) => {
            console.error('Error fetching store document:', error)
            setStoreName('Womanza Store')
            setStoreSlug('')
            setError('Failed to fetch store name')
            setLoading(false)
          }
        )
      }
    )

    // Cleanup function
    return () => {
      unsubscribeSettings()
    }
  }, [resolvedStoreId])

  // Fallback to currentStore name if available
  useEffect(() => {
    if (!storeName && currentStore?.name && !loading) {
      setStoreName(currentStore.name)
      setStoreSlug(currentStore.slug || '')
    }
  }, [currentStore, storeName, loading])

  return {
    storeName: storeName || 'Womanza Store',
    storeSlug: storeSlug || '',
    loading,
    error
  }
}
