import React, { useState, useMemo } from 'react'
import { But<PERSON> } from './button'
import { Input } from './input'
import { Badge } from './badge'
import { cn } from '../../lib/utils'
import {
  Search,
  Filter,
  Download,
  Plus,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Eye,
  Edit,
  Trash2,
  Grid,
  List
} from 'lucide-react'

export interface Column<T> {
  key: keyof T | string
  title: string
  sortable?: boolean
  filterable?: boolean
  render?: (value: any, item: T) => React.ReactNode
  width?: string
}

export interface DataTableProps<T> {
  data: T[]
  columns: Column<T>[]
  loading?: boolean
  searchable?: boolean
  filterable?: boolean
  exportable?: boolean
  viewModeToggle?: {
    currentMode: 'table' | 'grid'
    onModeChange: (mode: 'table' | 'grid') => void
  }
  addButton?: {
    label: string
    onClick: () => void
  }
  actions?: {
    view?: (item: T) => void
    edit?: (item: T) => void
    delete?: (item: T) => void
  }
  actionButtonProps?: {
    size?: 'sm' | 'md' | 'lg'
    className?: string
  }
  onRowClick?: (item: T) => void
  className?: string
  emptyState?: {
    title: string
    description: string
    icon?: React.ReactNode
  }
}

export function DataTable<T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  searchable = true,
  filterable = false,
  exportable = false,
  viewModeToggle,
  addButton,
  actions,
  actionButtonProps,
  onRowClick,
  className,
  emptyState
}: DataTableProps<T>) {
  const [searchTerm, setSearchTerm] = useState('')
  const [sortConfig, setSortConfig] = useState<{
    key: string
    direction: 'asc' | 'desc'
  } | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(10)
  const [showFilters, setShowFilters] = useState(false)
  const [columnFilters, setColumnFilters] = useState<Record<string, string>>({})

  // Filter and search data
  const filteredData = useMemo(() => {
    let filtered = [...data]

    // Apply search
    if (searchTerm) {
      filtered = filtered.filter(item =>
        Object.values(item).some(value =>
          String(value).toLowerCase().includes(searchTerm.toLowerCase())
        )
      )
    }

    // Apply column filters
    Object.entries(columnFilters).forEach(([columnKey, filterValue]) => {
      if (filterValue) {
        filtered = filtered.filter(item => {
          const itemValue = String(item[columnKey] || '').toLowerCase()
          return itemValue.includes(filterValue.toLowerCase())
        })
      }
    })

    // Apply sorting
    if (sortConfig) {
      filtered.sort((a, b) => {
        const aValue = a[sortConfig.key]
        const bValue = b[sortConfig.key]

        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1
        }
        return 0
      })
    }

    return filtered
  }, [data, searchTerm, sortConfig, columnFilters])

  // Pagination
  const totalPages = Math.ceil(filteredData.length / pageSize)
  const paginatedData = filteredData.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  )

  const handleSort = (key: string) => {
    setSortConfig(current => {
      if (current?.key === key) {
        return {
          key,
          direction: current.direction === 'asc' ? 'desc' : 'asc'
        }
      }
      return { key, direction: 'asc' }
    })
  }

  const getSortIcon = (key: string) => {
    if (sortConfig?.key !== key) {
      return <ArrowUpDown className="h-4 w-4" />
    }
    return sortConfig.direction === 'asc' 
      ? <ArrowUp className="h-4 w-4" />
      : <ArrowDown className="h-4 w-4" />
  }

  const renderCellValue = (column: Column<T>, item: T) => {
    const value = item[column.key as keyof T]
    
    if (column.render) {
      return column.render(value, item)
    }
    
    return String(value || '')
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="h-10 w-64 bg-gray-200 rounded animate-pulse" />
          <div className="h-10 w-32 bg-gray-200 rounded animate-pulse" />
        </div>
        <div className="border rounded-lg">
          <div className="h-12 bg-gray-100 border-b" />
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 border-b last:border-b-0 bg-gray-50 animate-pulse" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {searchable && (
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
          )}
          {filterable && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className={showFilters ? 'bg-blue-50 border-blue-300 text-blue-700' : ''}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          )}
        </div>

        <div className="flex items-center gap-2">
          {viewModeToggle && (
            <div className="flex items-center bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
              <Button
                variant={viewModeToggle.currentMode === 'table' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => viewModeToggle.onModeChange('table')}
                className={cn(
                  "h-8 px-3 rounded-md",
                  viewModeToggle.currentMode === 'table'
                    ? 'bg-white dark:bg-gray-700 shadow-sm'
                    : 'hover:bg-gray-200 dark:hover:bg-gray-700'
                )}
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                variant={viewModeToggle.currentMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => viewModeToggle.onModeChange('grid')}
                className={cn(
                  "h-8 px-3 rounded-md",
                  viewModeToggle.currentMode === 'grid'
                    ? 'bg-white dark:bg-gray-700 shadow-sm'
                    : 'hover:bg-gray-200 dark:hover:bg-gray-700'
                )}
              >
                <Grid className="h-4 w-4" />
              </Button>
            </div>
          )}
          {exportable && (
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          )}
          {addButton && (
            <Button onClick={addButton.onClick} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              {addButton.label}
            </Button>
          )}
        </div>
      </div>

      {/* Column Filters */}
      {showFilters && (
        <div className="bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {columns
              .filter(column => column.filterable !== false)
              .map((column) => (
                <div key={String(column.key)}>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {column.title}
                  </label>
                  <Input
                    placeholder={`Filter by ${column.title.toLowerCase()}...`}
                    value={columnFilters[String(column.key)] || ''}
                    onChange={(e) => setColumnFilters(prev => ({
                      ...prev,
                      [String(column.key)]: e.target.value
                    }))}
                    className="text-sm"
                  />
                </div>
              ))}
          </div>
          {Object.values(columnFilters).some(value => value) && (
            <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setColumnFilters({})}
                className="text-gray-600 dark:text-gray-400"
              >
                Clear All Filters
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Table */}
      <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-white dark:bg-gray-800 shadow-sm">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-800/50 border-b border-gray-200 dark:border-gray-700">
              <tr>
                {columns.map((column) => (
                  <th
                    key={String(column.key)}
                    className={cn(
                      "px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",
                      column.sortable && "cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-colors",
                      column.width && `w-${column.width}`
                    )}
                    onClick={() => column.sortable && handleSort(String(column.key))}
                  >
                    <div className="flex items-center gap-2">
                      {column.title}
                      {column.sortable && getSortIcon(String(column.key))}
                    </div>
                  </th>
                ))}
                {actions && (
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                )}
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {paginatedData.length === 0 ? (
                <tr>
                  <td colSpan={columns.length + (actions ? 1 : 0)} className="px-6 py-12 text-center">
                    <div className="flex flex-col items-center justify-center space-y-4">
                      {emptyState?.icon && (
                        <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                          {emptyState.icon}
                        </div>
                      )}
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">
                          {emptyState?.title || 'No data found'}
                        </h3>
                        <p className="text-sm text-gray-500 mt-1">
                          {emptyState?.description || 'Try adjusting your search or filter criteria.'}
                        </p>
                      </div>
                    </div>
                  </td>
                </tr>
              ) : (
                paginatedData.map((item, index) => (
                  <tr
                    key={index}
                    className={cn(
                      "hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors",
                      onRowClick && "cursor-pointer"
                    )}
                    onClick={() => onRowClick?.(item)}
                  >
                    {columns.map((column) => (
                      <td key={String(column.key)} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {renderCellValue(column, item)}
                      </td>
                    ))}
                    {actions && (
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end gap-1">
                          {actions.view && (
                            <Button
                              variant="ghost"
                              size={actionButtonProps?.size || "sm"}
                              onClick={(e) => {
                                e.stopPropagation()
                                actions.view!(item)
                              }}
                              className={cn(
                                "hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400",
                                actionButtonProps?.className
                              )}
                              title="View"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          )}
                          {actions.edit && (
                            <Button
                              variant="ghost"
                              size={actionButtonProps?.size || "sm"}
                              onClick={(e) => {
                                e.stopPropagation()
                                actions.edit!(item)
                              }}
                              className={cn(
                                "hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400",
                                actionButtonProps?.className
                              )}
                              title="Edit"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          )}
                          {actions.delete && (
                            <Button
                              variant="ghost"
                              size={actionButtonProps?.size || "sm"}
                              onClick={(e) => {
                                e.stopPropagation()
                                actions.delete!(item)
                              }}
                              className={cn(
                                "hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400",
                                actionButtonProps?.className
                              )}
                              title="Delete"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </td>
                    )}
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between bg-white dark:bg-gray-800 px-6 py-3 border-t border-gray-200 dark:border-gray-700">
          <div className="text-sm text-gray-700 dark:text-gray-300">
            Showing {(currentPage - 1) * pageSize + 1} to{' '}
            {Math.min(currentPage * pageSize, filteredData.length)} of{' '}
            {filteredData.length} results
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
              disabled={currentPage === 1}
              className="border-gray-300 dark:border-gray-600"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <span className="text-sm text-gray-700 dark:text-gray-300 px-3">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
              disabled={currentPage === totalPages}
              className="border-gray-300 dark:border-gray-600"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
