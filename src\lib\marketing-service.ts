import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  increment,
  onSnapshot,
  Unsubscribe
} from 'firebase/firestore'
import { db } from './firebase'

// Marketing Types based on marketing.md specifications

export interface Coupon {
  id: string
  storeId: string
  code: string
  discountType: 'percentage' | 'flat' | 'free_shipping'
  discountValue: number
  usageLimit: number
  usedCount: number
  minCartValue?: number
  customerGroup?: string
  applicableProducts?: string[]
  applicableCategories?: string[]
  active: boolean
  expiresAt: string
  assignedTo?: string | null // groupId or null for public
  createdAt: string
  updatedAt: string
  createdBy: string
}

export interface Promotion {
  id: string
  storeId: string
  title: string
  description: string
  discountType: 'percentage' | 'fixed_amount' | 'free_shipping'
  discountValue: number
  applicableCategories?: string[]
  applicableProducts?: string[]
  minCartValue?: number
  customerGroup?: string
  startDate: string
  endDate: string
  autoApply: boolean
  active: boolean
  priority: number
  imageUrl?: string
  createdAt: string
  updatedAt: string
  createdBy: string
}

export interface Banner {
  id: string
  storeId: string
  title: string
  imageUrl: string
  ctaLink?: string
  ctaText?: string
  position: 'homepage' | 'category' | 'product' | 'checkout'
  startDate: string
  endDate: string
  active: boolean
  clickCount: number
  impressionCount: number
  priority: number
  createdAt: string
  updatedAt: string
  createdBy: string
}

export interface EmailCampaign {
  id: string
  storeId: string
  title: string
  targetGroup: 'all' | 'customers' | 'subscribers' | 'group'
  targetGroupId?: string
  subject: string
  htmlContent: string
  attachedPromotionId?: string
  status: 'draft' | 'scheduled' | 'sent' | 'cancelled'
  scheduledAt?: string
  sentAt?: string
  recipientCount: number
  openCount: number
  clickCount: number
  createdAt: string
  updatedAt: string
  createdBy: string
}

export interface MarketingLog {
  id: string
  storeId: string
  type: 'coupon_used' | 'promotion_applied' | 'banner_clicked' | 'email_opened' | 'email_clicked'
  entityId: string // coupon/promotion/banner/campaign ID
  entityType: 'coupon' | 'promotion' | 'banner' | 'email_campaign'
  customerId?: string
  orderId?: string
  revenue?: number
  metadata?: Record<string, any>
  timestamp: string
}

export interface MarketingInsights {
  totalCoupons: number
  activeCoupons: number
  totalPromotions: number
  activePromotions: number
  totalBanners: number
  activeBanners: number
  totalCampaigns: number
  couponUsageRate: number
  promotionRevenue: number
  topPerformingCoupons: Array<{
    id: string
    code: string
    usageCount: number
    revenue: number
  }>
  topPerformingPromotions: Array<{
    id: string
    title: string
    revenue: number
    orderCount: number
  }>
  bannerClickStats: Array<{
    id: string
    title: string
    clicks: number
    impressions: number
    ctr: number
  }>
}

export class MarketingService {
  private storeId: string
  private listeners: Unsubscribe[] = []

  constructor(storeId: string) {
    this.storeId = storeId
  }

  // Clean up all listeners
  cleanup() {
    this.listeners.forEach(unsubscribe => unsubscribe())
    this.listeners = []
  }

  // ==================== REAL-TIME LISTENERS ====================

  // Real-time coupon listener
  onCouponsChange(callback: (coupons: Coupon[]) => void): Unsubscribe {
    const q = query(
      collection(db, 'stores', this.storeId, 'coupons'),
      orderBy('createdAt', 'desc')
    )

    const unsubscribe = onSnapshot(q, (snapshot) => {
      try {
        const coupons = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
          updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
          expiresAt: doc.data().expiresAt?.toDate?.()?.toISOString() || doc.data().expiresAt
        } as Coupon))
        callback(coupons)
      } catch (error) {
        console.error('Error in coupons listener:', error)
        callback(this.getSampleCoupons())
      }
    }, (error) => {
      console.error('Coupons listener error:', error)
      callback(this.getSampleCoupons())
    })

    this.listeners.push(unsubscribe)
    return unsubscribe
  }

  // Real-time promotions listener
  onPromotionsChange(callback: (promotions: Promotion[]) => void): Unsubscribe {
    const q = query(
      collection(db, 'stores', this.storeId, 'promotions'),
      orderBy('createdAt', 'desc')
    )

    const unsubscribe = onSnapshot(q, (snapshot) => {
      try {
        const promotions = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
          updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
          startDate: doc.data().startDate?.toDate?.()?.toISOString() || doc.data().startDate,
          endDate: doc.data().endDate?.toDate?.()?.toISOString() || doc.data().endDate
        } as Promotion))
        callback(promotions)
      } catch (error) {
        console.error('Error in promotions listener:', error)
        callback(this.getSamplePromotions())
      }
    }, (error) => {
      console.error('Promotions listener error:', error)
      callback(this.getSamplePromotions())
    })

    this.listeners.push(unsubscribe)
    return unsubscribe
  }

  // Real-time banners listener
  onBannersChange(callback: (banners: Banner[]) => void): Unsubscribe {
    const q = query(
      collection(db, 'stores', this.storeId, 'banners'),
      orderBy('createdAt', 'desc')
    )

    const unsubscribe = onSnapshot(q, (snapshot) => {
      try {
        const banners = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
          updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
          startDate: doc.data().startDate?.toDate?.()?.toISOString() || doc.data().startDate,
          endDate: doc.data().endDate?.toDate?.()?.toISOString() || doc.data().endDate
        } as Banner))
        callback(banners)
      } catch (error) {
        console.error('Error in banners listener:', error)
        callback(this.getSampleBanners())
      }
    }, (error) => {
      console.error('Banners listener error:', error)
      callback(this.getSampleBanners())
    })

    this.listeners.push(unsubscribe)
    return unsubscribe
  }

  // ==================== COUPONS ====================

  async getCoupons(): Promise<Coupon[]> {
    try {
      const q = query(
        collection(db, 'stores', this.storeId, 'coupons'),
        orderBy('createdAt', 'desc')
      )
      const snapshot = await getDocs(q)
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
        updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
        expiresAt: doc.data().expiresAt?.toDate?.()?.toISOString() || doc.data().expiresAt
      } as Coupon))
    } catch (error) {
      console.error('Error fetching coupons:', error)
      // Return sample data for demo
      return this.getSampleCoupons()
    }
  }

  async getCoupon(id: string): Promise<Coupon | null> {
    try {
      const docRef = doc(db, 'stores', this.storeId, 'coupons', id)
      const docSnap = await getDoc(docRef)

      if (!docSnap.exists()) {
        return null
      }

      const data = docSnap.data()
      return {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
        expiresAt: data.expiresAt?.toDate?.()?.toISOString() || data.expiresAt
      } as Coupon
    } catch (error) {
      console.error('Error fetching coupon:', error)
      return null
    }
  }

  async createCoupon(couponData: Omit<Coupon, 'id' | 'createdAt' | 'updatedAt' | 'storeId' | 'usedCount'>): Promise<Coupon> {
    try {
      const docRef = await addDoc(collection(db, 'stores', this.storeId, 'coupons'), {
        ...couponData,
        storeId: this.storeId,
        usedCount: 0,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      })

      const docSnap = await getDoc(docRef)
      const data = docSnap.data()!
      return {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
        expiresAt: data.expiresAt?.toDate?.()?.toISOString() || data.expiresAt
      } as Coupon
    } catch (error) {
      console.error('Error creating coupon:', error)
      throw new Error('Failed to create coupon')
    }
  }

  async updateCoupon(id: string, updates: Partial<Coupon>): Promise<Coupon> {
    try {
      const docRef = doc(db, 'stores', this.storeId, 'coupons', id)
      await updateDoc(docRef, {
        ...updates,
        updatedAt: serverTimestamp()
      })

      const docSnap = await getDoc(docRef)
      const data = docSnap.data()!
      return {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
        expiresAt: data.expiresAt?.toDate?.()?.toISOString() || data.expiresAt
      } as Coupon
    } catch (error) {
      console.error('Error updating coupon:', error)
      throw new Error('Failed to update coupon')
    }
  }

  async deleteCoupon(id: string): Promise<void> {
    try {
      await deleteDoc(doc(db, 'stores', this.storeId, 'coupons', id))
    } catch (error) {
      console.error('Error deleting coupon:', error)
      throw new Error('Failed to delete coupon')
    }
  }

  // ==================== PROMOTIONS ====================

  async getPromotions(): Promise<Promotion[]> {
    try {
      // Try to fetch from Firestore, but fall back to sample data if there are issues
      const q = query(
        collection(db, 'stores', this.storeId, 'promotions'),
        orderBy('createdAt', 'desc')
      )
      const snapshot = await getDocs(q)

      if (snapshot.empty) {
        console.log('No promotions found, returning sample data')
        return this.getSamplePromotions()
      }

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
        updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
        startDate: doc.data().startDate?.toDate?.()?.toISOString() || doc.data().startDate,
        endDate: doc.data().endDate?.toDate?.()?.toISOString() || doc.data().endDate
      } as Promotion))
    } catch (error) {
      console.error('Error fetching promotions:', error)
      console.log('Falling back to sample promotions data')
      return this.getSamplePromotions()
    }
  }

  async getPromotion(id: string): Promise<Promotion | null> {
    try {
      const docRef = doc(db, 'stores', this.storeId, 'promotions', id)
      const docSnap = await getDoc(docRef)

      if (!docSnap.exists()) {
        return null
      }

      const data = docSnap.data()
      return {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
        startDate: data.startDate?.toDate?.()?.toISOString() || data.startDate,
        endDate: data.endDate?.toDate?.()?.toISOString() || data.endDate
      } as Promotion
    } catch (error) {
      console.error('Error fetching promotion:', error)
      return null
    }
  }

  async createPromotion(promotionData: Omit<Promotion, 'id' | 'createdAt' | 'updatedAt' | 'storeId'>): Promise<Promotion> {
    try {
      const docRef = await addDoc(collection(db, 'stores', this.storeId, 'promotions'), {
        ...promotionData,
        storeId: this.storeId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      })

      const docSnap = await getDoc(docRef)
      const data = docSnap.data()!
      return {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
        startDate: data.startDate?.toDate?.()?.toISOString() || data.startDate,
        endDate: data.endDate?.toDate?.()?.toISOString() || data.endDate
      } as Promotion
    } catch (error) {
      console.error('Error creating promotion:', error)
      throw new Error('Failed to create promotion')
    }
  }

  async updatePromotion(id: string, updates: Partial<Promotion>): Promise<Promotion> {
    try {
      const docRef = doc(db, 'stores', this.storeId, 'promotions', id)
      await updateDoc(docRef, {
        ...updates,
        updatedAt: serverTimestamp()
      })

      const docSnap = await getDoc(docRef)
      const data = docSnap.data()!
      return {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
        startDate: data.startDate?.toDate?.()?.toISOString() || data.startDate,
        endDate: data.endDate?.toDate?.()?.toISOString() || data.endDate
      } as Promotion
    } catch (error) {
      console.error('Error updating promotion:', error)
      throw new Error('Failed to update promotion')
    }
  }

  async deletePromotion(id: string): Promise<void> {
    try {
      await deleteDoc(doc(db, 'stores', this.storeId, 'promotions', id))
    } catch (error) {
      console.error('Error deleting promotion:', error)
      throw new Error('Failed to delete promotion')
    }
  }

  // ==================== BANNERS ====================

  async getBanners(): Promise<Banner[]> {
    try {
      const q = query(
        collection(db, 'stores', this.storeId, 'banners'),
        orderBy('createdAt', 'desc')
      )
      const snapshot = await getDocs(q)
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
        updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
        startDate: doc.data().startDate?.toDate?.()?.toISOString() || doc.data().startDate,
        endDate: doc.data().endDate?.toDate?.()?.toISOString() || doc.data().endDate
      } as Banner))
    } catch (error) {
      console.error('Error fetching banners:', error)
      return this.getSampleBanners()
    }
  }
  // ==================== MARKETING INSIGHTS ====================

  async getMarketingInsights(): Promise<MarketingInsights> {
    try {
      const [coupons, promotions, banners] = await Promise.all([
        this.getCoupons(),
        this.getPromotions(),
        this.getBanners()
      ])

      const activeCoupons = coupons.filter(c => c.active)
      const activePromotions = promotions.filter(p => p.active)
      const activeBanners = banners.filter(b => b.active)

      // Calculate usage rate
      const totalCouponUsage = coupons.reduce((sum, c) => sum + c.usedCount, 0)
      const totalCouponLimit = coupons.reduce((sum, c) => sum + c.usageLimit, 0)
      const couponUsageRate = totalCouponLimit > 0 ? (totalCouponUsage / totalCouponLimit) * 100 : 0

      return {
        totalCoupons: coupons.length,
        activeCoupons: activeCoupons.length,
        totalPromotions: promotions.length,
        activePromotions: activePromotions.length,
        totalBanners: banners.length,
        activeBanners: activeBanners.length,
        totalCampaigns: 0, // TODO: Implement email campaigns
        couponUsageRate,
        promotionRevenue: 0, // TODO: Calculate from orders
        topPerformingCoupons: coupons
          .sort((a, b) => b.usedCount - a.usedCount)
          .slice(0, 5)
          .map(c => ({
            id: c.id,
            code: c.code,
            usageCount: c.usedCount,
            revenue: 0 // TODO: Calculate from orders
          })),
        topPerformingPromotions: promotions
          .slice(0, 5)
          .map(p => ({
            id: p.id,
            title: p.title,
            revenue: 0, // TODO: Calculate from orders
            orderCount: 0 // TODO: Calculate from orders
          })),
        bannerClickStats: banners
          .map(b => ({
            id: b.id,
            title: b.title,
            clicks: b.clickCount,
            impressions: b.impressionCount,
            ctr: b.impressionCount > 0 ? (b.clickCount / b.impressionCount) * 100 : 0
          }))
      }
    } catch (error) {
      console.error('Error fetching marketing insights:', error)
      return this.getSampleInsights()
    }
  }

  async incrementCouponUsage(id: string): Promise<void> {
    try {
      const docRef = doc(db, 'coupons', id)
      await updateDoc(docRef, {
        usedCount: increment(1),
        updatedAt: serverTimestamp()
      })
    } catch (error) {
      console.error('Error incrementing coupon usage:', error)
      throw new Error('Failed to update coupon usage')
    }
  }



  async getPromotion(id: string): Promise<Promotion | null> {
    try {
      const docRef = doc(db, 'promotions', id)
      const docSnap = await getDoc(docRef)
      
      if (!docSnap.exists()) {
        return null
      }

      const data = docSnap.data()
      return {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
        startDate: data.startDate?.toDate?.()?.toISOString() || data.startDate,
        endDate: data.endDate?.toDate?.()?.toISOString() || data.endDate
      } as Promotion
    } catch (error) {
      console.error('Error fetching promotion:', error)
      throw new Error('Failed to fetch promotion')
    }
  }



  // Real-time subscriptions
  subscribeToCoupons(callback: (coupons: Coupon[]) => void): () => void {
    const q = query(
      collection(db, 'coupons'),
      where('storeId', '==', this.storeId),
      orderBy('createdAt', 'desc')
    )
    
    return onSnapshot(q, (snapshot) => {
      const coupons = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
        updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
        validFrom: doc.data().validFrom?.toDate?.()?.toISOString() || doc.data().validFrom,
        validTo: doc.data().validTo?.toDate?.()?.toISOString() || doc.data().validTo
      } as Coupon))
      callback(coupons)
    }, (error) => {
      console.error('Error in coupons subscription:', error)
    })
  }

  subscribeToPromotions(callback: (promotions: Promotion[]) => void): () => void {
    const q = query(
      collection(db, 'promotions'),
      where('storeId', '==', this.storeId),
      orderBy('priority', 'desc'),
      orderBy('createdAt', 'desc')
    )
    
    return onSnapshot(q, (snapshot) => {
      const promotions = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
        updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
        startDate: doc.data().startDate?.toDate?.()?.toISOString() || doc.data().startDate,
        endDate: doc.data().endDate?.toDate?.()?.toISOString() || doc.data().endDate
      } as Promotion))
      callback(promotions)
    }, (error) => {
      console.error('Error in promotions subscription:', error)
    })
  }

  // ==================== SAMPLE DATA ====================

  private getSampleCoupons(): Coupon[] {
    return [
      {
        id: 'sample-coupon-1',
        storeId: this.storeId,
        code: 'WOMANZA20',
        discountType: 'percentage',
        discountValue: 20,
        usageLimit: 100,
        usedCount: 45,
        minCartValue: 5000,
        active: true,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        assignedTo: null,
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'admin'
      },
      {
        id: 'sample-coupon-2',
        storeId: this.storeId,
        code: 'FLAT1000',
        discountType: 'flat',
        discountValue: 1000,
        usageLimit: 50,
        usedCount: 12,
        minCartValue: 3000,
        active: true,
        expiresAt: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
        assignedTo: null,
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'admin'
      },
      {
        id: 'sample-coupon-3',
        storeId: this.storeId,
        code: 'FREESHIP',
        discountType: 'free_shipping',
        discountValue: 0,
        usageLimit: 200,
        usedCount: 89,
        minCartValue: 2000,
        active: true,
        expiresAt: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(),
        assignedTo: null,
        createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'admin'
      }
    ]
  }

  private getSamplePromotions(): Promotion[] {
    return [
      {
        id: 'sample-promotion-1',
        storeId: this.storeId,
        title: 'Summer Sale',
        description: '20% off on all rings and earrings',
        discountType: 'percentage',
        discountValue: 20,
        applicableCategories: ['rings', 'earrings'],
        minCartValue: 3000,
        startDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000).toISOString(),
        autoApply: true,
        active: true,
        priority: 1,
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'admin'
      },
      {
        id: 'sample-promotion-2',
        storeId: this.storeId,
        title: 'New Customer Discount',
        description: 'Flat ₹1500 off for first-time buyers',
        discountType: 'fixed_amount',
        discountValue: 1500,
        customerGroup: 'new_customers',
        minCartValue: 5000,
        startDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date(Date.now() + 50 * 24 * 60 * 60 * 1000).toISOString(),
        autoApply: false,
        active: true,
        priority: 2,
        createdAt: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'admin'
      }
    ]
  }

  private getSampleBanners(): Banner[] {
    return [
      {
        id: 'sample-banner-1',
        storeId: this.storeId,
        title: 'Summer Collection',
        imageUrl: 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=800',
        ctaLink: '/collections/summer',
        ctaText: 'Shop Now',
        position: 'homepage',
        startDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000).toISOString(),
        active: true,
        clickCount: 156,
        impressionCount: 2340,
        priority: 1,
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'admin'
      },
      {
        id: 'sample-banner-2',
        storeId: this.storeId,
        title: 'Free Shipping',
        imageUrl: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800',
        ctaLink: '/shipping-info',
        ctaText: 'Learn More',
        position: 'category',
        startDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(),
        active: true,
        clickCount: 89,
        impressionCount: 1567,
        priority: 2,
        createdAt: new Date(Date.now() - 18 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'admin'
      }
    ]
  }

  private getSampleInsights(): MarketingInsights {
    const coupons = this.getSampleCoupons()
    const promotions = this.getSamplePromotions()
    const banners = this.getSampleBanners()

    return {
      totalCoupons: coupons.length,
      activeCoupons: coupons.filter(c => c.active).length,
      totalPromotions: promotions.length,
      activePromotions: promotions.filter(p => p.active).length,
      totalBanners: banners.length,
      activeBanners: banners.filter(b => b.active).length,
      totalCampaigns: 0,
      couponUsageRate: 45.6,
      promotionRevenue: 125000,
      topPerformingCoupons: [
        { id: 'sample-coupon-3', code: 'FREESHIP', usageCount: 89, revenue: 45000 },
        { id: 'sample-coupon-1', code: 'WOMANZA20', usageCount: 45, revenue: 67500 },
        { id: 'sample-coupon-2', code: 'FLAT1000', usageCount: 12, revenue: 12000 }
      ],
      topPerformingPromotions: [
        { id: 'sample-promotion-1', title: 'Summer Sale', revenue: 89000, orderCount: 34 },
        { id: 'sample-promotion-2', title: 'New Customer Discount', revenue: 36000, orderCount: 24 }
      ],
      bannerClickStats: [
        { id: 'sample-banner-1', title: 'Summer Collection', clicks: 156, impressions: 2340, ctr: 6.67 },
        { id: 'sample-banner-2', title: 'Free Shipping', clicks: 89, impressions: 1567, ctr: 5.68 }
      ]
    }
  }
}

export const marketingService = new MarketingService('womanza-jewelry-store')
