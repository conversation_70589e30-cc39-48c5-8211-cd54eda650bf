import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { useStoreManagement } from '../store/store-management'
import { rolesPermissionsService, type Permission, type Role } from '../lib/roles-permissions-service'
import toast from 'react-hot-toast'
import {
  Shield,
  Users,
  Settings,
  Eye,
  Edit,
  Trash2,
  Plus,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info
} from 'lucide-react'



export function RolesPermissionsPage() {
  const { user } = useFirebaseAuthStore()
  const { currentStore } = useStoreManagement()
  const [roles, setRoles] = useState<Role[]>([])
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [loading, setLoading] = useState(true)

  // Check if user can manage roles (only super_admin)
  const canManageRoles = user?.role === 'super_admin'

  // Get store ID for current user
  const storeId = currentStore?.id || user?.storeId || user?.primaryStoreId || import.meta.env.VITE_STORE_ID || 'womanza'

  // Initialize permissions and subscribe to roles
  useEffect(() => {
    if (!canManageRoles) {
      setLoading(false)
      return
    }

    // Get system permissions
    const systemPermissions = rolesPermissionsService.getPermissions()
    setPermissions(systemPermissions)

    // Subscribe to real-time roles data
    const unsubscribe = rolesPermissionsService.subscribeToRoles(
      storeId,
      (rolesData) => {
        setRoles(rolesData)
        setLoading(false)

        // Update selected role if it exists
        if (selectedRole) {
          const updatedRole = rolesData.find(r => r.id === selectedRole.id)
          if (updatedRole) {
            setSelectedRole(updatedRole)
          }
        }
      }
    )

    return () => {
      unsubscribe()
    }
  }, [canManageRoles, storeId, selectedRole?.id])

  // Get unique permission categories
  const categories = ['all', ...Array.from(new Set(permissions.map(p => p.category)))]

  // Filter permissions based on search and category
  const filteredPermissions = permissions.filter(permission => {
    const matchesSearch = permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         permission.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || permission.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  // Group permissions by category
  const groupedPermissions = filteredPermissions.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = []
    }
    acc[permission.category].push(permission)
    return acc
  }, {} as Record<string, Permission[]>)

  const getRoleIcon = (roleName: string) => {
    switch (roleName) {
      case 'super_admin': return <Shield className="h-5 w-5 text-purple-600" />
      case 'admin': return <Settings className="h-5 w-5 text-blue-600" />
      case 'editor': return <Edit className="h-5 w-5 text-green-600" />
      case 'viewer': return <Eye className="h-5 w-5 text-gray-600" />
      default: return <Users className="h-5 w-5 text-gray-600" />
    }
  }

  const getRoleBadgeColor = (roleName: string) => {
    switch (roleName) {
      case 'super_admin': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'admin': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'editor': return 'bg-green-100 text-green-800 border-green-200'
      case 'viewer': return 'bg-gray-100 text-gray-800 border-gray-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPermissionIcon = (category: string) => {
    switch (category) {
      case 'Dashboard': return <Info className="h-4 w-4" />
      case 'Products': return <Plus className="h-4 w-4" />
      case 'Categories': return <Filter className="h-4 w-4" />
      case 'Orders': return <CheckCircle className="h-4 w-4" />
      case 'Customers': return <Users className="h-4 w-4" />
      case 'Marketing': return <AlertTriangle className="h-4 w-4" />
      case 'Analytics': return <Eye className="h-4 w-4" />
      case 'Settings': return <Settings className="h-4 w-4" />
      case 'Files': return <Plus className="h-4 w-4" />
      default: return <Shield className="h-4 w-4" />
    }
  }

  if (!canManageRoles) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <Shield className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Restricted</h2>
          <p className="text-gray-600">
            You don't have permission to view roles and permissions.
            Only Super Admins can access this section.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Roles & Permissions</h1>
          <p className="text-gray-600 mt-1">
            Manage user roles and their permissions across your store
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Roles List */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                User Roles
              </CardTitle>
              <CardDescription>
                System-defined roles with specific permissions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {roles.map((role) => (
                <div
                  key={role.id}
                  className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                    selectedRole?.id === role.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedRole(role)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getRoleIcon(role.name)}
                      <span className="font-medium">{role.displayName}</span>
                    </div>
                    <Badge className={getRoleBadgeColor(role.name)}>
                      {role.userCount} users
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">{role.description}</p>
                  <div className="mt-2">
                    <span className="text-xs text-gray-500">
                      {role.permissions.length} permissions
                    </span>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Permissions Details */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    {selectedRole ? `${selectedRole.displayName} Permissions` : 'All Permissions'}
                  </CardTitle>
                  <CardDescription>
                    {selectedRole 
                      ? `Permissions assigned to ${selectedRole.displayName} role`
                      : 'Complete list of available permissions'
                    }
                  </CardDescription>
                </div>
              </div>
              
              {/* Search and Filter */}
              <div className="flex gap-4 mt-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search permissions..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category}
                    </option>
                  ))}
                </select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {Object.entries(groupedPermissions).map(([category, categoryPermissions]) => (
                  <div key={category}>
                    <h3 className="flex items-center gap-2 text-lg font-semibold text-gray-900 mb-3">
                      {getPermissionIcon(category)}
                      {category}
                    </h3>
                    <div className="grid gap-3">
                      {categoryPermissions.map((permission) => {
                        const hasPermission = selectedRole?.permissions.includes(permission.id)
                        return (
                          <div
                            key={permission.id}
                            className={`p-3 rounded-lg border ${
                              hasPermission
                                ? 'border-green-200 bg-green-50'
                                : 'border-gray-200 bg-gray-50'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                {hasPermission ? (
                                  <CheckCircle className="h-5 w-5 text-green-600" />
                                ) : (
                                  <XCircle className="h-5 w-5 text-gray-400" />
                                )}
                                <div>
                                  <h4 className="font-medium text-gray-900">
                                    {permission.name}
                                  </h4>
                                  <p className="text-sm text-gray-600">
                                    {permission.description}
                                  </p>
                                </div>
                              </div>
                              <Badge
                                variant={hasPermission ? "default" : "secondary"}
                                className={hasPermission ? "bg-green-100 text-green-800" : ""}
                              >
                                {hasPermission ? 'Granted' : 'Not Granted'}
                              </Badge>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
