import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { useProductsStore } from '../../store/products-store'
import { useFirebaseAuthStore } from '../../store/firebase-auth'
import { debugFirebaseStructure } from '../../utils/firebase-debug'
import { 
  Database, 
  Plus, 
  RefreshCw, 
  CheckCircle, 
  AlertTriangle,
  Package,
  Upload,
  Download,
  Zap,
  Activity,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'

export function DataManagement() {
  const { user } = useFirebaseAuthStore()
  const { products, loading, error, fetchProducts, addProduct, subscribeToProducts } = useProductsStore()
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [newProduct, setNewProduct] = useState({
    name: '',
    description: '',
    price: 0,
    category: '',
    sku: ''
  })

  // Set up real-time subscription
  useEffect(() => {
    if (user && !isSubscribed) {
      console.log('🔔 Setting up real-time subscription')
      const unsubscribe = subscribeToProducts()
      setIsSubscribed(true)
      
      return () => {
        unsubscribe()
        setIsSubscribed(false)
      }
    }
  }, [user, subscribeToProducts, isSubscribed])

  const handleAddProduct = async () => {
    if (!newProduct.name || !newProduct.price) {
      alert('Please fill in required fields (name and price)')
      return
    }

    try {
      await addProduct({
        name: newProduct.name,
        description: newProduct.description,
        price: newProduct.price,
        compareAtPrice: undefined,
        cost: undefined,
        sku: newProduct.sku || `SKU-${Date.now()}`,
        barcode: undefined,
        trackQuantity: true,
        quantity: 0,
        lowStockThreshold: 10,
        category: newProduct.category || 'Uncategorized',
        tags: [],
        images: [],
        weight: undefined,
        dimensions: undefined,
        status: 'active' as const,
        vendor: undefined,
        type: undefined,
        seoTitle: undefined,
        seoDescription: undefined,
        storeId: 'womanza'
      })

      // Reset form
      setNewProduct({
        name: '',
        description: '',
        price: 0,
        category: '',
        sku: ''
      })

      alert('Product added successfully!')
    } catch (error) {
      console.error('Error adding product:', error)
      alert('Failed to add product')
    }
  }

  return (
    <div className="space-y-6">
      {/* Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5 text-blue-600" />
            Data Management Status
          </CardTitle>
          <CardDescription>
            Real-time data synchronization and management
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <Activity className={`h-5 w-5 ${isSubscribed ? 'text-green-600' : 'text-gray-400'}`} />
              <div>
                <p className="font-medium text-gray-900">Real-time Sync</p>
                <p className="text-sm text-gray-600">
                  {isSubscribed ? 'Active' : 'Inactive'}
                </p>
              </div>
              <Badge variant={isSubscribed ? 'default' : 'secondary'}>
                {isSubscribed ? 'ON' : 'OFF'}
              </Badge>
            </div>

            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <Package className="h-5 w-5 text-blue-600" />
              <div>
                <p className="font-medium text-gray-900">Products</p>
                <p className="text-sm text-gray-600">{products.length} items</p>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              {error ? (
                <AlertTriangle className="h-5 w-5 text-red-600" />
              ) : (
                <CheckCircle className="h-5 w-5 text-green-600" />
              )}
              <div>
                <p className="font-medium text-gray-900">Status</p>
                <p className="text-sm text-gray-600">
                  {error ? 'Error' : 'Connected'}
                </p>
              </div>
            </div>
          </div>

          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-700">
                <strong>Error:</strong> {error}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-orange-600" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button 
              onClick={fetchProducts}
              disabled={loading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh Data
            </Button>
            
            <Button 
              variant="outline"
              onClick={() => window.open('https://console.firebase.google.com/project/womanza-store/firestore/data', '_blank')}
              className="flex items-center gap-2"
            >
              <Eye className="h-4 w-4" />
              View Firebase
            </Button>
            
            <Button
              variant="outline"
              onClick={() => console.log('Products:', products)}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Debug Log
            </Button>

            <Button
              variant="outline"
              onClick={() => debugFirebaseStructure('womanza')}
              className="flex items-center gap-2"
            >
              <Database className="h-4 w-4" />
              Debug Firebase
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Add New Product */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5 text-green-600" />
            Add New Product
          </CardTitle>
          <CardDescription>
            Quickly add a new product to test real-time functionality
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Product Name *
              </label>
              <Input
                value={newProduct.name}
                onChange={(e) => setNewProduct(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter product name"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Price *
              </label>
              <Input
                type="number"
                value={newProduct.price}
                onChange={(e) => setNewProduct(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                placeholder="0.00"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <Input
                value={newProduct.category}
                onChange={(e) => setNewProduct(prev => ({ ...prev, category: e.target.value }))}
                placeholder="e.g., Jewelry, Clothing"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                SKU
              </label>
              <Input
                value={newProduct.sku}
                onChange={(e) => setNewProduct(prev => ({ ...prev, sku: e.target.value }))}
                placeholder="Auto-generated if empty"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <Textarea
              value={newProduct.description}
              onChange={(e) => setNewProduct(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Product description..."
              rows={3}
            />
          </div>
          
          <Button 
            onClick={handleAddProduct}
            className="w-full flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Product
          </Button>
        </CardContent>
      </Card>

      {/* Products List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5 text-purple-600" />
            Current Products ({products.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto text-gray-400 mb-2" />
              <p className="text-gray-600">Loading products...</p>
            </div>
          ) : products.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-8 w-8 mx-auto text-gray-400 mb-2" />
              <p className="text-gray-600">No products found</p>
              <p className="text-sm text-gray-500">Add a product above to get started</p>
            </div>
          ) : (
            <div className="space-y-3">
              {products.slice(0, 5).map((product) => (
                <div key={product.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{product.name}</p>
                    <p className="text-sm text-gray-600">
                      {product.category} • ${product.price} • SKU: {product.sku}
                    </p>
                  </div>
                  <Badge variant="secondary">
                    {product.status}
                  </Badge>
                </div>
              ))}
              {products.length > 5 && (
                <p className="text-sm text-gray-500 text-center">
                  ... and {products.length - 5} more products
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
