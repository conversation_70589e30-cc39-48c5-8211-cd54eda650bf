import { cn } from "../../lib/utils"
import { Loader2, Package, ShoppingCart, Users, BarChart3 } from 'lucide-react'

interface LoadingSpinnerProps {
  size?: "sm" | "default" | "lg"
  className?: string
  variant?: "spinner" | "dots" | "pulse"
}

export function LoadingSpinner({ size = "default", className, variant = "spinner" }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    default: "h-6 w-6",
    lg: "h-8 w-8"
  }

  if (variant === "dots") {
    return (
      <div className="flex items-center justify-center">
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
        </div>
      </div>
    )
  }

  if (variant === "pulse") {
    return (
      <div className="flex items-center justify-center">
        <div className={cn("bg-blue-600 rounded-full animate-pulse", sizeClasses[size], className)}></div>
      </div>
    )
  }

  return (
    <div className="flex items-center justify-center">
      <Loader2 className={cn("animate-spin text-blue-600", sizeClasses[size], className)} />
    </div>
  )
}

interface PageLoadingProps {
  message?: string
}

export function PageLoading({ message = "Loading..." }: PageLoadingProps) {
  const icons = [Package, ShoppingCart, Users, BarChart3]
  const IconComponent = icons[Math.floor(Math.random() * icons.length)]

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 animate-pulse">
          <IconComponent className="h-8 w-8 text-white" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading Womanza Admin</h2>
        <p className="text-gray-500 mb-6">{message}</p>
        <LoadingSpinner variant="dots" />
      </div>
    </div>
  )
}

export function DashboardSkeleton() {
  return (
    <div className="space-y-6 p-6">
      {/* Header Skeleton */}
      <div className="bg-gradient-to-r from-gray-200 to-gray-300 rounded-2xl p-8 animate-pulse">
        <div className="flex items-center justify-between">
          <div className="space-y-3">
            <div className="h-8 bg-white/30 rounded w-64"></div>
            <div className="h-4 bg-white/20 rounded w-48"></div>
          </div>
          <div className="flex gap-3">
            <div className="h-10 bg-white/30 rounded w-32"></div>
            <div className="h-10 bg-white/30 rounded w-32"></div>
          </div>
        </div>
      </div>

      {/* Stats Cards Skeleton */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="bg-white rounded-xl p-6 border border-gray-200 animate-pulse">
            <div className="flex items-center justify-between mb-4">
              <div className="h-4 bg-gray-200 rounded w-20"></div>
              <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
            </div>
            <div className="space-y-2">
              <div className="h-8 bg-gray-200 rounded w-24"></div>
              <div className="h-3 bg-gray-200 rounded w-32"></div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Skeleton */}
      <div className="grid gap-6 md:grid-cols-2">
        <div className="bg-white rounded-xl p-6 border border-gray-200 animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-32 mb-4"></div>
          <div className="h-64 bg-gray-100 rounded"></div>
        </div>
        <div className="bg-white rounded-xl p-6 border border-gray-200 animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-32 mb-4"></div>
          <div className="h-64 bg-gray-100 rounded"></div>
        </div>
      </div>
    </div>
  )
}

export function TableSkeleton({ rows = 5 }: { rows?: number }) {
  return (
    <div className="space-y-4">
      {/* Table Header */}
      <div className="grid grid-cols-5 gap-4 p-4 bg-gray-50 rounded-lg">
        {[1, 2, 3, 4, 5].map((i) => (
          <div key={i} className="h-4 bg-gray-200 rounded animate-pulse"></div>
        ))}
      </div>

      {/* Table Rows */}
      {Array.from({ length: rows }).map((_, i) => (
        <div key={i} className="grid grid-cols-5 gap-4 p-4 bg-white border border-gray-200 rounded-lg animate-pulse">
          {[1, 2, 3, 4, 5].map((j) => (
            <div key={j} className="h-4 bg-gray-200 rounded"></div>
          ))}
        </div>
      ))}
    </div>
  )
}
