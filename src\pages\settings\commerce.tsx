import { useState, useEffect } from 'react'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Switch } from '../../components/ui/switch'
import { Button } from '../../components/ui/button'
import { toast } from 'react-hot-toast'
import { Save, Loader2 } from 'lucide-react'
import { useRealtimeSettings } from '../../hooks/use-realtime-settings'
import { useStoreManagement } from '../../store/store-management'
import { useFirebaseAuthStore } from '../../store/firebase-auth'

export function CommerceSettingsPage() {
  const { hasPermission } = useFirebaseAuthStore()
  const { currentStore } = useStoreManagement()
  const {
    settings,
    loading,
    updatePayment,
    updateShipping,
    updateTax,
    initializeSettings
  } = useRealtimeSettings(currentStore?.id || 'default')

  const [paymentData, setPaymentData] = useState({})
  const [shippingData, setShippingData] = useState({})
  const [taxData, setTaxData] = useState({})
  const [isSaving, setIsSaving] = useState(false)

  const canEdit = hasPermission(['super_admin', 'admin'])

  // Initialize form data when settings load
  useEffect(() => {
    if (settings?.payment) {
      setPaymentData(settings.payment)
    }
    if (settings?.shipping) {
      setShippingData(settings.shipping)
    }
    if (settings?.tax) {
      setTaxData(settings.tax)
    }
  }, [settings?.payment, settings?.shipping, settings?.tax])

  // Initialize settings if they don't exist
  useEffect(() => {
    if (!loading && (!settings?.payment || !settings?.shipping || !settings?.tax) && currentStore?.id) {
      console.log('🔧 Initializing commerce settings for store:', currentStore.id)
      initializeSettings()
    }
  }, [loading, settings?.payment, settings?.shipping, settings?.tax, currentStore?.id, initializeSettings])

  // Handle field changes
  const handlePaymentChange = (field: string, value: any) => {
    setPaymentData(prev => ({ ...prev, [field]: value }))
  }

  const handleShippingChange = (field: string, value: any) => {
    setShippingData(prev => ({ ...prev, [field]: value }))
  }

  const handleTaxChange = (field: string, value: any) => {
    setTaxData(prev => ({ ...prev, [field]: value }))
  }

  // Handle save
  const handleSave = async () => {
    if (!canEdit) return

    setIsSaving(true)
    try {
      await Promise.all([
        updatePayment(paymentData),
        updateShipping(shippingData),
        updateTax(taxData)
      ])
      
      toast.success('Commerce settings saved successfully!')
    } catch (error) {
      console.error('Error saving commerce settings:', error)
      toast.error('Failed to save settings. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Commerce Settings</h1>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            Configure payment methods, shipping options, and tax settings
          </p>
        </div>
        <Button
          onClick={handleSave}
          disabled={isSaving || !canEdit}
          className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
        >
          {isSaving ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          {isSaving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>

      {/* Payment Methods */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Payment Methods</h3>
        
        <div className="space-y-6">
          {/* Stripe */}
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h4 className="text-md font-medium text-gray-900 dark:text-gray-100">Stripe</h4>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Accept credit cards, debit cards, and digital wallets
                </p>
              </div>
              <Switch
                checked={paymentData.stripeEnabled || false}
                onCheckedChange={(checked) => handlePaymentChange('stripeEnabled', checked)}
                disabled={!canEdit}
              />
            </div>

            {paymentData.stripeEnabled && (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="stripePublishableKey">Publishable Key</Label>
                  <Input
                    id="stripePublishableKey"
                    value={paymentData.stripePublishableKey || ''}
                    onChange={(e) => handlePaymentChange('stripePublishableKey', e.target.value)}
                    disabled={!canEdit}
                    placeholder="pk_test_..."
                  />
                </div>
                <div>
                  <Label htmlFor="stripeSecretKey">Secret Key</Label>
                  <Input
                    id="stripeSecretKey"
                    type="password"
                    value={paymentData.stripeSecretKey || ''}
                    onChange={(e) => handlePaymentChange('stripeSecretKey', e.target.value)}
                    disabled={!canEdit}
                    placeholder="sk_test_..."
                  />
                </div>
              </div>
            )}
          </div>

          {/* PayPal */}
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h4 className="text-md font-medium text-gray-900 dark:text-gray-100">PayPal</h4>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Accept PayPal payments and digital wallets
                </p>
              </div>
              <Switch
                checked={paymentData.paypalEnabled || false}
                onCheckedChange={(checked) => handlePaymentChange('paypalEnabled', checked)}
                disabled={!canEdit}
              />
            </div>

            {paymentData.paypalEnabled && (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="paypalClientId">Client ID</Label>
                  <Input
                    id="paypalClientId"
                    value={paymentData.paypalClientId || ''}
                    onChange={(e) => handlePaymentChange('paypalClientId', e.target.value)}
                    disabled={!canEdit}
                    placeholder="AXxxx..."
                  />
                </div>
                <div>
                  <Label htmlFor="paypalClientSecret">Client Secret</Label>
                  <Input
                    id="paypalClientSecret"
                    type="password"
                    value={paymentData.paypalClientSecret || ''}
                    onChange={(e) => handlePaymentChange('paypalClientSecret', e.target.value)}
                    disabled={!canEdit}
                    placeholder="EXxxx..."
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Shipping Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Shipping Settings</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="enableShipping" className="text-sm font-medium">Enable Shipping</Label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Allow customers to select shipping options
              </p>
            </div>
            <Switch
              id="enableShipping"
              checked={shippingData.enableShipping || false}
              onCheckedChange={(checked) => handleShippingChange('enableShipping', checked)}
              disabled={!canEdit}
            />
          </div>

          {shippingData.enableShipping && (
            <div className="space-y-6 border-l-2 border-gray-200 dark:border-gray-700 pl-4 ml-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="freeShippingThreshold">Free Shipping Threshold</Label>
                  <Input
                    id="freeShippingThreshold"
                    type="number"
                    min="0"
                    step="0.01"
                    value={shippingData.freeShippingThreshold || '100'}
                    onChange={(e) => handleShippingChange('freeShippingThreshold', parseFloat(e.target.value))}
                    disabled={!canEdit}
                    placeholder="100.00"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Minimum order value for free shipping
                  </p>
                </div>

                <div>
                  <Label htmlFor="defaultShippingRate">Default Shipping Rate</Label>
                  <Input
                    id="defaultShippingRate"
                    type="number"
                    min="0"
                    step="0.01"
                    value={shippingData.defaultShippingRate || '10'}
                    onChange={(e) => handleShippingChange('defaultShippingRate', parseFloat(e.target.value))}
                    disabled={!canEdit}
                    placeholder="10.00"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Standard shipping cost
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Tax Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Tax Settings</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="enableTax" className="text-sm font-medium">Enable Tax Calculation</Label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Apply tax to orders and products
              </p>
            </div>
            <Switch
              id="enableTax"
              checked={taxData.enableTax || false}
              onCheckedChange={(checked) => handleTaxChange('enableTax', checked)}
              disabled={!canEdit}
            />
          </div>

          {taxData.enableTax && (
            <div className="space-y-6 border-l-2 border-gray-200 dark:border-gray-700 pl-4 ml-6">
              <div>
                <Label htmlFor="defaultTaxRate">Default Tax Rate (%)</Label>
                <Input
                  id="defaultTaxRate"
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={taxData.defaultTaxRate || '0'}
                  onChange={(e) => handleTaxChange('defaultTaxRate', parseFloat(e.target.value))}
                  disabled={!canEdit}
                  placeholder="8.25"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Default tax rate applied to products (e.g., 8.25 for 8.25%)
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="taxIncluded" className="text-sm font-medium">Tax Included in Prices</Label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Product prices already include tax
                  </p>
                </div>
                <Switch
                  id="taxIncluded"
                  checked={taxData.taxIncluded || false}
                  onCheckedChange={(checked) => handleTaxChange('taxIncluded', checked)}
                  disabled={!canEdit}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
