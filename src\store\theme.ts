import { create } from 'zustand'

export interface StoreTheme {
  id: string
  store_id: string
  primary_color: string
  secondary_color: string
  accent_color: string
  background_color: string
  text_color: string
  logo_url?: string
  font_family: string
  border_radius: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  button_style: 'rounded' | 'square' | 'pill'
  created_at: string
  updated_at: string
}

export interface ThemeState {
  currentTheme: StoreTheme | null
  isLoading: boolean
  error: string | null
  
  // Actions
  loadTheme: (storeId: string) => Promise<void>
  updateTheme: (theme: Partial<StoreTheme>) => Promise<void>
  applyTheme: (theme: StoreTheme) => void
  resetTheme: () => void
}

// Default theme
const defaultTheme: StoreTheme = {
  id: 'default',
  store_id: 'default',
  primary_color: '#2563eb',
  secondary_color: '#9333ea',
  accent_color: '#06b6d4',
  background_color: '#ffffff',
  text_color: '#111827',
  font_family: 'Inter',
  border_radius: 'md',
  button_style: 'rounded',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
}

export const useThemeStore = create<ThemeState>((set, get) => ({
  currentTheme: defaultTheme,
  isLoading: false,
  error: null,

  loadTheme: async (storeId: string) => {
    set({ isLoading: true, error: null })
    
    try {
      // For development, use localStorage to store theme
      const savedTheme = localStorage.getItem(`theme-${storeId}`)

      if (savedTheme) {
        const theme = JSON.parse(savedTheme) as StoreTheme
        get().applyTheme(theme)
        set({ currentTheme: theme, isLoading: false })
      } else {
        // Use default theme
        const theme = { ...defaultTheme, store_id: storeId }
        get().applyTheme(theme)
        set({ currentTheme: theme, isLoading: false })
        localStorage.setItem(`theme-${storeId}`, JSON.stringify(theme))
      }
    } catch (error) {
      console.error('Error loading theme:', error)
      set({ 
        error: error instanceof Error ? error.message : 'Failed to load theme',
        isLoading: false 
      })
    }
  },

  updateTheme: async (themeUpdate: Partial<StoreTheme>) => {
    const { currentTheme } = get()
    if (!currentTheme) return

    set({ isLoading: true, error: null })

    try {
      const updatedTheme = { ...currentTheme, ...themeUpdate, updated_at: new Date().toISOString() }

      // Save to localStorage for development
      localStorage.setItem(`theme-${updatedTheme.store_id}`, JSON.stringify(updatedTheme))

      get().applyTheme(updatedTheme)
      set({ currentTheme: updatedTheme, isLoading: false })
    } catch (error) {
      console.error('Error updating theme:', error)
      set({ 
        error: error instanceof Error ? error.message : 'Failed to update theme',
        isLoading: false 
      })
    }
  },

  applyTheme: (theme: StoreTheme) => {
    // Apply CSS custom properties to the document root
    const root = document.documentElement
    
    // Convert hex colors to RGB for better CSS variable usage
    const hexToRgb = (hex: string) => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : null
    }

    const primaryRgb = hexToRgb(theme.primary_color)
    const secondaryRgb = hexToRgb(theme.secondary_color)
    const accentRgb = hexToRgb(theme.accent_color)

    if (primaryRgb) {
      root.style.setProperty('--primary-50', `rgb(${Math.min(255, primaryRgb.r + 40)} ${Math.min(255, primaryRgb.g + 40)} ${Math.min(255, primaryRgb.b + 40)})`)
      root.style.setProperty('--primary-100', `rgb(${Math.min(255, primaryRgb.r + 30)} ${Math.min(255, primaryRgb.g + 30)} ${Math.min(255, primaryRgb.b + 30)})`)
      root.style.setProperty('--primary-500', `rgb(${primaryRgb.r} ${primaryRgb.g} ${primaryRgb.b})`)
      root.style.setProperty('--primary-600', `rgb(${Math.max(0, primaryRgb.r - 20)} ${Math.max(0, primaryRgb.g - 20)} ${Math.max(0, primaryRgb.b - 20)})`)
      root.style.setProperty('--primary-700', `rgb(${Math.max(0, primaryRgb.r - 40)} ${Math.max(0, primaryRgb.g - 40)} ${Math.max(0, primaryRgb.b - 40)})`)
    }

    if (secondaryRgb) {
      root.style.setProperty('--secondary-500', `rgb(${secondaryRgb.r} ${secondaryRgb.g} ${secondaryRgb.b})`)
      root.style.setProperty('--secondary-600', `rgb(${Math.max(0, secondaryRgb.r - 20)} ${Math.max(0, secondaryRgb.g - 20)} ${Math.max(0, secondaryRgb.b - 20)})`)
    }

    if (accentRgb) {
      root.style.setProperty('--accent-500', `rgb(${accentRgb.r} ${accentRgb.g} ${accentRgb.b})`)
    }

    // Apply font family
    root.style.setProperty('--font-family', theme.font_family)
    
    // Apply border radius
    const radiusMap = {
      none: '0',
      sm: '0.125rem',
      md: '0.375rem',
      lg: '0.5rem',
      xl: '0.75rem'
    }
    root.style.setProperty('--border-radius', radiusMap[theme.border_radius])

    // Apply background and text colors
    root.style.setProperty('--background-color', theme.background_color)
    root.style.setProperty('--text-color', theme.text_color)
  },

  resetTheme: () => {
    set({ currentTheme: defaultTheme, error: null })
    get().applyTheme(defaultTheme)
  }
}))

// Hook to get theme-aware CSS classes
export const useThemeClasses = () => {
  const { currentTheme } = useThemeStore()
  
  return {
    primary: `bg-[${currentTheme?.primary_color}] text-white`,
    secondary: `bg-[${currentTheme?.secondary_color}] text-white`,
    accent: `bg-[${currentTheme?.accent_color}] text-white`,
    button: currentTheme?.button_style === 'pill' ? 'rounded-full' : 
            currentTheme?.button_style === 'square' ? 'rounded-none' : 
            'rounded-md',
  }
}
