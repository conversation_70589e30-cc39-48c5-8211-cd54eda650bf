import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  setDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  onSnapshot
} from 'firebase/firestore'
import { createUserWithEmailAndPassword } from 'firebase/auth'
import { db, auth, isFirebaseConfigured } from './firebase'
import { emailService } from './email-service'

export interface StoreRegistrationData {
  store: {
    name: string
    slug: string
    description: string
    category: string
    country: string
    currency: string
    website?: string
    businessAddress: string
    businessType: string
    expectedMonthlyOrders: string
  }
  owner: {
    name: string
    email: string
    phone: string
    password: string
  }
}

export interface PendingStoreRequest {
  id: string
  store: {
    name: string
    slug: string
    description: string
    category: string
    country: string
    currency: string
    website?: string
    businessAddress: string
    businessType: string
    expectedMonthlyOrders: string
  }
  owner: {
    name: string
    email: string
    phone: string
    uid?: string
    tempPassword?: string // Temporary storage for approval process
  }
  status: 'pending' | 'approved' | 'rejected'
  submittedAt: string
  reviewedAt?: string
  reviewedBy?: string
  rejectionReason?: string
  notes?: string
}

export class StoreRegistrationService {

  /**
   * Generate a temporary password for new users
   */
  private generateTempPassword(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
    let password = ''
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return password
  }

  /**
   * Get existing user ID by email from Firestore users collection
   */
  private async getExistingUserIdByEmail(email: string): Promise<string | null> {
    try {
      // Search in users collection for existing user with this email
      const usersQuery = query(
        collection(db, 'users'),
        where('email', '==', email)
      )

      const usersSnapshot = await getDocs(usersQuery)
      if (!usersSnapshot.empty) {
        const userDoc = usersSnapshot.docs[0]
        return userDoc.id
      }

      return null
    } catch (error) {
      console.error('Error finding existing user:', error)
      return null
    }
  }
  
  /**
   * Submit a new store registration request
   */
  async submitRegistration(data: StoreRegistrationData): Promise<void> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      // Check if store slug is already taken
      const existingStores = await getDocs(
        query(
          collection(db, 'pending_store_requests'),
          where('store.slug', '==', data.store.slug)
        )
      )

      if (!existingStores.empty) {
        throw new Error('Store URL slug is already taken. Please choose a different one.')
      }

      // Check if email is already registered
      const existingEmails = await getDocs(
        query(
          collection(db, 'pending_store_requests'),
          where('owner.email', '==', data.owner.email)
        )
      )

      if (!existingEmails.empty) {
        throw new Error('Email address is already registered. Please use a different email.')
      }

      // Create the registration request
      // Store password temporarily for approval process (encrypted in production)
      const requestData: Omit<PendingStoreRequest, 'id'> = {
        store: data.store,
        owner: {
          name: data.owner.name,
          email: data.owner.email,
          phone: data.owner.phone,
          // Store password temporarily for approval (in production, use encryption)
          tempPassword: data.owner.password
        },
        status: 'pending',
        submittedAt: new Date().toISOString()
      }

      const docRef = await addDoc(collection(db, 'pending_store_requests'), {
        ...requestData,
        submittedAt: serverTimestamp()
      })

      // Send email notification to app admin
      await emailService.sendStoreRegistrationNotification(data.store, data.owner)

      console.log('Store registration submitted successfully:', docRef.id)
      
    } catch (error: any) {
      console.error('Error submitting store registration:', error)
      throw new Error(error.message || 'Failed to submit registration')
    }
  }

  /**
   * Get all pending store requests (for app admin)
   */
  async getPendingRequests(): Promise<PendingStoreRequest[]> {
    if (!isFirebaseConfigured) return []

    try {
      const q = query(
        collection(db, 'pending_store_requests'),
        orderBy('submittedAt', 'desc')
      )
      
      const snapshot = await getDocs(q)
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        submittedAt: doc.data().submittedAt?.toDate?.()?.toISOString() || doc.data().submittedAt,
        reviewedAt: doc.data().reviewedAt?.toDate?.()?.toISOString() || doc.data().reviewedAt
      } as PendingStoreRequest))
    } catch (error) {
      console.error('Error fetching pending requests:', error)
      return []
    }
  }

  /**
   * Subscribe to pending requests in real-time
   */
  subscribeToPendingRequests(callback: (requests: PendingStoreRequest[]) => void): () => void {
    if (!isFirebaseConfigured) {
      callback([])
      return () => {}
    }

    const q = query(
      collection(db, 'pending_store_requests'),
      orderBy('submittedAt', 'desc')
    )

    return onSnapshot(q, (snapshot) => {
      const requests = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        submittedAt: doc.data().submittedAt?.toDate?.()?.toISOString() || doc.data().submittedAt,
        reviewedAt: doc.data().reviewedAt?.toDate?.()?.toISOString() || doc.data().reviewedAt
      } as PendingStoreRequest))
      callback(requests)
    })
  }

  /**
   * Approve a store registration request
   */
  async approveRequest(requestId: string, reviewedBy: string): Promise<void> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      // Get the request data
      const requestDoc = await getDoc(doc(db, 'pending_store_requests', requestId))
      if (!requestDoc.exists()) {
        throw new Error('Request not found')
      }

      const requestData = requestDoc.data() as PendingStoreRequest

      // Create Firebase Auth user for the store owner
      let firebaseUser
      let userAlreadyExists = false

      // Use the password from the original registration
      const originalPassword = requestData.owner.tempPassword
      if (!originalPassword) {
        throw new Error('Original password not found. Cannot create user account.')
      }

      console.log('🔐 Creating Firebase Auth user with original password...')

      try {
        const userCredential = await createUserWithEmailAndPassword(
          auth,
          requestData.owner.email,
          originalPassword
        )
        firebaseUser = userCredential.user
        console.log('✅ Firebase Auth user created successfully:', firebaseUser.uid)

      } catch (authError: any) {
        if (authError.code === 'auth/email-already-in-use') {
          console.log('⚠️  Email already in use, checking existing user...')
          userAlreadyExists = true

          // Check if user already exists in our Firestore
          const existingUserId = await this.getExistingUserIdByEmail(requestData.owner.email)
          if (existingUserId) {
            firebaseUser = { uid: existingUserId }
            console.log('✅ Using existing user from Firestore:', existingUserId)
          } else {
            throw new Error(`Email ${requestData.owner.email} is already registered in Firebase Auth but not found in our system. Please contact support.`)
          }

        } else if (authError.code === 'auth/weak-password') {
          throw new Error('The password is too weak. Please use a stronger password.')
        } else if (authError.code === 'auth/invalid-email') {
          throw new Error('Invalid email address format.')
        } else {
          console.error('Firebase Auth error:', authError)
          throw new Error(`Failed to create user account: ${authError.message}`)
        }
      }

      // Create the store document with hierarchical structure
      console.log('📦 Creating store document with hierarchical structure...')
      const storeData = {
        id: requestData.store.slug,
        name: requestData.store.name,
        slug: requestData.store.slug,
        description: requestData.store.description,
        category: requestData.store.businessType,
        country: requestData.store.country,
        currency: requestData.store.currency,
        website: requestData.store.website,
        businessAddress: requestData.store.businessAddress,
        businessType: requestData.store.businessType,
        expectedMonthlyOrders: requestData.store.expectedMonthlyOrders,
        ownerId: firebaseUser.uid,
        ownerEmail: requestData.owner.email,
        status: 'active',
        settings: {
          timezone: 'UTC',
          language: 'en',
          currency: requestData.store.currency,
          taxEnabled: false,
          shippingEnabled: true
        },
        stats: {
          totalUsers: 1,
          totalProducts: 0,
          totalOrders: 0,
          totalRevenue: 0
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      try {
        // Create main store document
        await setDoc(doc(db, 'stores', requestData.store.slug), {
          ...storeData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        })
        console.log('✅ Store document created successfully:', requestData.store.slug)

        // Initialize store subcollections with placeholder documents
        console.log('📁 Initializing store subcollections...')

        // Initialize categories subcollection
        await setDoc(doc(db, 'stores', requestData.store.slug, 'categories', '_init'), {
          name: 'Default Category',
          description: 'Default category for new products',
          active: true,
          createdAt: serverTimestamp()
        })

        // Initialize empty subcollections (Firestore will create them when first document is added)
        // No need to create sample data - collections will be created when real data is added

        console.log('✅ Store subcollections initialized')

      } catch (storeError) {
        console.error('❌ Error creating store structure:', storeError)
        throw new Error(`Failed to create store: ${storeError.message}`)
      }

      // Create super admin user document in both global and store-specific collections
      console.log('👤 Creating super admin user documents...')
      console.log('   User UID:', firebaseUser.uid)
      console.log('   Email:', requestData.owner.email)
      console.log('   Store ID:', requestData.store.slug)

      // Global user data (for authentication and cross-store access)
      const globalUserData = {
        uid: firebaseUser.uid,
        email: requestData.owner.email,
        name: requestData.owner.name,
        fullName: requestData.owner.name,
        phone: requestData.owner.phone,
        role: 'super_admin' as const,
        primaryStoreId: requestData.store.slug, // Primary store for this user
        storeIds: [requestData.store.slug], // All stores this user has access to
        active: true,
        createdBy: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastLoginAt: null,
        avatarUrl: null,
        profile: {
          firstName: requestData.owner.name.split(' ')[0] || requestData.owner.name,
          lastName: requestData.owner.name.split(' ').slice(1).join(' ') || '',
          phone: requestData.owner.phone,
          avatar: ''
        },
        permissions: {
          canManageUsers: true,
          canManageProducts: true,
          canManageOrders: true,
          canViewAnalytics: true,
          canManageSettings: true,
          canManageCategories: true
        }
      }

      // Store-specific user data (for store management)
      const storeUserData = {
        uid: firebaseUser.uid,
        email: requestData.owner.email,
        name: requestData.owner.name,
        fullName: requestData.owner.name,
        phone: requestData.owner.phone,
        role: 'super_admin' as const,
        storeId: requestData.store.slug,
        active: true,
        isOwner: true, // Mark as store owner
        createdBy: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastLoginAt: null,
        profile: {
          firstName: requestData.owner.name.split(' ')[0] || requestData.owner.name,
          lastName: requestData.owner.name.split(' ').slice(1).join(' ') || '',
          phone: requestData.owner.phone,
          avatar: ''
        },
        permissions: {
          canManageUsers: true,
          canManageProducts: true,
          canManageOrders: true,
          canViewAnalytics: true,
          canManageSettings: true,
          canManageCategories: true,
          canDeleteStore: true // Only super admin can delete store
        }
      }

      try {
        // Create in global users collection (for authentication)
        await setDoc(doc(db, 'users', firebaseUser.uid), {
          ...globalUserData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        })
        console.log('✅ Global user document created successfully')

        // Create in store-specific users collection (for store management)
        await setDoc(doc(db, 'stores', requestData.store.slug, 'users', firebaseUser.uid), {
          ...storeUserData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        })
        console.log('✅ Store-specific user document created successfully')

      } catch (userError) {
        console.error('❌ Error creating user documents:', userError)
        throw new Error(`Failed to create user account: ${userError.message}`)
      }

      // Update the request status and clean up
      console.log('📝 Updating request status and cleaning up...')
      try {
        // First update the status for record keeping
        await updateDoc(doc(db, 'pending_store_requests', requestId), {
          status: 'approved',
          reviewedAt: serverTimestamp(),
          reviewedBy,
          'owner.uid': firebaseUser.uid,
          processedAt: serverTimestamp()
        })
        console.log('✅ Request status updated to approved')

        // After successful processing, move to approved_requests collection for history
        const approvedRequestData = {
          ...requestData,
          status: 'approved',
          reviewedAt: new Date().toISOString(),
          reviewedBy,
          processedAt: new Date().toISOString(),
          storeCreated: requestData.store.slug,
          userCreated: firebaseUser.uid
        }

        await setDoc(doc(db, 'approved_requests', requestId), approvedRequestData)
        console.log('✅ Request moved to approved_requests collection')

        // Remove from pending_store_requests to avoid conflicts
        await deleteDoc(doc(db, 'pending_store_requests', requestId))
        console.log('✅ Pending request cleaned up successfully')

      } catch (cleanupError) {
        console.error('⚠️  Error during cleanup (but approval succeeded):', cleanupError)
        // Don't throw error here as the main approval succeeded
      }

      // Send approval email to the store owner
      const emailMessage = userAlreadyExists
        ? 'Your store has been approved! You can now login with your existing credentials.'
        : 'Your store has been approved! You can now login with the email and password you provided during registration.'

      await emailService.sendApprovalNotification(
        requestData.store,
        requestData.owner,
        null, // No temporary password needed since we use original
        emailMessage
      )

      console.log('✅ Store request approved successfully:', requestId)
      console.log('✅ Store created:', requestData.store.slug)
      console.log('✅ Super admin user created:', firebaseUser.uid)
      console.log('✅ Email notification sent to:', requestData.owner.email)

    } catch (error: any) {
      console.error('Error approving request:', error)
      throw new Error(error.message || 'Failed to approve request')
    }
  }

  /**
   * Reject a store registration request
   */
  async rejectRequest(requestId: string, reason: string, reviewedBy: string): Promise<void> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      // Get the request data
      const requestDoc = await getDoc(doc(db, 'pending_store_requests', requestId))
      if (!requestDoc.exists()) {
        throw new Error('Request not found')
      }

      const requestData = requestDoc.data() as PendingStoreRequest

      // Update the request status
      await updateDoc(doc(db, 'pending_store_requests', requestId), {
        status: 'rejected',
        reviewedAt: serverTimestamp(),
        reviewedBy,
        rejectionReason: reason
      })

      // Send rejection email to the store owner
      await emailService.sendRejectionNotification(requestData.store, requestData.owner, reason)

      console.log('Store request rejected:', requestId)
      
    } catch (error: any) {
      console.error('Error rejecting request:', error)
      throw new Error(error.message || 'Failed to reject request')
    }
  }

  /**
   * Delete a store registration request
   */
  async deleteRequest(requestId: string): Promise<void> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      await deleteDoc(doc(db, 'pending_store_requests', requestId))
      console.log('Store request deleted:', requestId)
    } catch (error: any) {
      console.error('Error deleting request:', error)
      throw new Error(error.message || 'Failed to delete request')
    }
  }




}

// Export singleton instance
export const storeRegistrationService = new StoreRegistrationService()
