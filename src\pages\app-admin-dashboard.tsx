import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Badge } from '../components/ui/badge'
import { Textarea } from '../components/ui/textarea'
import { Alert, AlertDescription } from '../components/ui/alert'

import { storeRegistrationService, type PendingStoreRequest } from '../lib/store-registration-service'
import { appAdminService } from '../lib/app-admin-service'
import { 
  Store, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Trash2,
  LogOut,
  Shield
} from 'lucide-react'

export function AppAdminDashboard() {
  const navigate = useNavigate()
  const [requests, setRequests] = useState<PendingStoreRequest[]>([])
  const [stores, setStores] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [storesLoading, setStoresLoading] = useState(true)
  const [selectedRequest, setSelectedRequest] = useState<PendingStoreRequest | null>(null)
  const [rejectionReason, setRejectionReason] = useState('')
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('requests')

  useEffect(() => {
    loadRequests()
    loadStores()

    // Subscribe to real-time updates for requests
    const unsubscribe = storeRegistrationService.subscribeToPendingRequests((newRequests) => {
      setRequests(newRequests)
      setLoading(false)
    })

    return unsubscribe
  }, [])

  const loadRequests = async () => {
    try {
      const pendingRequests = await storeRegistrationService.getPendingRequests()
      setRequests(pendingRequests)
    } catch (error) {
      console.error('Error loading requests:', error)
      toast.error('Failed to load store requests')
    } finally {
      setLoading(false)
    }
  }

  const loadStores = async () => {
    try {
      const allStores = await appAdminService.getAllStores()
      setStores(allStores)
    } catch (error) {
      console.error('Error loading stores:', error)
      toast.error('Failed to load stores')
    } finally {
      setStoresLoading(false)
    }
  }

  const handleApprove = async (requestId: string) => {
    setActionLoading(requestId)
    try {
      console.log('🚀 Starting approval process for request:', requestId)
      await storeRegistrationService.approveRequest(requestId, '<EMAIL>')

      toast.success('🎉 Store request approved successfully! Store and super admin account created.', {
        duration: 6000,
        style: {
          background: '#10b981',
          color: 'white',
        },
      })

      console.log('✅ Approval process completed successfully')

      // Update local state immediately for better UX
      setRequests(prev => prev.map(req =>
        req.id === requestId
          ? { ...req, status: 'approved', reviewedAt: new Date().toISOString() }
          : req
      ))

      setSelectedRequest(null)

      // Refresh data from server
      await Promise.all([
        loadRequests(), // Refresh the requests list
        loadStores()    // Refresh the stores list
      ])

      // Switch to All Stores tab to show the newly created store
      setTimeout(() => setActiveTab('stores'), 1000)

    } catch (error: any) {
      console.error('❌ Error approving request:', error)

      let errorMessage = 'Failed to approve request'
      if (error.message.includes('email-already-in-use')) {
        errorMessage = 'Email is already registered. Please contact support to resolve this issue.'
      } else if (error.message.includes('weak-password')) {
        errorMessage = 'The password is too weak. Please ask the user to register with a stronger password.'
      } else if (error.message.includes('invalid-email')) {
        errorMessage = 'Invalid email address format.'
      } else if (error.message.includes('Original password not found')) {
        errorMessage = 'Registration data is incomplete. Please ask the user to register again.'
      } else if (error.message) {
        errorMessage = error.message
      }

      toast.error(`❌ Approval Failed: ${errorMessage}`, {
        duration: 8000,
        style: {
          background: '#ef4444',
          color: 'white',
        },
      })
    } finally {
      setActionLoading(null)
    }
  }

  const handleReject = async (requestId: string) => {
    if (!rejectionReason.trim()) {
      toast.error('Please provide a reason for rejection')
      return
    }

    setActionLoading(requestId)
    try {
      console.log('🚫 Starting rejection process for request:', requestId)
      await storeRegistrationService.rejectRequest(requestId, rejectionReason, '<EMAIL>')

      toast.success('📧 Store request rejected and notification sent to applicant.', {
        duration: 5000,
        style: {
          background: '#f59e0b',
          color: 'white',
        },
      })

      console.log('✅ Rejection process completed successfully')
      setSelectedRequest(null)
      setRejectionReason('')
      await loadRequests() // Refresh the requests list
      await loadStores() // Refresh the stores list

    } catch (error: any) {
      console.error('❌ Error rejecting request:', error)
      toast.error(`❌ Rejection Failed: ${error.message || 'Failed to reject request'}`, {
        duration: 6000,
        style: {
          background: '#ef4444',
          color: 'white',
        },
      })
    } finally {
      setActionLoading(null)
    }
  }

  const handleDelete = async (requestId: string) => {
    if (!confirm('Are you sure you want to delete this request? This action cannot be undone.')) {
      return
    }

    setActionLoading(requestId)
    try {
      await storeRegistrationService.deleteRequest(requestId)
      toast.success('Request deleted successfully')
    } catch (error: any) {
      console.error('Error deleting request:', error)
      toast.error(error.message || 'Failed to delete request')
    } finally {
      setActionLoading(null)
    }
  }

  const handleLogout = () => {
    // Clear any app admin session data
    localStorage.removeItem('app_admin_session')
    navigate('/login')
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600"><Clock className="h-3 w-3 mr-1" />Pending</Badge>
      case 'approved':
        return <Badge variant="outline" className="text-green-600 border-green-600"><CheckCircle className="h-3 w-3 mr-1" />Approved</Badge>
      case 'rejected':
        return <Badge variant="outline" className="text-red-600 border-red-600"><XCircle className="h-3 w-3 mr-1" />Rejected</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const pendingCount = requests.filter(r => r.status === 'pending').length
  const approvedCount = requests.filter(r => r.status === 'approved').length
  const rejectedCount = requests.filter(r => r.status === 'rejected').length

  return (
    <div className="min-h-screen bg-primary-50 dark:bg-primary-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-900 border-b border-primary-200 dark:border-primary-700 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-3">
              <Shield className="h-8 w-8 text-accent-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  Womanza App Admin
                </h1>
                <p className="text-sm text-gray-500">Store Registration Management</p>
              </div>
            </div>
            <Button onClick={handleLogout} variant="outline" size="sm">
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Requests</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{requests.length}</p>
                </div>
                <Store className="h-8 w-8 text-accent-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Pending</p>
                  <p className="text-2xl font-bold text-yellow-600">{pendingCount}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Approved</p>
                  <p className="text-2xl font-bold text-green-600">{approvedCount}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Rejected</p>
                  <p className="text-2xl font-bold text-red-600">{rejectedCount}</p>
                </div>
                <XCircle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Navigation Buttons */}
        <div className="flex gap-4 mb-6">
          <button
            onClick={() => setActiveTab('requests')}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === 'requests'
                ? 'bg-accent-600 text-white'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
            }`}
          >
            <Clock className="h-4 w-4" />
            Store Requests ({requests.length})
          </button>
          <button
            onClick={() => setActiveTab('stores')}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === 'stores'
                ? 'bg-accent-600 text-white'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
            }`}
          >
            <Store className="h-4 w-4" />
            All Stores ({stores.length})
          </button>
        </div>

        {/* Store Requests Tab */}
        {activeTab === 'requests' && (
          <Card>
            <CardHeader>
              <CardTitle>Store Registration Requests</CardTitle>
            <CardDescription>
              Review and manage store registration applications
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-600 mx-auto"></div>
                <p className="mt-2 text-gray-500">Loading requests...</p>
              </div>
            ) : requests.length === 0 ? (
              <div className="text-center py-8">
                <Store className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No store registration requests found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {requests.map((request) => (
                  <div key={request.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            {request.store.name}
                          </h3>
                          {getStatusBadge(request.status)}
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4" />
                            <span>{request.owner.name}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Mail className="h-4 w-4" />
                            <span>{request.owner.email}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Phone className="h-4 w-4" />
                            <span>{request.owner.phone}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4" />
                            <span>{new Date(request.submittedAt).toLocaleDateString()}</span>
                          </div>
                        </div>

                        <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                          {request.store.description}
                        </p>

                        {request.rejectionReason && (
                          <Alert className="mt-3">
                            <XCircle className="h-4 w-4" />
                            <AlertDescription>
                              <strong>Rejection Reason:</strong> {request.rejectionReason}
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>

                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          onClick={() => setSelectedRequest(request)}
                          variant="outline"
                          size="sm"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>

                        {request.status === 'pending' && (
                          <>
                            <Button
                              onClick={() => handleApprove(request.id)}
                              disabled={actionLoading === request.id}
                              size="sm"
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Approve
                            </Button>
                            <Button
                              onClick={() => {
                                setSelectedRequest(request)
                                setRejectionReason('')
                              }}
                              disabled={actionLoading === request.id}
                              variant="destructive"
                              size="sm"
                            >
                              <XCircle className="h-4 w-4 mr-1" />
                              Reject
                            </Button>
                          </>
                        )}

                        <Button
                          onClick={() => handleDelete(request.id)}
                          disabled={actionLoading === request.id}
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>

            {/* Request Details Modal */}
            {selectedRequest && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white dark:bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                    Store Registration Details
                  </h2>
                  <Button
                    onClick={() => setSelectedRequest(null)}
                    variant="outline"
                    size="sm"
                  >
                    Close
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Store Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Store className="h-5 w-5" />
                        Store Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Store Name</label>
                        <p className="text-gray-900 dark:text-white">{selectedRequest.store.name}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Store Slug</label>
                        <p className="text-gray-900 dark:text-white">{selectedRequest.store.slug}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Business Type</label>
                        <p className="text-gray-900 dark:text-white">{selectedRequest.store.businessType}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Country</label>
                        <p className="text-gray-900 dark:text-white">{selectedRequest.store.country}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Currency</label>
                        <p className="text-gray-900 dark:text-white">{selectedRequest.store.currency}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Expected Monthly Orders</label>
                        <p className="text-gray-900 dark:text-white">{selectedRequest.store.expectedMonthlyOrders}</p>
                      </div>
                      {selectedRequest.store.website && (
                        <div>
                          <label className="text-sm font-medium text-gray-500">Website</label>
                          <p className="text-gray-900 dark:text-white">
                            <a href={selectedRequest.store.website} target="_blank" rel="noopener noreferrer" className="text-accent-600 hover:underline">
                              {selectedRequest.store.website}
                            </a>
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Owner Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <User className="h-5 w-5" />
                        Owner Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Full Name</label>
                        <p className="text-gray-900 dark:text-white">{selectedRequest.owner.name}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Email</label>
                        <p className="text-gray-900 dark:text-white">{selectedRequest.owner.email}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Phone</label>
                        <p className="text-gray-900 dark:text-white">{selectedRequest.owner.phone}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Submitted At</label>
                        <p className="text-gray-900 dark:text-white">
                          {new Date(selectedRequest.submittedAt).toLocaleString()}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Status</label>
                        <div className="mt-1">
                          {getStatusBadge(selectedRequest.status)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Business Details */}
                <Card className="mt-6">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="h-5 w-5" />
                      Business Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Description</label>
                      <p className="text-gray-900 dark:text-white">{selectedRequest.store.description}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Business Address</label>
                      <p className="text-gray-900 dark:text-white">{selectedRequest.store.businessAddress}</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Actions */}
                {selectedRequest.status === 'pending' && (
                  <div className="mt-6 flex gap-4">
                    <div className="flex-1">
                      <Button
                        onClick={() => handleApprove(selectedRequest.id)}
                        disabled={actionLoading === selectedRequest.id}
                        className="w-full bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Approve Store
                      </Button>
                    </div>
                    <div className="flex-1">
                      <div className="space-y-2">
                        <Textarea
                          placeholder="Enter reason for rejection..."
                          value={rejectionReason}
                          onChange={(e) => setRejectionReason(e.target.value)}
                          rows={2}
                        />
                        <Button
                          onClick={() => handleReject(selectedRequest.id)}
                          disabled={actionLoading === selectedRequest.id || !rejectionReason.trim()}
                          variant="destructive"
                          className="w-full"
                        >
                          <XCircle className="h-4 w-4 mr-2" />
                          Reject Store
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
            )}
          </Card>
        )}

        {/* All Stores Tab */}
        {activeTab === 'stores' && (
          <Card>
            <CardHeader>
              <CardTitle>All Active Stores</CardTitle>
              <CardDescription>
                Manage all stores in the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              {storesLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-600 mx-auto mb-4"></div>
                    <p className="text-gray-500">Loading stores...</p>
                  </div>
                </div>
              ) : stores.length === 0 ? (
                <div className="text-center py-8">
                  <Store className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No stores found</p>
                </div>
              ) : (
                <div className="grid gap-4">
                  {stores.map((store) => (
                    <Card key={store.id} className="border-l-4 border-l-green-500">
                      <CardContent className="pt-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <Store className="h-5 w-5 text-accent-600" />
                              <h3 className="font-semibold text-lg">{store.name}</h3>
                              <Badge variant="outline" className="text-green-600 border-green-600">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Active
                              </Badge>
                            </div>
                            <p className="text-gray-600 dark:text-gray-400 mb-3">{store.description}</p>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                              <div>
                                <label className="text-gray-500">Store ID</label>
                                <p className="font-medium">{store.slug}</p>
                              </div>
                              <div>
                                <label className="text-gray-500">Owner</label>
                                <p className="font-medium">{store.ownerEmail}</p>
                              </div>
                              <div>
                                <label className="text-gray-500">Country</label>
                                <p className="font-medium">{store.country}</p>
                              </div>
                              <div>
                                <label className="text-gray-500">Created</label>
                                <p className="font-medium">
                                  {new Date(store.createdAt).toLocaleDateString()}
                                </p>
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-2 ml-4">
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4 mr-1" />
                              View
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
