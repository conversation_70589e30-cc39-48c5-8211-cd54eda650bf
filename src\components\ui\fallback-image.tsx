import React, { useState } from 'react'
import { ImageIcon, AlertTriangle } from 'lucide-react'
import { cn } from '../../lib/utils'

interface FallbackImageProps {
  src: string
  alt: string
  className?: string
  fallbackClassName?: string
  onError?: () => void
  children?: React.ReactNode
}

export function FallbackImage({
  src,
  alt,
  className,
  fallbackClassName,
  onError,
  children
}: FallbackImageProps) {
  const [hasError, setHasError] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const handleError = () => {
    setHasError(true)
    setIsLoading(false)
    onError?.()
  }

  const handleLoad = () => {
    setIsLoading(false)
  }

  if (hasError) {
    return (
      <div className={cn(
        "flex items-center justify-center bg-gray-100 dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600",
        fallbackClassName,
        className
      )}>
        <div className="text-center p-4">
          <AlertTriangle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-500 dark:text-gray-400">Failed to load image</p>
          <p className="text-xs text-gray-400 mt-1">Image may be corrupted or unavailable</p>
        </div>
      </div>
    )
  }

  return (
    <div className="relative">
      {isLoading && (
        <div className={cn(
          "absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800",
          className
        )}>
          <div className="text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-xs text-gray-500">Loading...</p>
          </div>
        </div>
      )}
      <img
        src={src}
        alt={alt}
        className={cn(className, isLoading && "opacity-0")}
        onError={handleError}
        onLoad={handleLoad}
      />
      {children}
    </div>
  )
}

// Placeholder image component for when no image is available
export function ImagePlaceholder({ 
  className, 
  text = "No image" 
}: { 
  className?: string
  text?: string 
}) {
  return (
    <div className={cn(
      "flex items-center justify-center bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700",
      className
    )}>
      <div className="text-center p-4">
        <ImageIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
        <p className="text-sm text-gray-500 dark:text-gray-400">{text}</p>
      </div>
    </div>
  )
}
