import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'

import { Button } from '../components/ui/button'
import { Badge } from '../components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { DataTable, Column } from '../components/ui/data-table'

import {
  RotateCcw,
  RefreshCw,
  DollarSign,
  Package,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  ArrowLeft,
  Eye,
  Edit
} from 'lucide-react'

import { dataService } from '../lib/data-service'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { formatCurrency, formatDateTime } from '../lib/utils'

interface ReturnRefund {
  id: string
  orderId: string
  orderNumber: string
  customerId: string
  customerName: string
  type: 'return' | 'refund'
  status: 'pending' | 'approved' | 'processing' | 'completed' | 'rejected'
  reason: string
  amount: number
  items: Array<{
    productName: string
    quantity: number
    price: number
  }>
  requestedAt: string
  processedAt?: string
  notes?: string
}

export function ReturnsRefundsPage() {
  const navigate = useNavigate()
  const { user, hasPermission } = useFirebaseAuthStore()
  
  const [returnsRefunds, setReturnsRefunds] = useState<ReturnRefund[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalReturns: 0,
    totalRefunds: 0,
    pendingReturns: 0,
    pendingRefunds: 0,
    totalRefundAmount: 0,
    avgProcessingTime: 0
  })

  const canEdit = hasPermission(['super_admin', 'admin', 'editor'])

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />Pending</Badge>
      case 'approved':
        return <Badge className="bg-blue-100 text-blue-800"><CheckCircle className="h-3 w-3 mr-1" />Approved</Badge>
      case 'processing':
        return <Badge className="bg-purple-100 text-purple-800"><AlertTriangle className="h-3 w-3 mr-1" />Processing</Badge>
      case 'completed':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Completed</Badge>
      case 'rejected':
        return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />Rejected</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getTypeBadge = (type: string) => {
    switch (type) {
      case 'return':
        return <Badge variant="outline" className="text-blue-600 border-blue-600">Return</Badge>
      case 'refund':
        return <Badge variant="outline" className="text-green-600 border-green-600">Refund</Badge>
      default:
        return <Badge variant="secondary">{type}</Badge>
    }
  }

  const fetchReturnsRefunds = async () => {
    try {
      setLoading(true)
      
      // Mock data for demonstration
      const mockData: ReturnRefund[] = [
        {
          id: '1',
          orderId: 'ord-123',
          orderNumber: 'ORD-12345678',
          customerId: 'cust-123',
          customerName: 'John Doe',
          type: 'return',
          status: 'pending',
          reason: 'Product damaged during shipping',
          amount: 299.99,
          items: [
            {
              productName: 'White Gold - Wedding Rings',
              quantity: 1,
              price: 299.99
            }
          ],
          requestedAt: new Date().toISOString(),
          notes: 'Customer provided photos of damage'
        },
        {
          id: '2',
          orderId: 'ord-124',
          orderNumber: 'ORD-12345679',
          customerId: 'cust-124',
          customerName: 'Jane Smith',
          type: 'refund',
          status: 'completed',
          reason: 'Wrong size ordered',
          amount: 199.99,
          items: [
            {
              productName: 'Silver Necklace',
              quantity: 1,
              price: 199.99
            }
          ],
          requestedAt: new Date(Date.now() - 86400000).toISOString(),
          processedAt: new Date().toISOString(),
          notes: 'Refund processed to original payment method'
        }
      ]

      setReturnsRefunds(mockData)

      // Calculate stats
      const totalReturns = mockData.filter(item => item.type === 'return').length
      const totalRefunds = mockData.filter(item => item.type === 'refund').length
      const pendingReturns = mockData.filter(item => item.type === 'return' && item.status === 'pending').length
      const pendingRefunds = mockData.filter(item => item.type === 'refund' && item.status === 'pending').length
      const totalRefundAmount = mockData.filter(item => item.type === 'refund' && item.status === 'completed')
        .reduce((sum, item) => sum + item.amount, 0)

      setStats({
        totalReturns,
        totalRefunds,
        pendingReturns,
        pendingRefunds,
        totalRefundAmount,
        avgProcessingTime: 2.5 // days
      })
    } catch (error) {
      console.error('Error fetching returns/refunds:', error)
      toast.error('Failed to load returns and refunds')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchReturnsRefunds()
  }, [user])

  const columns: Column<ReturnRefund>[] = [
    {
      key: 'orderNumber',
      title: 'Order',
      sortable: true,
      render: (orderNumber, item) => (
        <div>
          <div className="font-medium">{orderNumber}</div>
          <div className="text-sm text-gray-500">{item.customerName}</div>
        </div>
      )
    },
    {
      key: 'type',
      title: 'Type',
      render: (type) => getTypeBadge(type)
    },
    {
      key: 'status',
      title: 'Status',
      render: (status) => getStatusBadge(status)
    },
    {
      key: 'reason',
      title: 'Reason',
      render: (reason) => (
        <div className="max-w-xs truncate" title={reason}>
          {reason}
        </div>
      )
    },
    {
      key: 'amount',
      title: 'Amount',
      sortable: true,
      render: (amount) => (
        <div className="font-medium">
          {formatCurrency(amount)}
        </div>
      )
    },
    {
      key: 'requestedAt',
      title: 'Requested',
      sortable: true,
      render: (date) => formatDateTime(date)
    }
  ]

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/admin/orders')}
            className="hover:bg-gray-100"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">Returns & Refunds</h1>
            <p className="text-gray-500">Manage customer returns and refund requests</p>
          </div>
        </div>
        <Button onClick={fetchReturnsRefunds}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Returns</CardTitle>
            <RotateCcw className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.totalReturns}</div>
            <p className="text-xs text-gray-500">{stats.pendingReturns} pending</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Refunds</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.totalRefunds}</div>
            <p className="text-xs text-gray-500">{stats.pendingRefunds} pending</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Refund Amount</CardTitle>
            <DollarSign className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{formatCurrency(stats.totalRefundAmount)}</div>
            <p className="text-xs text-gray-500">Total processed</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Processing</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.avgProcessingTime} days</div>
            <p className="text-xs text-gray-500">Processing time</p>
          </CardContent>
        </Card>
      </div>

      {/* Returns & Refunds Table */}
      <DataTable
        data={returnsRefunds}
        columns={columns}
        loading={loading}
        searchable={true}
        filterable={true}
        exportable={true}
        actions={{
          view: (item) => {
            console.log('View return/refund:', item)
            toast.success('View functionality coming soon')
          },
          edit: canEdit ? (item) => {
            console.log('Edit return/refund:', item)
            toast.success('Edit functionality coming soon')
          } : undefined
        }}
        emptyState={{
          title: 'No returns or refunds found',
          description: 'Return and refund requests will appear here.',
          icon: <RotateCcw className="h-6 w-6 text-gray-400" />
        }}
      />
    </div>
  )
}
