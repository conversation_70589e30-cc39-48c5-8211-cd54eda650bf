// Email service for sending notifications
// In a production environment, this would integrate with a service like SendGrid, Mailgun, or AWS SES
// For now, we'll simulate email sending and log the emails

export interface EmailData {
  to: string
  subject: string
  html: string
  from?: string
}

export class EmailService {
  private readonly fromEmail = '<EMAIL>'

  /**
   * Send an email using EmailJS service
   * This provides real email sending capability
   */
  async sendEmail(emailData: EmailData): Promise<void> {
    try {
      // For development and production, we'll use EmailJS for real email sending
      console.log('📧 Preparing to send email:', {
        from: emailData.from || this.fromEmail,
        to: emailData.to,
        subject: emailData.subject
      })

      // Use EmailJS for real email sending
      const emailJSResponse = await this.sendViaEmailJS(emailData)

      if (emailJSResponse.success) {
        console.log('✅ Email sent successfully via EmailJS')
      } else {
        console.log('⚠️  EmailJS not configured, logging email instead')
        console.log('📧 Email content:', {
          from: emailData.from || this.fromEmail,
          to: emailData.to,
          subject: emailData.subject,
          html: emailData.html
        })
      }

    } catch (error) {
      console.error('Error sending email:', error)
      // Don't throw error to prevent registration from failing
      console.log('📧 Email sending failed, but registration will continue')
    }
  }

  /**
   * Send email via EmailJS service
   */
  private async sendViaEmailJS(emailData: EmailData): Promise<{ success: boolean }> {
    try {
      // EmailJS configuration (you'll need to set these up)
      const EMAILJS_SERVICE_ID = 'service_womanza'
      const EMAILJS_TEMPLATE_ID = 'template_registration'
      const EMAILJS_PUBLIC_KEY = 'your_emailjs_public_key'

      // Check if EmailJS is available
      if (typeof window !== 'undefined' && (window as any).emailjs) {
        const emailjs = (window as any).emailjs

        const templateParams = {
          from_email: emailData.from || this.fromEmail,
          to_email: emailData.to,
          subject: emailData.subject,
          html_content: emailData.html,
          reply_to: emailData.from || this.fromEmail
        }

        await emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams, EMAILJS_PUBLIC_KEY)
        return { success: true }
      }

      // Fallback: Use a simple email API endpoint
      const response = await fetch('/api/send-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          from: emailData.from || this.fromEmail,
          to: emailData.to,
          subject: emailData.subject,
          html: emailData.html
        })
      })

      if (response.ok) {
        return { success: true }
      }

      return { success: false }

    } catch (error) {
      console.error('EmailJS sending failed:', error)
      return { success: false }
    }
  }

  /**
   * Send store registration notification to app admin
   */
  async sendStoreRegistrationNotification(storeData: any, ownerData: any): Promise<void> {
    const approveUrl = `${window.location.origin}/app-admin/requests`

    await this.sendEmail({
      to: '<EMAIL>',
      from: ownerData.email, // Use store owner's email as from address
      subject: `New Store Registration: ${storeData.name}`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>New Store Registration</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #3b82f6; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .info-section { margin: 20px 0; }
            .info-section h3 { color: #3b82f6; border-bottom: 2px solid #3b82f6; padding-bottom: 5px; }
            .info-list { list-style: none; padding: 0; }
            .info-list li { padding: 5px 0; border-bottom: 1px solid #eee; }
            .info-list strong { color: #333; }
            .actions { text-align: center; margin: 30px 0; }
            .btn { display: inline-block; padding: 12px 24px; margin: 0 10px; text-decoration: none; border-radius: 6px; font-weight: bold; }
            .btn-approve { background: #22c55e; color: white; }
            .btn-reject { background: #ef4444; color: white; }
            .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>New Store Registration Request</h1>
            </div>
            
            <div class="content">
              <div class="info-section">
                <h3>Store Information</h3>
                <ul class="info-list">
                  <li><strong>Store Name:</strong> ${storeData.name}</li>
                  <li><strong>Store Slug:</strong> ${storeData.slug}</li>
                  <li><strong>Business Type:</strong> ${storeData.businessType}</li>
                  <li><strong>Country:</strong> ${storeData.country}</li>
                  <li><strong>Currency:</strong> ${storeData.currency}</li>
                  <li><strong>Expected Monthly Orders:</strong> ${storeData.expectedMonthlyOrders}</li>
                </ul>
              </div>
              
              <div class="info-section">
                <h3>Owner Information</h3>
                <ul class="info-list">
                  <li><strong>Name:</strong> ${ownerData.name}</li>
                  <li><strong>Email:</strong> ${ownerData.email}</li>
                  <li><strong>Phone:</strong> ${ownerData.phone}</li>
                </ul>
              </div>
              
              <div class="info-section">
                <h3>Business Details</h3>
                <p><strong>Description:</strong></p>
                <p>${storeData.description}</p>
                <p><strong>Business Address:</strong></p>
                <p>${storeData.businessAddress}</p>
                ${storeData.website ? `<p><strong>Website:</strong> <a href="${storeData.website}">${storeData.website}</a></p>` : ''}
              </div>
              
              <div class="actions">
                <a href="${approveUrl}" class="btn btn-approve">Review in Admin Panel</a>
              </div>
            </div>
            
            <div class="footer">
              <p>This is an automated notification from the Womanza platform.</p>
              <p>Please review and process this registration request promptly.</p>
            </div>
          </div>
        </body>
        </html>
      `
    })
  }

  /**
   * Send approval notification to store owner
   */
  async sendApprovalNotification(storeData: any, ownerData: any, tempPassword?: string, customMessage?: string): Promise<void> {
    const loginUrl = `${window.location.origin}/login`
    const resetPasswordUrl = `${window.location.origin}/reset-password`
    
    await this.sendEmail({
      to: ownerData.email,
      subject: `🎉 Store Registration Approved - Welcome to Womanza!`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Store Registration Approved</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #22c55e; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .success-icon { font-size: 48px; text-align: center; margin: 20px 0; }
            .next-steps { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .next-steps ol { padding-left: 20px; }
            .next-steps li { margin: 10px 0; }
            .actions { text-align: center; margin: 30px 0; }
            .btn { display: inline-block; padding: 15px 30px; background: #3b82f6; color: white; text-decoration: none; border-radius: 6px; font-weight: bold; }
            .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🎉 Congratulations!</h1>
              <p>Your store has been approved!</p>
            </div>
            
            <div class="content">
              <div class="success-icon">✅</div>
              
              <p>Dear ${ownerData.name},</p>

              <p>We're excited to inform you that your store registration for <strong>${storeData.name}</strong> has been approved and is now active on the Womanza platform!</p>

              ${customMessage ? `<p><strong>${customMessage}</strong></p>` : ''}
              
              <div class="next-steps">
                <h3>🚀 Next Steps:</h3>
                <ol>
                  <li><strong>Access Your Admin Panel:</strong> Click the button below to login</li>
                  <li><strong>Complete Your Profile:</strong> Add your store logo and customize settings</li>
                  <li><strong>Add Your Products:</strong> Start building your product catalog</li>
                  <li><strong>Configure Payments:</strong> Set up your payment methods</li>
                  <li><strong>Launch Your Store:</strong> Go live and start selling!</li>
                </ol>
              </div>
              
              <div class="actions">
                <a href="${loginUrl}" class="btn">Access Your Admin Panel</a>
              </div>
              
              <p><strong>Your Login Details:</strong></p>
              <ul>
                <li>Email: ${ownerData.email}</li>
                <li>Password: Please use the password you set during registration</li>
              </ul>

              ${tempPassword ? `
              <div style="background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <h4 style="color: #92400e; margin: 0 0 10px 0;">🔐 Temporary Password</h4>
                <p style="margin: 0; color: #92400e;">
                  If you need to reset your password, a temporary password has been generated: <strong>${tempPassword}</strong>
                </p>
                <p style="margin: 10px 0 0 0; color: #92400e;">
                  Please change this password immediately after logging in for security.
                </p>
              </div>
              ` : ''}
              
              <p>If you have any questions or need assistance getting started, our support team is here to help!</p>
              
              <p>Welcome to the Womanza family! 🎊</p>
            </div>
            
            <div class="footer">
              <p>Best regards,<br>The Womanza Team</p>
              <p>Need help? Contact <NAME_EMAIL></p>
            </div>
          </div>
        </body>
        </html>
      `
    })
  }

  /**
   * Send rejection notification to store owner
   */
  async sendRejectionNotification(storeData: any, ownerData: any, reason: string): Promise<void> {
    await this.sendEmail({
      to: ownerData.email,
      subject: `Store Registration Update - ${storeData.name}`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Store Registration Update</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #ef4444; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .reason-box { background: #fee2e2; border: 1px solid #fecaca; padding: 15px; border-radius: 6px; margin: 20px 0; }
            .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Store Registration Update</h1>
            </div>
            
            <div class="content">
              <p>Dear ${ownerData.name},</p>
              
              <p>Thank you for your interest in joining the Womanza platform. After careful review of your application for <strong>${storeData.name}</strong>, we are unable to approve your registration at this time.</p>
              
              <div class="reason-box">
                <h3>Reason for Rejection:</h3>
                <p>${reason}</p>
              </div>
              
              <p>We encourage you to review the feedback above and consider resubmitting your application once you've addressed the mentioned concerns.</p>
              
              <p>If you have any questions about this decision or need clarification on our requirements, please don't hesitate to contact our support team.</p>
              
              <p>Thank you for your understanding.</p>
            </div>
            
            <div class="footer">
              <p>Best regards,<br>The Womanza Team</p>
              <p>Questions? Contact <NAME_EMAIL></p>
            </div>
          </div>
        </body>
        </html>
      `
    })
  }
}

// Export singleton instance
export const emailService = new EmailService()
