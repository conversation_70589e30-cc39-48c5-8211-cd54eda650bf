import { useState, useEffect } from 'react'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { dataService } from '../lib/data-service'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Badge } from '../components/ui/badge'
import { cn, formatCurrency, formatDateTime } from '../lib/utils'
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Eye,
  Calendar,
  Download,
  Filter,
  RefreshCw,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react'

interface AnalyticsData {
  revenue: {
    current: number
    previous: number
    change: number
  }
  orders: {
    current: number
    previous: number
    change: number
  }
  customers: {
    current: number
    previous: number
    change: number
  }
  products: {
    current: number
    previous: number
    change: number
  }
  conversionRate: {
    current: number
    previous: number
    change: number
  }
  avgOrderValue: {
    current: number
    previous: number
    change: number
  }
}

export function AnalyticsPage() {
  const { user } = useAuthStore()
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30d')
  const [analytics, setAnalytics] = useState<AnalyticsData>({
    revenue: { current: 0, previous: 0, change: 0 },
    orders: { current: 0, previous: 0, change: 0 },
    customers: { current: 0, previous: 0, change: 0 },
    products: { current: 0, previous: 0, change: 0 },
    conversionRate: { current: 0, previous: 0, change: 0 },
    avgOrderValue: { current: 0, previous: 0, change: 0 }
  })

  useEffect(() => {
    fetchAnalytics()
  }, [user, timeRange])

  const fetchAnalytics = async () => {
    if (!user?.store_id) return

    try {
      setLoading(true)

      // Fetch real analytics from Firebase
      // TODO: Implement real analytics service
      const realAnalytics = await dataService.getDashboardStats()

      // Convert to analytics format
      const analyticsData: AnalyticsData = {
        revenue: { current: realAnalytics.totalRevenue, previous: 0, change: 0 },
        orders: { current: realAnalytics.totalOrders, previous: 0, change: 0 },
        customers: { current: realAnalytics.totalCustomers, previous: 0, change: 0 },
        products: { current: realAnalytics.totalProducts, previous: 0, change: 0 },
        conversionRate: { current: 0, previous: 0, change: 0 },
        avgOrderValue: { current: realAnalytics.totalRevenue / Math.max(realAnalytics.totalOrders, 1), previous: 0, change: 0 }
      }

      setAnalytics(analyticsData)

      // Legacy mock data - remove after implementation
      const mockAnalytics: AnalyticsData = {
        revenue: { current: 45230, previous: 38950, change: 16.1 },
        orders: { current: 342, previous: 298, change: 14.8 },
        customers: { current: 1247, previous: 1156, change: 7.9 },
        products: { current: 89, previous: 85, change: 4.7 },
        conversionRate: { current: 3.2, previous: 2.8, change: 14.3 },
        avgOrderValue: { current: 132.25, previous: 130.70, change: 1.2 }
      }

      setAnalytics(mockAnalytics)
    } catch (error) {
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const getChangeIcon = (change: number) => {
    if (change > 0) return <ArrowUp className="h-3 w-3 text-green-600" />
    if (change < 0) return <ArrowDown className="h-3 w-3 text-red-600" />
    return <Minus className="h-3 w-3 text-gray-400" />
  }

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-green-600'
    if (change < 0) return 'text-red-600'
    return 'text-gray-400'
  }

  const timeRanges = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 3 months' },
    { value: '1y', label: 'Last year' }
  ]

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Analytics</h1>
          <p className="text-gray-500">Track your store's performance and insights</p>
        </div>
        <div className="flex items-center gap-2">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border rounded-md text-sm"
          >
            {timeRanges.map((range) => (
              <option key={range.value} value={range.value}>
                {range.label}
              </option>
            ))}
          </select>
          <Button variant="outline" onClick={fetchAnalytics}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(analytics.revenue.current)}</div>
            <div className={cn("flex items-center text-xs", getChangeColor(analytics.revenue.change))}>
              {getChangeIcon(analytics.revenue.change)}
              <span className="ml-1">{Math.abs(analytics.revenue.change).toFixed(1)}% from last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.orders.current.toLocaleString()}</div>
            <div className={cn("flex items-center text-xs", getChangeColor(analytics.orders.change))}>
              {getChangeIcon(analytics.orders.change)}
              <span className="ml-1">{Math.abs(analytics.orders.change).toFixed(1)}% from last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customers</CardTitle>
            <Users className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.customers.current.toLocaleString()}</div>
            <div className={cn("flex items-center text-xs", getChangeColor(analytics.customers.change))}>
              {getChangeIcon(analytics.customers.change)}
              <span className="ml-1">{Math.abs(analytics.customers.change).toFixed(1)}% from last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Products</CardTitle>
            <Package className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.products.current}</div>
            <div className={cn("flex items-center text-xs", getChangeColor(analytics.products.change))}>
              {getChangeIcon(analytics.products.change)}
              <span className="ml-1">{Math.abs(analytics.products.change).toFixed(1)}% from last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.conversionRate.current.toFixed(1)}%</div>
            <div className={cn("flex items-center text-xs", getChangeColor(analytics.conversionRate.change))}>
              {getChangeIcon(analytics.conversionRate.change)}
              <span className="ml-1">{Math.abs(analytics.conversionRate.change).toFixed(1)}% from last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Order Value</CardTitle>
            <BarChart3 className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(analytics.avgOrderValue.current)}</div>
            <div className={cn("flex items-center text-xs", getChangeColor(analytics.avgOrderValue.change))}>
              {getChangeIcon(analytics.avgOrderValue.change)}
              <span className="ml-1">{Math.abs(analytics.avgOrderValue.change).toFixed(1)}% from last period</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Revenue Trend</CardTitle>
            <CardDescription>Daily revenue over the selected period</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
              <div className="text-center">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Revenue chart will be displayed here</p>
                <p className="text-sm text-gray-400">Integration with charting library coming soon</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Order Volume</CardTitle>
            <CardDescription>Number of orders over time</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
              <div className="text-center">
                <ShoppingCart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Order volume chart will be displayed here</p>
                <p className="text-sm text-gray-400">Integration with charting library coming soon</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Analytics */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Top Products</CardTitle>
            <CardDescription>Best performing products by revenue</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { name: 'Elegant Summer Dress', revenue: 8450, orders: 45 },
                { name: 'Classic Denim Jacket', revenue: 6230, orders: 32 },
                { name: 'Comfortable Sneakers', revenue: 5890, orders: 38 },
                { name: 'Silk Scarf', revenue: 3420, orders: 28 },
                { name: 'Leather Handbag', revenue: 2980, orders: 15 }
              ].map((product, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-sm">{product.name}</p>
                    <p className="text-xs text-gray-500">{product.orders} orders</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-sm">{formatCurrency(product.revenue)}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Customer Segments</CardTitle>
            <CardDescription>Customer distribution by value</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { segment: 'VIP Customers', count: 45, percentage: 3.6, color: 'bg-purple-500' },
                { segment: 'Regular Customers', count: 234, percentage: 18.8, color: 'bg-blue-500' },
                { segment: 'New Customers', count: 567, percentage: 45.5, color: 'bg-green-500' },
                { segment: 'One-time Buyers', count: 401, percentage: 32.1, color: 'bg-gray-500' }
              ].map((segment, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{segment.segment}</span>
                    <span className="text-sm text-gray-500">{segment.count}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={cn("h-2 rounded-full", segment.color)}
                      style={{ width: `${segment.percentage}%` }}
                    />
                  </div>
                  <div className="text-xs text-gray-500">{segment.percentage}%</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest store activities</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { action: 'New order received', details: 'Order #WOM-1234', time: '2 minutes ago', type: 'order' },
                { action: 'Product updated', details: 'Summer Dress inventory', time: '15 minutes ago', type: 'product' },
                { action: 'Customer registered', details: '<EMAIL>', time: '1 hour ago', type: 'customer' },
                { action: 'Payment received', details: '$149.99 processed', time: '2 hours ago', type: 'payment' },
                { action: 'Review submitted', details: '5-star review on Sneakers', time: '3 hours ago', type: 'review' }
              ].map((activity, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">{activity.action}</p>
                    <p className="text-xs text-gray-500">{activity.details}</p>
                    <p className="text-xs text-gray-400">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
