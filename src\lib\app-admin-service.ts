import { signInWithEmailAndPassword } from 'firebase/auth'
import { doc, getDoc, setDoc, collection, getDocs, query, orderBy } from 'firebase/firestore'
import { auth, db, isFirebaseConfigured } from './firebase'

export interface AppAdmin {
  id: string
  email: string
  fullName: string
  role: 'app_admin'
  active: boolean
  createdAt: string
  lastLoginAt?: string
}

export class AppAdminService {
  private readonly APP_ADMIN_EMAIL = '<EMAIL>'
  private readonly APP_ADMIN_PASSWORD = 'Womanza766@@'

  /**
   * Initialize the app admin user if it doesn't exist
   */
  async initializeAppAdmin(): Promise<void> {
    if (!isFirebaseConfigured) {
      console.log('Firebase not configured, skipping app admin initialization')
      return
    }

    try {
      // Wait for authentication state to be ready
      const currentUser = auth.currentUser
      if (!currentUser) {
        console.log('No authenticated user, skipping app admin initialization')
        return
      }

      // Check if app admin already exists
      const adminDoc = await getDoc(doc(db, 'app_admins', 'womanza_app_admin'))

      if (!adminDoc.exists()) {
        // Create app admin document
        const appAdminData: AppAdmin = {
          id: 'womanza_app_admin',
          email: this.APP_ADMIN_EMAIL,
          fullName: 'Womanza App Administrator',
          role: 'app_admin',
          active: true,
          createdAt: new Date().toISOString()
        }

        await setDoc(doc(db, 'app_admins', 'womanza_app_admin'), appAdminData)
        console.log('✅ App admin initialized successfully')
      } else {
        console.log('✅ App admin already exists')
      }
    } catch (error) {
      console.error('Error initializing app admin:', error)
      // Don't throw error to prevent app initialization failure
    }
  }

  /**
   * Authenticate app admin
   */
  async authenticateAppAdmin(email: string, password: string): Promise<AppAdmin> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    // Verify credentials
    if (email !== this.APP_ADMIN_EMAIL || password !== this.APP_ADMIN_PASSWORD) {
      throw new Error('Invalid app admin credentials')
    }

    try {
      // Sign in with Firebase Auth (we'll create this user if needed)
      let userCredential
      try {
        userCredential = await signInWithEmailAndPassword(auth, email, password)
      } catch (authError: any) {
        if (authError.code === 'auth/user-not-found') {
          // App admin user doesn't exist in Firebase Auth, this is expected
          // We'll handle authentication through our custom logic
          console.log('App admin not in Firebase Auth, using custom authentication')
        } else {
          throw authError
        }
      }

      // Get app admin data
      const adminDoc = await getDoc(doc(db, 'app_admins', 'womanza_app_admin'))
      
      if (!adminDoc.exists()) {
        await this.initializeAppAdmin()
        // Get the newly created admin data
        const newAdminDoc = await getDoc(doc(db, 'app_admins', 'womanza_app_admin'))
        if (!newAdminDoc.exists()) {
          throw new Error('Failed to initialize app admin')
        }
        return newAdminDoc.data() as AppAdmin
      }

      const adminData = adminDoc.data() as AppAdmin

      // Update last login
      await setDoc(doc(db, 'app_admins', 'womanza_app_admin'), {
        ...adminData,
        lastLoginAt: new Date().toISOString()
      })

      return {
        ...adminData,
        lastLoginAt: new Date().toISOString()
      }

    } catch (error: any) {
      console.error('App admin authentication error:', error)
      throw new Error(error.message || 'Authentication failed')
    }
  }

  /**
   * Check if user is app admin
   */
  async isAppAdmin(email: string): Promise<boolean> {
    return email === this.APP_ADMIN_EMAIL
  }

  /**
   * Get app admin data
   */
  async getAppAdmin(): Promise<AppAdmin | null> {
    if (!isFirebaseConfigured) return null

    try {
      const adminDoc = await getDoc(doc(db, 'app_admins', 'womanza_app_admin'))
      return adminDoc.exists() ? adminDoc.data() as AppAdmin : null
    } catch (error) {
      console.error('Error getting app admin:', error)
      return null
    }
  }

  /**
   * Get all stores in the system
   */
  async getAllStores(): Promise<any[]> {
    if (!isFirebaseConfigured) return []

    try {
      console.log('📊 Fetching all stores from Firestore...')
      const storesQuery = query(
        collection(db, 'stores'),
        orderBy('createdAt', 'desc')
      )

      const storesSnapshot = await getDocs(storesQuery)
      const stores = storesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }))

      console.log(`✅ Found ${stores.length} stores`)
      return stores
    } catch (error) {
      console.error('Error fetching stores:', error)
      throw new Error('Failed to fetch stores')
    }
  }

  /**
   * Get store statistics
   */
  async getStoreStats(): Promise<{
    totalStores: number
    activeStores: number
    totalUsers: number
    totalOrders: number
  }> {
    if (!isFirebaseConfigured) {
      return { totalStores: 0, activeStores: 0, totalUsers: 0, totalOrders: 0 }
    }

    try {
      const stores = await this.getAllStores()
      const activeStores = stores.filter(store => store.status === 'active')

      // Get total users count
      const usersSnapshot = await getDocs(collection(db, 'users'))
      const totalUsers = usersSnapshot.size

      return {
        totalStores: stores.length,
        activeStores: activeStores.length,
        totalUsers,
        totalOrders: 0 // TODO: Implement order counting across all stores
      }
    } catch (error) {
      console.error('Error getting store stats:', error)
      return { totalStores: 0, activeStores: 0, totalUsers: 0, totalOrders: 0 }
    }
  }
}

// Export singleton instance
export const appAdminService = new AppAdminService()
