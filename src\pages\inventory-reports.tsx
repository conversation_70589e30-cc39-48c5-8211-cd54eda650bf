import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { InventoryService } from '../lib/inventory-service'
import { Button } from '../components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import { Select } from '../components/ui/select'
import { formatCurrency, formatDateTime } from '../lib/utils'
import {
  BarChart3,
  Download,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Package,
  AlertTriangle,
  DollarSign,
  Calendar,
  Filter
} from 'lucide-react'
import type { InventoryItem } from '../types/inventory'

export default function InventoryReportsPage() {
  const navigate = useNavigate()
  const { user } = useFirebaseAuthStore()

  // State
  const [items, setItems] = useState<InventoryItem[]>([])
  const [stats, setStats] = useState(null)
  const [loading, setLoading] = useState(true)
  const [reportType, setReportType] = useState('stock-levels')
  const [dateRange, setDateRange] = useState('30d')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [refreshing, setRefreshing] = useState(false)

  // Initialize service
  const storeId = user?.storeIds?.[0] || 'womanza-jewelry-store'
  const inventoryService = new InventoryService(storeId)

  // Load inventory data
  const loadInventoryData = async () => {
    try {
      setLoading(true)
      const data = await inventoryService.getInventoryOverview()
      setItems(data.items)
      setStats(data.stats)
    } catch (error) {
      console.error('Error loading inventory:', error)
      toast.error('Failed to load inventory data')
    } finally {
      setLoading(false)
    }
  }

  // Load data on mount
  useEffect(() => {
    loadInventoryData()
  }, [])

  // Handle refresh
  const handleRefresh = async () => {
    try {
      setRefreshing(true)
      await loadInventoryData()
      toast.success('Reports refreshed')
    } catch (error) {
      toast.error('Failed to refresh reports')
    } finally {
      setRefreshing(false)
    }
  }

  // Export report data
  const handleExportReport = () => {
    let reportData: any[] = []
    let filename = ''

    switch (reportType) {
      case 'stock-levels':
        reportData = items.map(item => ({
          'Product Name': item.name,
          'SKU': item.sku,
          'Category': item.category || 'Uncategorized',
          'Current Stock': item.currentStock,
          'Low Stock Threshold': item.lowStockThreshold,
          'Stock Status': item.isOutOfStock ? 'Out of Stock' : 
                         item.currentStock <= item.lowStockThreshold ? 'Low Stock' : 'In Stock',
          'Unit Price': item.price,
          'Total Value': item.currentStock * item.price,
          'Last Updated': formatDateTime(item.lastUpdated)
        }))
        filename = 'stock-levels-report'
        break

      case 'low-stock':
        reportData = items
          .filter(item => item.currentStock <= item.lowStockThreshold)
          .map(item => ({
            'Product Name': item.name,
            'SKU': item.sku,
            'Category': item.category || 'Uncategorized',
            'Current Stock': item.currentStock,
            'Low Stock Threshold': item.lowStockThreshold,
            'Shortage': item.lowStockThreshold - item.currentStock,
            'Unit Price': item.price,
            'Restock Value Needed': (item.lowStockThreshold - item.currentStock) * item.price,
            'Last Updated': formatDateTime(item.lastUpdated)
          }))
        filename = 'low-stock-report'
        break

      case 'valuation':
        reportData = items.map(item => ({
          'Product Name': item.name,
          'SKU': item.sku,
          'Category': item.category || 'Uncategorized',
          'Current Stock': item.currentStock,
          'Unit Price': item.price,
          'Total Value': item.currentStock * item.price,
          'Percentage of Total': stats ? ((item.currentStock * item.price) / stats.totalValue * 100).toFixed(2) + '%' : '0%'
        }))
        filename = 'inventory-valuation-report'
        break

      default:
        reportData = items
        filename = 'inventory-report'
    }

    if (reportData.length === 0) {
      toast.error('No data available for export')
      return
    }

    const csvContent = [
      Object.keys(reportData[0]).join(','),
      ...reportData.map(row => Object.values(row).join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${filename}-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
    
    toast.success('Report exported successfully')
  }

  // Get filtered items based on category
  const getFilteredItems = () => {
    if (categoryFilter === 'all') return items
    return items.filter(item => item.category === categoryFilter)
  }

  // Get unique categories
  const getCategories = () => {
    const categories = [...new Set(items.map(item => item.category).filter(Boolean))]
    return categories
  }

  // Get report data based on type
  const getReportData = () => {
    const filteredItems = getFilteredItems()

    switch (reportType) {
      case 'low-stock':
        return filteredItems.filter(item => item.currentStock <= item.lowStockThreshold)
      case 'out-of-stock':
        return filteredItems.filter(item => item.isOutOfStock)
      case 'valuation':
        return filteredItems.sort((a, b) => (b.currentStock * b.price) - (a.currentStock * a.price))
      default:
        return filteredItems
    }
  }

  const reportData = getReportData()
  const categories = getCategories()

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
            Stock Reports
          </h1>
          <p className="text-gray-500 dark:text-gray-400">
            Analyze your inventory data and generate reports
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            variant="outline"
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button 
            onClick={handleExportReport}
            className="bg-blue-600 hover:bg-blue-700"
            disabled={reportData.length === 0}
          >
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Report Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                Report Type
              </label>
              <Select
                value={reportType}
                onValueChange={setReportType}
                options={[
                  { value: 'stock-levels', label: '📊 Stock Levels' },
                  { value: 'low-stock', label: '⚠️ Low Stock Items' },
                  { value: 'out-of-stock', label: '❌ Out of Stock' },
                  { value: 'valuation', label: '💰 Inventory Valuation' }
                ]}
              />
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                Category
              </label>
              <Select
                value={categoryFilter}
                onValueChange={setCategoryFilter}
                options={[
                  { value: 'all', label: 'All Categories' },
                  ...categories.map(cat => ({ value: cat, label: cat }))
                ]}
              />
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                Date Range
              </label>
              <Select
                value={dateRange}
                onValueChange={setDateRange}
                options={[
                  { value: '7d', label: 'Last 7 days' },
                  { value: '30d', label: 'Last 30 days' },
                  { value: '90d', label: 'Last 90 days' },
                  { value: '1y', label: 'Last year' }
                ]}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Report Summary */}
      {stats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-blue-900 dark:text-blue-100">
                Items in Report
              </CardTitle>
              <Package className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                {reportData.length}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 border-yellow-200 dark:border-yellow-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-yellow-900 dark:text-yellow-100">
                Low Stock
              </CardTitle>
              <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                {reportData.filter(item => item.currentStock <= item.lowStockThreshold).length}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 border-red-200 dark:border-red-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-red-900 dark:text-red-100">
                Out of Stock
              </CardTitle>
              <TrendingDown className="h-4 w-4 text-red-600 dark:text-red-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-900 dark:text-red-100">
                {reportData.filter(item => item.isOutOfStock).length}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-green-900 dark:text-green-100">
                Total Value
              </CardTitle>
              <DollarSign className="h-4 w-4 text-green-600 dark:text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                {formatCurrency(reportData.reduce((sum, item) => sum + (item.currentStock * item.price), 0))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Report Data */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            {reportType === 'stock-levels' && 'Stock Levels Report'}
            {reportType === 'low-stock' && 'Low Stock Items Report'}
            {reportType === 'out-of-stock' && 'Out of Stock Items Report'}
            {reportType === 'valuation' && 'Inventory Valuation Report'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-500">Loading report data...</p>
              </div>
            </div>
          ) : reportData.length > 0 ? (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {reportData.map((item) => (
                <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800">
                  <div className="flex items-center gap-3">
                    {item.image ? (
                      <img 
                        src={item.image} 
                        alt={item.name}
                        className="w-10 h-10 rounded-lg object-cover"
                      />
                    ) : (
                      <div className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center">
                        <Package className="h-5 w-5 text-gray-400" />
                      </div>
                    )}
                    <div>
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {item.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        SKU: {item.sku} • {item.category || 'Uncategorized'}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div className="text-center">
                      <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                        {item.currentStock}
                      </div>
                      <div className="text-xs text-gray-500">Stock</div>
                    </div>
                    
                    <div className="text-center">
                      <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                        {formatCurrency(item.currentStock * item.price)}
                      </div>
                      <div className="text-xs text-gray-500">Value</div>
                    </div>
                    
                    <div>
                      {item.isOutOfStock ? (
                        <Badge variant="destructive">Out of Stock</Badge>
                      ) : item.currentStock <= item.lowStockThreshold ? (
                        <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Low Stock</Badge>
                      ) : (
                        <Badge variant="default" className="bg-green-100 text-green-800">In Stock</Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                No data found
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                No items match the current filter criteria.
              </p>
              <Button
                variant="outline"
                onClick={() => {
                  setReportType('stock-levels')
                  setCategoryFilter('all')
                }}
              >
                Reset Filters
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
