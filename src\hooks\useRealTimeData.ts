import { useState, useEffect, useCallback } from 'react'
import { dataService } from '../lib/data-service'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { useProductsStore } from '../store/products-store'
import type { Product, Order, Customer } from '../lib/firebase'

interface RealTimeDataState {
  products: Product[]
  orders: Order[]
  customers: Customer[]
  loading: boolean
  error: string | null
  lastUpdated: Date | null
}

export function useRealTimeData() {
  const { user } = useFirebaseAuthStore()
  const [state, setState] = useState<RealTimeDataState>({
    products: [],
    orders: [],
    customers: [],
    loading: true,
    error: null,
    lastUpdated: null
  })

  // Update helper function - memoized to prevent infinite loops
  const updateState = useCallback((updates: Partial<RealTimeDataState>) => {
    setState(prev => {
      // Only update if there are actual changes
      const hasChanges = Object.keys(updates).some(key => {
        const typedKey = key as keyof RealTimeDataState
        return prev[typedKey] !== updates[typedKey]
      })

      if (!hasChanges && !updates.lastUpdated) {
        return prev
      }

      return {
        ...prev,
        ...updates,
        lastUpdated: new Date()
      }
    })
  }, [])

  // Set up real-time subscriptions
  useEffect(() => {
    if (!user) {
      setState({
        products: [],
        orders: [],
        customers: [],
        loading: false,
        error: 'User not authenticated',
        lastUpdated: null
      })
      return
    }

    console.log('🔥 Setting up real-time Firebase subscriptions...')
    setState(prev => ({ ...prev, loading: true, error: null }))

    let unsubscribeProducts: (() => void) | null = null
    let unsubscribeOrders: (() => void) | null = null
    let loadingTimeout: NodeJS.Timeout | null = null

    const setupSubscriptions = async () => {
      try {
        // Products subscription with error handling
        unsubscribeProducts = dataService.subscribeToProducts((products) => {
          console.log('📦 Products updated:', products.length)
          setState(prev => ({
            ...prev,
            products,
            loading: false,
            lastUpdated: new Date()
          }))
        })

        // Orders subscription with error handling
        unsubscribeOrders = dataService.subscribeToOrders((orders) => {
          console.log('🛒 Orders updated:', orders.length)
          setState(prev => ({
            ...prev,
            orders,
            loading: false,
            lastUpdated: new Date()
          }))
        })

        // Set loading to false after a timeout if no data comes through
        loadingTimeout = setTimeout(() => {
          setState(prev => ({ ...prev, loading: false }))
        }, 5000)

      } catch (error: any) {
        console.error('Error setting up subscriptions:', error)
        setState(prev => ({
          ...prev,
          loading: false,
          error: error.message || 'Failed to set up real-time subscriptions'
        }))
      }
    }

    setupSubscriptions()

    // Cleanup subscriptions
    return () => {
      console.log('🔥 Cleaning up real-time subscriptions')
      if (loadingTimeout) clearTimeout(loadingTimeout)
      if (unsubscribeProducts) unsubscribeProducts()
      if (unsubscribeOrders) unsubscribeOrders()
    }
  }, [user?.id]) // Only depend on user.id to prevent infinite loops

  // Manual refresh function
  const refresh = useCallback(async () => {
    if (!user) return

    try {
      updateState({ loading: true, error: null })
      
      // Fetch fresh data
      const [products, orders, customers] = await Promise.all([
        dataService.getProducts(),
        dataService.getOrders(),
        dataService.getCustomers()
      ])

      updateState({
        products,
        orders,
        customers,
        loading: false
      })
    } catch (error: any) {
      console.error('Error refreshing data:', error)
      updateState({
        loading: false,
        error: error.message || 'Failed to refresh data'
      })
    }
  }, [user, updateState])

  // Get statistics
  const getStats = useCallback(() => {
    const totalRevenue = state.orders.reduce((sum, order) => sum + (order.total || 0), 0)
    const completedOrders = state.orders.filter(order => order.status === 'completed').length
    const activeProducts = state.products.filter(product => product.status === 'active').length
    
    return {
      totalRevenue,
      totalOrders: state.orders.length,
      completedOrders,
      totalProducts: state.products.length,
      activeProducts,
      totalCustomers: state.customers.length,
      lastUpdated: state.lastUpdated
    }
  }, [state])

  return {
    ...state,
    refresh,
    getStats,
    isConnected: !state.loading && !state.error,
    isEmpty: !state.loading && state.products.length === 0 && state.orders.length === 0
  }
}

// Hook for specific data types
export function useRealTimeProducts() {
  const { products, loading, error } = useProductsStore()
  const { user } = useFirebaseAuthStore()

  useEffect(() => {
    if (user) {
      const { subscribeToProducts } = useProductsStore.getState()
      const unsubscribe = subscribeToProducts()
      return unsubscribe
    }
  }, [user])

  return {
    products,
    loading,
    error,
    lastUpdated: new Date()
  }
}

export function useRealTimeOrders() {
  const { orders, loading, error, lastUpdated } = useRealTimeData()
  return { orders, loading, error, lastUpdated }
}

export function useRealTimeCustomers() {
  const { customers, loading, error, lastUpdated } = useRealTimeData()
  return { customers, loading, error, lastUpdated }
}
