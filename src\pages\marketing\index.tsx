import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'

import { useFirebaseAuthStore } from '../../store/firebase-auth'
import { Button } from '../../components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { cn } from '../../lib/utils'
import {
  Plus,
  TrendingUp,
  Users,
  Target,
  Calendar,
  DollarSign,
  Percent,
  Gift,
  Mail,
  BarChart3,
  Activity,
  Eye,
  Edit,
  Trash2,
  ArrowRight
} from 'lucide-react'

interface MarketingStats {
  activeCampaigns: number
  totalCoupons: number
  couponRedemptions: number
  promotionSales: number
  campaignEngagement: number
  conversionRate: number
}

interface RecentActivity {
  id: string
  type: 'coupon' | 'promotion' | 'campaign'
  title: string
  action: string
  timestamp: string
  status: 'active' | 'scheduled' | 'expired' | 'draft'
}

export function MarketingDashboard() {
  const navigate = useNavigate()
  const { user, hasPermission } = useFirebaseAuthStore()
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState<MarketingStats>({
    activeCampaigns: 0,
    totalCoupons: 0,
    couponRedemptions: 0,
    promotionSales: 0,
    campaignEngagement: 0,
    conversionRate: 0
  })
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])

  const canEdit = hasPermission(['super_admin', 'admin', 'editor'])
  const canDelete = hasPermission(['super_admin', 'admin'])

  useEffect(() => {
    fetchMarketingData()
  }, [user])

  const fetchMarketingData = async () => {
    // ...existing code...
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Active</Badge>
      case 'scheduled':
        return <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">Scheduled</Badge>
      case 'expired':
        return <Badge className="bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">Expired</Badge>
      case 'draft':
        return <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">Draft</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'coupon':
        return <Gift className="h-4 w-4" />
      case 'promotion':
        return <Percent className="h-4 w-4" />
      case 'campaign':
        return <Mail className="h-4 w-4" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="page-container">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="page-container">
      {/* Header */}
      <div className="page-header flex items-center justify-between">
        <div>
          <h1 className="page-title">Marketing Dashboard</h1>
          <p className="page-subtitle">Manage promotions, coupons, and campaigns</p>
        </div>
        {canEdit && (
          <div className="flex gap-2">
            <Button onClick={() => navigate('/admin/marketing/coupons/new')}>
              <Plus className="h-4 w-4 mr-2" />
              New Coupon
            </Button>
            <Button onClick={() => navigate('/admin/marketing/promotions/new')}>
              <Plus className="h-4 w-4 mr-2" />
              New Promotion
            </Button>
          </div>
        )}
      </div>

      {/* Stats Cards */}
      <div className="stats-grid-optimized">
        <Card className="card-optimized">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
            <Activity className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeCampaigns}</div>
            <p className="text-xs text-gray-500">
              +2 from last month
            </p>
          </CardContent>
        </Card>

        <Card className="card-optimized">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Coupons</CardTitle>
            <Gift className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCoupons}</div>
            <p className="text-xs text-gray-500">
              {stats.couponRedemptions} redemptions
            </p>
          </CardContent>
        </Card>

        <Card className="card-optimized">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Promotion Sales</CardTitle>
            <DollarSign className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Rs. {stats.promotionSales.toLocaleString()}</div>
            <p className="text-xs text-gray-500">
              +15% from last month
            </p>
          </CardContent>
        </Card>

        <Card className="card-optimized">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Engagement Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.campaignEngagement}%</div>
            <p className="text-xs text-gray-500">
              Conversion: {stats.conversionRate}%
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="card-optimized cursor-pointer hover:shadow-md transition-shadow" onClick={() => navigate('/admin/marketing/coupons')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Gift className="h-5 w-5 text-green-600" />
              Coupon Management
            </CardTitle>
            <CardDescription>
              Create and manage discount coupons
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">{stats.totalCoupons} active coupons</span>
              <ArrowRight className="h-4 w-4" />
            </div>
          </CardContent>
        </Card>

        <Card className="card-optimized cursor-pointer hover:shadow-md transition-shadow" onClick={() => navigate('/admin/marketing/promotions')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Percent className="h-5 w-5 text-purple-600" />
              Promotions
            </CardTitle>
            <CardDescription>
              Manage site-wide and category promotions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">{stats.activeCampaigns} running</span>
              <ArrowRight className="h-4 w-4" />
            </div>
          </CardContent>
        </Card>

        <Card className="card-optimized cursor-pointer hover:shadow-md transition-shadow" onClick={() => navigate('/admin/marketing/campaigns')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5 text-blue-600" />
              Campaigns
            </CardTitle>
            <CardDescription>
              Email and SMS marketing campaigns
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">Coming soon</span>
              <ArrowRight className="h-4 w-4" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card className="card-optimized">
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>Latest marketing activities and updates</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-white dark:bg-gray-700 rounded-lg">
                    {getTypeIcon(activity.type)}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-gray-100">{activity.title}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{activity.action}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(activity.status)}
                  <span className="text-xs text-gray-500">{activity.timestamp}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
