import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore'
import { db, auth, isFirebaseConfigured } from './firebase'

export interface Notification {
  id: string
  userId: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  read: boolean
  actionUrl?: string
  actionLabel?: string
  createdAt: string
  readAt?: string
}

export class NotificationService {
  private storeId = import.meta.env.VITE_STORE_ID || 'womanza'

  // Helper method to get current store ID dynamically
  private getCurrentStoreId(): string {
    // Get from current user if authenticated
    if (auth.currentUser) {
      // Try to get from localStorage as a fallback
      const storedStoreId = localStorage.getItem('currentStoreId')
      if (storedStoreId) {
        return storedStoreId
      }
    }

    return this.storeId
  }

  /**
   * Subscribe to real-time notifications for a user
   */
  subscribeToNotifications(
    userId: string,
    callback: (notifications: Notification[]) => void
  ): () => void {
    if (!isFirebaseConfigured) {
      // Return empty notifications for development
      callback([])
      return () => {}
    }

    // Check if user is authenticated
    if (!auth.currentUser) {
      console.warn('No authenticated user for notifications subscription')
      callback([])
      return () => {}
    }

    const currentStoreId = this.getCurrentStoreId()
    if (!currentStoreId) {
      console.warn('No store context available for notifications subscription')
      callback([])
      return () => {}
    }

    // For now, disable notifications to avoid permission errors
    // TODO: Implement proper notification permissions
    console.log('📢 Notifications temporarily disabled to avoid permission errors')
    callback([])
    return () => {}

    try {
      const notificationsRef = collection(db, 'stores', currentStoreId, 'notifications')
      const q = query(
        notificationsRef,
        where('userId', '==', userId),
        orderBy('createdAt', 'desc'),
        limit(50)
      )

      return onSnapshot(q,
        (snapshot) => {
          const notifications: Notification[] = snapshot.docs.map(doc => {
            const data = doc.data()
            return {
              id: doc.id,
              ...data,
              createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
              readAt: data.readAt?.toDate?.()?.toISOString() || data.readAt
            } as Notification
          })
          callback(notifications)
          console.log(`🔔 Loaded ${notifications.length} notifications for user: ${userId} in store: ${currentStoreId}`)
        },
        (error) => {
          console.error('❌ Error in notifications subscription:', error)
          // Check if it's a permission error and handle gracefully
          if (error.code === 'permission-denied') {
            console.warn('⚠️ Permission denied for notifications - user may not have access to this store')
            // Return empty notifications on permission error to prevent crashes
            callback([])
            return
          }
          // Return empty notifications on other errors to prevent crashes
          callback([])
        }
      )
    } catch (error) {
      console.error('Error setting up notifications subscription:', error)
      callback([])
      return () => {}
    }
  }

  /**
   * Create a new notification
   */
  async createNotification(notification: Omit<Notification, 'id' | 'createdAt'>): Promise<void> {
    if (!isFirebaseConfigured) return

    const currentStoreId = this.getCurrentStoreId()
    if (!currentStoreId) {
      console.warn('No store context available for creating notification')
      return
    }

    try {
      const notificationsRef = collection(db, 'stores', currentStoreId, 'notifications')
      await addDoc(notificationsRef, {
        ...notification,
        createdAt: serverTimestamp()
      })
      console.log(`✅ Created notification for store: ${currentStoreId}`)
    } catch (error) {
      console.error('Error creating notification:', error)
      throw error
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string): Promise<void> {
    if (!isFirebaseConfigured) return

    const currentStoreId = this.getCurrentStoreId()
    if (!currentStoreId) {
      console.warn('No store context available for marking notification as read')
      return
    }

    try {
      const notificationRef = doc(db, 'stores', currentStoreId, 'notifications', notificationId)
      await updateDoc(notificationRef, {
        read: true,
        readAt: serverTimestamp()
      })
    } catch (error) {
      console.error('Error marking notification as read:', error)
      throw error
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllAsRead(userId: string): Promise<void> {
    if (!isFirebaseConfigured) return

    try {
      // This would require a batch operation in a real implementation
      // For now, we'll implement it as a cloud function trigger
      console.log('Mark all as read for user:', userId)
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
      throw error
    }
  }

  /**
   * Delete a notification
   */
  async deleteNotification(notificationId: string): Promise<void> {
    if (!isFirebaseConfigured) return

    try {
      const notificationRef = doc(db, 'stores', this.storeId, 'notifications', notificationId)
      await deleteDoc(notificationRef)
    } catch (error) {
      console.error('Error deleting notification:', error)
      throw error
    }
  }

  /**
   * Create system notifications for common events
   */
  async notifyOrderCreated(userId: string, orderNumber: string): Promise<void> {
    await this.createNotification({
      userId,
      title: 'New Order Received',
      message: `Order ${orderNumber} has been placed`,
      type: 'info',
      read: false,
      actionUrl: `/admin/orders?search=${orderNumber}`,
      actionLabel: 'View Order'
    })
  }

  async notifyLowStock(userId: string, productName: string, quantity: number): Promise<void> {
    await this.createNotification({
      userId,
      title: 'Low Stock Alert',
      message: `${productName} is running low (${quantity} remaining)`,
      type: 'warning',
      read: false,
      actionUrl: '/admin/products',
      actionLabel: 'Manage Inventory'
    })
  }

  async notifyUserCreated(userId: string, newUserEmail: string): Promise<void> {
    await this.createNotification({
      userId,
      title: 'New User Created',
      message: `User ${newUserEmail} has been added to the system`,
      type: 'success',
      read: false,
      actionUrl: '/admin/users',
      actionLabel: 'View Users'
    })
  }
}

// Export singleton instance
export const notificationService = new NotificationService()
