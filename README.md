# Womanza Admin Panel

A comprehensive, role-based admin panel for managing the Womanza e-commerce store. Built with React, Vite, TailwindCSS, and Firebase.

## 🚀 Features

- **Role-based Access Control**: Super Admin, Admin, Editor, and Viewer roles
- **Product Management**: Complete CRUD operations for products, categories, and inventory
- **Order Management**: Track and manage customer orders and payments
- **Customer Management**: Manage customers and customer groups
- **Analytics Dashboard**: Sales overview and performance metrics
- **Marketing Tools**: Coupon management and promotional campaigns
- **Store Customization**: Branding and theme management
- **User Management**: Create and manage admin users with different roles
- **Responsive Design**: Works on desktop and mobile devices

## 🛠️ Tech Stack

- **Frontend**: React 18 + Vite + TypeScript
- **Styling**: TailwindCSS + Radix UI
- **State Management**: Zustand
- **Backend**: Firebase (Firestore + Auth + Storage)
- **Routing**: React Router DOM
- **Data Fetching**: TanStack Query
- **Icons**: Lucide React

## 📋 Prerequisites

- Node.js 18+ and npm
- Firebase account (optional for development)
- Git

## 🔧 Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

### 2. Development Mode (Recommended)

The app is configured to work in development mode with Firebase emulators and mock data:

```bash
# Setup emulators (one-time setup)
npm run setup:emulators

# Start development with emulators
npm run dev:firebase
```

This will start:
- Firebase emulators (Auth, Firestore, Storage)
- Development server
- Emulator UI at http://localhost:4000

### 3. Production Firebase Setup (Optional)

For production deployment:

1. Create a Firebase project at [console.firebase.google.com](https://console.firebase.google.com)
2. Enable Authentication, Firestore, and Storage
3. Update environment variables (see below)

### 4. Environment Variables

For development, the default `.env` file is already configured for emulator mode.

For production, update these variables:

```env
# Firebase Configuration
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id

# Set to false for production
VITE_USE_FIREBASE_EMULATOR=false

# Store Configuration
VITE_STORE_ID=your_store_id
```

### 5. Development Credentials

In development mode, use these test credentials:

- **Super Admin**: <EMAIL> / password123
- **Admin**: <EMAIL> / password123
- **Editor**: <EMAIL> / password123
- **Viewer**: <EMAIL> / password123

### 6. Run the Application

```bash
# Development with emulators
npm run dev:firebase

# Or just the app (requires Firebase setup)
npm run dev
```

The application will be available at `http://localhost:5173`

## 👥 User Roles

### Super Admin
- Full system access
- Can manage all users, settings, and store configuration
- Cannot be deleted or demoted by other users

### Admin
- Can manage products, orders, customers, and marketing
- Can create/manage Editor and Viewer users
- Cannot modify Super Admin accounts

### Editor
- Can manage products, categories, inventory, and orders
- Can update order statuses and stock levels
- Read-only access to customers and analytics

### Viewer
- Read-only access to dashboard and reports
- Can view products, orders, and customer data
- Cannot make any modifications

## 🔐 Security Features

- JWT-based authentication via Supabase
- Row Level Security (RLS) policies
- Role-based UI rendering
- HTTPS-only access
- No public signup (admin-only user creation)
- Session management and auto-refresh

## 📱 Performance Optimizations

- Code splitting with React.lazy()
- Image optimization and lazy loading
- Efficient state management with Zustand
- Query caching with TanStack Query
- Optimized bundle size with Vite
- CSS-in-JS with TailwindCSS for minimal bundle

## 🚀 Deployment

### Vercel (Recommended)

1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Manual Deployment

```bash
npm run build
# Upload dist/ folder to your hosting provider
```

## 📊 Firebase Collections

The application uses the following Firestore collections:
- `users` - Admin panel users with roles
- `products` - Product catalog
- `categories` - Product categories
- `customers` - Customer information
- `orders` - Order management
- `campaigns` - Marketing campaigns and coupons
- `analytics` - Store analytics and reports
- `stores` - Store configuration

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is proprietary software for Womanza store management.
