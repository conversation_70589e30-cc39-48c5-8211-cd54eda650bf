{"name": "womanza-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "firebase:emulators": "firebase emulators:start", "firebase:emulators:ui": "firebase emulators:start --only firestore,auth,storage", "dev:firebase": "concurrently \"npm run firebase:emulators:ui\" \"npm run dev\"", "deploy:all": "firebase deploy", "create:store": "node scripts/create-store.js", "check:user": "node scripts/check-user-role.js"}, "dependencies": {"@tanstack/react-query": "^5.83.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^12.0.0", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.7.0", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/node": "^24.0.14", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "dotenv": "^17.2.1", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "firebase-admin": "^13.4.0", "firebase-tools": "^14.11.0", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^7.0.4"}}