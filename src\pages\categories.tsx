import { useState, useEffect } from 'react'
import { Button } from '../components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Input } from '../components/ui/input'
import { Label } from '../components/ui/label'
import { Badge } from '../components/ui/badge'
import { Textarea } from '../components/ui/textarea'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { useCategoriesStore, type Category } from '../store/categories-store'
import toast from 'react-hot-toast'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  FolderTree,
  Tag,
  Eye,
  EyeOff,
  Save,
  X,
  ChevronRight,
  ChevronDown,
  Folder,
  FolderOpen,
  Hash
} from 'lucide-react'

export function CategoriesPage() {
  const { user, hasPermission } = useFirebaseAuthStore()
  const { 
    categories, 
    loading, 
    error, 
    fetchCategories, 
    addCategory, 
    updateCategory, 
    deleteCategory,
    clearError 
  } = useCategoriesStore()

  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set())
  const [viewMode, setViewMode] = useState<'hierarchy' | 'flat'>('hierarchy')
  const [createForm, setCreateForm] = useState({
    name: '',
    slug: '',
    description: '',
    status: 'active' as const,
    sortOrder: 0,
    parentId: '' as string
  })

  const canEdit = hasPermission(['super_admin', 'admin', 'editor'])
  const canDelete = hasPermission(['super_admin', 'admin'])

  // Helper functions for hierarchical display
  const buildCategoryTree = (categories: Category[]): Category[] => {
    const categoryMap = new Map<string, Category & { children: Category[] }>()
    const rootCategories: (Category & { children: Category[] })[] = []

    // First pass: create map with children arrays
    categories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] })
    })

    // Second pass: build tree structure
    categories.forEach(category => {
      const categoryWithChildren = categoryMap.get(category.id)!
      if (category.parentId && categoryMap.has(category.parentId)) {
        categoryMap.get(category.parentId)!.children.push(categoryWithChildren)
      } else {
        rootCategories.push(categoryWithChildren)
      }
    })

    // Sort categories by sortOrder
    const sortCategories = (cats: (Category & { children: Category[] })[]) => {
      cats.sort((a, b) => a.sortOrder - b.sortOrder)
      cats.forEach(cat => sortCategories(cat.children))
    }
    sortCategories(rootCategories)

    return rootCategories
  }

  const getSubcategoryCount = (categoryId: string): number => {
    return categories.filter(cat => cat.parentId === categoryId).length
  }

  const getCategoryPath = (categoryId: string): string => {
    const category = categories.find(cat => cat.id === categoryId)
    if (!category) return ''

    if (category.parentId) {
      const parentPath = getCategoryPath(category.parentId)
      return parentPath ? `${parentPath} > ${category.name}` : category.name
    }
    return category.name
  }

  const toggleExpanded = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId)
    } else {
      newExpanded.add(categoryId)
    }
    setExpandedCategories(newExpanded)
  }

  // Component to render hierarchical category tree
  const CategoryTreeItem = ({
    category,
    level = 0
  }: {
    category: Category & { children: Category[] },
    level?: number
  }) => {
    const hasChildren = category.children.length > 0
    const isExpanded = expandedCategories.has(category.id)
    const subcategoryCount = getSubcategoryCount(category.id)

    return (
      <div>
        <div
          className={`flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 ${
            level > 0 ? 'ml-6 border-l-4 border-l-blue-200' : ''
          }`}
          style={{ marginLeft: level * 24 }}
        >
          <div className="flex-1">
            <div className="flex items-center gap-3">
              {hasChildren ? (
                <button
                  onClick={() => toggleExpanded(category.id)}
                  className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                >
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4 text-gray-600" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-600" />
                  )}
                </button>
              ) : (
                <div className="w-6 h-6 flex items-center justify-center">
                  <Hash className="h-3 w-3 text-gray-400" />
                </div>
              )}

              {hasChildren ? (
                isExpanded ? (
                  <FolderOpen className="h-5 w-5 text-blue-600" />
                ) : (
                  <Folder className="h-5 w-5 text-blue-600" />
                )
              ) : (
                <Tag className="h-5 w-5 text-gray-600" />
              )}

              <div>
                <h3 className="font-medium text-gray-900 dark:text-gray-100">
                  {category.name}
                </h3>
                {category.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {category.description}
                  </p>
                )}
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant={category.status === 'active' ? 'default' : 'secondary'}>
                    {category.status}
                  </Badge>
                  {hasChildren && (
                    <Badge variant="outline" className="text-xs">
                      {subcategoryCount} subcategories
                    </Badge>
                  )}
                  {level > 0 && (
                    <Badge variant="outline" className="text-xs">
                      Level {level + 1}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>

          {canEdit && (
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleEdit(category)}
                className="h-8 w-8 p-0"
              >
                <Edit className="h-4 w-4" />
              </Button>
              {canDelete && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDelete(category.id)}
                  className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          )}
        </div>

        {hasChildren && isExpanded && (
          <div className="mt-2 space-y-2">
            {category.children.map((child) => (
              <CategoryTreeItem
                key={child.id}
                category={child}
                level={level + 1}
              />
            ))}
          </div>
        )}
      </div>
    )
  }

  // Load categories from Firebase
  useEffect(() => {
    fetchCategories()
  }, [])

  // Auto-generate slug from name
  const handleNameChange = (name: string) => {
    const slug = name.toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
    
    setCreateForm(prev => ({ ...prev, name, slug }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!createForm.name.trim() || !createForm.slug.trim()) {
      alert('Please fill in all required fields')
      return
    }

    try {
      if (editingCategory) {
        await updateCategory(editingCategory.id, createForm)
        console.log('Category updated successfully')
      } else {
        await addCategory(createForm)
        console.log('Category created successfully')
      }

      setShowCreateForm(false)
      setEditingCategory(null)
      setCreateForm({
        name: '',
        slug: '',
        description: '',
        status: 'active',
        sortOrder: 0,
        parentId: ''
      })
    } catch (error: any) {
      console.error('Error saving category:', error)
      alert('Failed to save category: ' + error.message)
    }
  }

  const handleEdit = (category: Category) => {
    setEditingCategory(category)
    setCreateForm({
      name: category.name,
      slug: category.slug,
      description: category.description || '',
      status: category.status,
      sortOrder: category.sortOrder
    })
    setShowCreateForm(true)
  }

  const handleDelete = async (categoryId: string) => {
    if (!window.confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      return
    }

    try {
      await deleteCategory(categoryId)
      console.log('Category deleted successfully')
    } catch (error: any) {
      console.error('Error deleting category:', error)
      alert('Failed to delete category: ' + error.message)
    }
  }

  const handleToggleStatus = async (category: Category) => {
    try {
      await updateCategory(category.id, {
        status: category.status === 'active' ? 'inactive' : 'active'
      })
    } catch (error: any) {
      console.error('Error toggling category status:', error)
    }
  }

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Categories</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Organize your products with categories
          </p>
        </div>
        {canEdit && (
          <Button onClick={() => setShowCreateForm(true)} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Add Category
          </Button>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
              <Button variant="ghost" size="sm" onClick={clearError}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search and View Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex gap-4 items-center">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search categories..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant={viewMode === 'hierarchy' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('hierarchy')}
                className="flex items-center gap-2"
              >
                <FolderTree className="h-4 w-4" />
                Tree View
              </Button>
              <Button
                variant={viewMode === 'flat' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('flat')}
                className="flex items-center gap-2"
              >
                <Tag className="h-4 w-4" />
                List View
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Create/Edit Form */}
      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FolderTree className="h-5 w-5" />
              {editingCategory ? 'Edit Category' : 'Create New Category'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Category Name *</Label>
                  <Input
                    id="name"
                    value={createForm.name}
                    onChange={(e) => handleNameChange(e.target.value)}
                    placeholder="Enter category name"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="slug">Slug *</Label>
                  <Input
                    id="slug"
                    value={createForm.slug}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, slug: e.target.value }))}
                    placeholder="category-slug"
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={createForm.description}
                  onChange={(e) => setCreateForm(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Category description..."
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="parentId">Parent Category</Label>
                <select
                  id="parentId"
                  value={createForm.parentId}
                  onChange={(e) => setCreateForm(prev => ({ ...prev, parentId: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">No Parent (Top Level)</option>
                  {categories
                    .filter(cat => cat.id !== editingCategory?.id) // Don't allow self as parent
                    .map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  Select a parent category to create a subcategory
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="status">Status</Label>
                  <select
                    id="status"
                    value={createForm.status}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, status: e.target.value as 'active' | 'inactive' }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="sortOrder">Sort Order</Label>
                  <Input
                    id="sortOrder"
                    type="number"
                    value={createForm.sortOrder}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, sortOrder: parseInt(e.target.value) || 0 }))}
                    placeholder="0"
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <Button type="submit" className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  {editingCategory ? 'Update Category' : 'Create Category'}
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => {
                    setShowCreateForm(false)
                    setEditingCategory(null)
                    setCreateForm({ name: '', slug: '', description: '', status: 'active', sortOrder: 0, parentId: '' })
                  }}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Categories List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Tag className="h-5 w-5" />
            Categories ({filteredCategories.length})
          </CardTitle>
          <CardDescription>
            Manage your product categories
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">Loading categories...</p>
            </div>
          ) : filteredCategories.length === 0 ? (
            <div className="text-center py-8">
              <FolderTree className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No categories found</p>
              {canEdit && (
                <Button onClick={() => setShowCreateForm(true)} className="mt-4">
                  Create your first category
                </Button>
              )}
            </div>
          ) : viewMode === 'hierarchy' ? (
            <div className="space-y-3">
              {buildCategoryTree(filteredCategories).map((category) => (
                <CategoryTreeItem key={category.id} category={category} />
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredCategories.map((category) => (
                <div
                  key={category.id}
                  className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3">
                      <Tag className="h-5 w-5 text-gray-600" />
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-gray-100">
                          {category.name}
                        </h3>
                        {category.parentId && (
                          <p className="text-xs text-gray-500 mt-1">
                            Path: {getCategoryPath(category.id)}
                          </p>
                        )}
                        {category.description && (
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {category.description}
                          </p>
                        )}
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant={category.status === 'active' ? 'default' : 'secondary'}>
                            {category.status}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            Sort: {category.sortOrder}
                          </Badge>
                          {getSubcategoryCount(category.id) > 0 && (
                            <Badge variant="outline" className="text-xs">
                              {getSubcategoryCount(category.id)} subcategories
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {canEdit && (
                      <>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleStatus(category)}
                          className="h-8 w-8 p-0"
                        >
                          {category.status === 'active' ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(category)}
                          className="h-8 w-8 p-0"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                    {canDelete && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(category.id)}
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
