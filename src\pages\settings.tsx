import { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { Label } from '../components/ui/label'
import { Badge } from '../components/ui/badge'
import { Textarea } from '../components/ui/textarea'
import { Switch } from '../components/ui/switch'
import { Select } from '../components/ui/select'
import {
  Save,
  AlertTriangle,
  X,
  Loader2,
  Store,
  Palette,
  Search,
  Shield,
  Mail,
  CreditCard,
  Truck,
  Calculator,
  FileText,
  Sliders,
  Monitor,
  Upload,
  Trash2,
  ImageIcon
} from 'lucide-react'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { useStoreManagement } from '../store/store-management'
import { useRealtimeSettings } from '../hooks/use-realtime-settings'

type TabType = 'general' | 'branding' | 'seo' | 'auth' | 'email' | 'payment' | 'shipping' | 'tax' | 'compliance' | 'behavior' | 'panel-preferences'

const tabs = [
  { id: 'general', label: 'General', icon: Store },
  { id: 'branding', label: 'Branding', icon: Palette },
  { id: 'seo', label: 'SEO', icon: Search },
  { id: 'auth', label: 'Auth', icon: Shield },
  { id: 'email', label: 'Email', icon: Mail },
  { id: 'payment', label: 'Payment', icon: CreditCard },
  { id: 'shipping', label: 'Shipping', icon: Truck },
  { id: 'tax', label: 'Tax', icon: Calculator },
  { id: 'compliance', label: 'Legal', icon: FileText },
  { id: 'behavior', label: 'Behavior', icon: Sliders },
  { id: 'panel-preferences', label: 'Panel', icon: Monitor }
] as const

export function SettingsPage() {
  const { hasPermission } = useFirebaseAuthStore()
  const { currentStore, syncStoreNameFromSettings } = useStoreManagement()
  const [activeTab, setActiveTab] = useState<TabType>('general')
  const [pendingChanges, setPendingChanges] = useState<Record<string, any>>({})
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  const {
    settings,
    loading,
    updateGeneral,
    updateBranding,
    updateSEO,
    updateAuth,
    updateEmail,
    updatePayment,
    updateShipping,
    updateTax,
    updateCompliance,
    updateBehavior,
    updatePanelPreferences,
    initializeSettings
  } = useRealtimeSettings(currentStore?.id || 'default')

  const canEdit = hasPermission(['settings:write'])

  // Initialize settings if they don't exist
  useEffect(() => {
    if (!loading && !settings && currentStore?.id) {
      console.log('🔧 Initializing settings for store:', currentStore.id)
      initializeSettings()
    }
  }, [loading, settings, currentStore?.id, initializeSettings])

  // Function to generate slug from store name
  const generateSlug = (name: string): string => {
    return name
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
      .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
  }

  // Handle field changes (store in pending changes instead of immediate save)
  const handleFieldChange = (category: string, field: string, value: any) => {
    const changeKey = `${category}.${field}`
    const newChanges = {
      ...pendingChanges,
      [changeKey]: value
    }

    // Auto-update store slug when store name changes
    if (category === 'general' && field === 'storeName' && value) {
      const slugKey = 'general.storeSlug'
      // Always auto-update slug when store name changes
      newChanges[slugKey] = generateSlug(value)
    }

    setPendingChanges(newChanges)
    setHasUnsavedChanges(true)
  }

  // Get current value (from pending changes or settings)
  const getCurrentValue = (category: string, field: string) => {
    const changeKey = `${category}.${field}`
    if (changeKey in pendingChanges) {
      return pendingChanges[changeKey]
    }
    return settings?.[category as keyof typeof settings]?.[field as any] || ''
  }

  // Handle save changes
  const handleSaveChanges = async () => {
    if (!hasUnsavedChanges) return

    setIsSaving(true)
    try {
      // Group changes by category
      const changesByCategory: Record<string, Record<string, any>> = {}

      Object.entries(pendingChanges).forEach(([key, value]) => {
        const [category, field] = key.split('.')
        if (!changesByCategory[category]) {
          changesByCategory[category] = {}
        }
        changesByCategory[category][field] = value
      })

      // Save each category
      for (const [category, changes] of Object.entries(changesByCategory)) {
        switch (category) {
          case 'general':
            await updateGeneral(changes)
            // Sync store name to main store document if it was updated
            if (changes.storeName && currentStore?.id) {
              await syncStoreNameFromSettings(currentStore.id, changes.storeName, changes.storeSlug)
            }
            break
          case 'branding':
            await updateBranding(changes)
            break
          case 'seo':
            await updateSEO(changes)
            break
          case 'auth':
            await updateAuth(changes)
            break
          case 'email':
            await updateEmail(changes)
            break
          case 'payment':
            await updatePayment(changes)
            break
          case 'shipping':
            await updateShipping(changes)
            break
          case 'tax':
            await updateTax(changes)
            break
          case 'compliance':
            await updateCompliance(changes)
            break
          case 'behavior':
            await updateBehavior(changes)
            break
          case 'panel-preferences':
            await updatePanelPreferences(changes)
            break
        }
      }

      setPendingChanges({})
      setHasUnsavedChanges(false)
      toast.success('Settings saved successfully!')
      console.log('Settings saved successfully')
    } catch (error) {
      console.error('Error saving settings:', error)
      toast.error('Failed to save settings. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  // Handle discard changes
  const handleDiscardChanges = () => {
    setPendingChanges({})
    setHasUnsavedChanges(false)
    console.log('Changes discarded')
  }

  if (loading) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">Settings</h1>
          <p className="text-gray-500 dark:text-gray-400">Configure your store settings and preferences</p>
        </div>
        <div className="flex items-center gap-3">
          {isSaving && (
            <div className="flex items-center text-sm text-gray-500">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-accent-600 mr-2"></div>
              Saving...
            </div>
          )}
          <Badge variant="outline" className="text-xs">
            Store: {currentStore?.name || 'Default'}
          </Badge>
        </div>
      </div>

      {/* Save/Discard Bar */}
      {hasUnsavedChanges && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-amber-200 dark:border-amber-700 overflow-hidden">
          <div className="bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border-b border-amber-200 dark:border-amber-700 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-8 h-8 bg-amber-100 dark:bg-amber-900/40 rounded-full">
                  <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-amber-800 dark:text-amber-200">
                    Unsaved Changes
                  </h4>
                  <p className="text-xs text-amber-600 dark:text-amber-400">
                    You have unsaved changes that will be lost if you navigate away
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDiscardChanges}
                  disabled={isSaving}
                  className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <X className="h-4 w-4 mr-2" />
                  Discard Changes
                </Button>
                <Button
                  size="sm"
                  onClick={handleSaveChanges}
                  disabled={isSaving}
                  className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-sm"
                >
                  {isSaving ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  {isSaving ? 'Saving Changes...' : 'Save All Changes'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => {
              const Icon = tab.icon
              const isActive = activeTab === tab.id
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    isActive
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors`}
                >
                  <Icon className="h-4 w-4" />
                  {tab.label}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'general' && (
            <div className="space-y-6">
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">General Settings</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Basic store information and configuration</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="storeName">Store Name</Label>
                  <Input
                    id="storeName"
                    value={getCurrentValue('general', 'storeName') || ''}
                    onChange={(e) => handleFieldChange('general', 'storeName', e.target.value)}
                    disabled={!canEdit}
                    placeholder="My Store"
                  />
                </div>
                <div>
                  <Label htmlFor="storeSlug">Store Slug</Label>
                  <Input
                    id="storeSlug"
                    value={getCurrentValue('general', 'storeSlug') || ''}
                    onChange={(e) => handleFieldChange('general', 'storeSlug', e.target.value)}
                    disabled={true}
                    placeholder="my-store"
                    className="bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-400"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Auto-generated from store name. This will be used in your store URL.
                  </p>
                </div>
              </div>

              <div>
                <Label htmlFor="description">Store Description</Label>
                <Textarea
                  id="description"
                  value={getCurrentValue('general', 'description') || ''}
                  onChange={(e) => handleFieldChange('general', 'description', e.target.value)}
                  disabled={!canEdit}
                  placeholder="Brief description of your store"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="contactEmail">Contact Email</Label>
                  <Input
                    id="contactEmail"
                    type="email"
                    value={getCurrentValue('general', 'contactEmail') || ''}
                    onChange={(e) => handleFieldChange('general', 'contactEmail', e.target.value)}
                    disabled={!canEdit}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="contactPhone">Contact Phone</Label>
                  <Input
                    id="contactPhone"
                    value={getCurrentValue('general', 'contactPhone') || ''}
                    onChange={(e) => handleFieldChange('general', 'contactPhone', e.target.value)}
                    disabled={!canEdit}
                    placeholder="+****************"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Label htmlFor="currency">Currency</Label>
                  <Select
                    value={getCurrentValue('general', 'currency') || 'USD'}
                    onValueChange={(value) => handleFieldChange('general', 'currency', value)}
                    disabled={!canEdit}
                    options={[
                      { value: 'USD', label: 'US Dollar ($)' },
                      { value: 'EUR', label: 'Euro (€)' },
                      { value: 'GBP', label: 'British Pound (£)' },
                      { value: 'JPY', label: 'Japanese Yen (¥)' },
                      { value: 'CAD', label: 'Canadian Dollar (C$)' },
                      { value: 'AUD', label: 'Australian Dollar (A$)' },
                      { value: 'PKR', label: 'Pakistani Rupee (₨)' }
                    ]}
                  />
                </div>
                <div>
                  <Label htmlFor="locale">Locale</Label>
                  <Select
                    value={getCurrentValue('general', 'locale') || 'en-US'}
                    onValueChange={(value) => handleFieldChange('general', 'locale', value)}
                    disabled={!canEdit}
                    options={[
                      { value: 'en-US', label: 'English (US)' },
                      { value: 'en-GB', label: 'English (UK)' },
                      { value: 'es-ES', label: 'Spanish' },
                      { value: 'fr-FR', label: 'French' },
                      { value: 'de-DE', label: 'German' },
                      { value: 'it-IT', label: 'Italian' },
                      { value: 'pt-BR', label: 'Portuguese (Brazil)' },
                      { value: 'ja-JP', label: 'Japanese' },
                      { value: 'ko-KR', label: 'Korean' },
                      { value: 'zh-CN', label: 'Chinese (Simplified)' }
                    ]}
                  />
                </div>
                <div>
                  <Label htmlFor="timeZone">Time Zone</Label>
                  <Select
                    value={getCurrentValue('general', 'timeZone') || 'UTC'}
                    onValueChange={(value) => handleFieldChange('general', 'timeZone', value)}
                    disabled={!canEdit}
                    options={[
                      { value: 'UTC', label: 'UTC' },
                      { value: 'America/New_York', label: 'Eastern Time (ET)' },
                      { value: 'America/Chicago', label: 'Central Time (CT)' },
                      { value: 'America/Denver', label: 'Mountain Time (MT)' },
                      { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
                      { value: 'Europe/London', label: 'London (GMT)' },
                      { value: 'Europe/Paris', label: 'Paris (CET)' },
                      { value: 'Asia/Tokyo', label: 'Tokyo (JST)' },
                      { value: 'Asia/Shanghai', label: 'Shanghai (CST)' },
                      { value: 'Australia/Sydney', label: 'Sydney (AEST)' },
                      { value: 'Asia/Karachi', label: 'Karachi (PKT)' }
                    ]}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="status">Store Status</Label>
                <Select
                  value={getCurrentValue('general', 'status') || 'active'}
                  onValueChange={(value) => handleFieldChange('general', 'status', value)}
                  disabled={!canEdit}
                  options={[
                    { value: 'active', label: 'Active' },
                    { value: 'inactive', label: 'Inactive' },
                    { value: 'maintenance', label: 'Maintenance' }
                  ]}
                />
              </div>
            </div>
          )}

          {activeTab === 'branding' && (
            <div className="space-y-6">
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Branding & Appearance</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Customize your store's visual identity and theme</p>
              </div>

              {/* Logo Upload */}
              <div>
                <Label className="text-sm font-medium">Store Logo</Label>
                <div className="mt-2 flex items-center gap-4">
                  <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-600">
                    {getCurrentValue('branding', 'logoUrl') ? (
                      <img
                        src={getCurrentValue('branding', 'logoUrl')}
                        alt="Store Logo"
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <ImageIcon className="h-6 w-6 text-gray-400" />
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      Upload your store logo (recommended: 200x200px, PNG or JPG)
                    </p>
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          // TODO: Implement logo upload
                          console.log('Logo upload feature coming soon')
                        }}
                        disabled={!canEdit}
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Upload Logo
                      </Button>
                      {getCurrentValue('branding', 'logoUrl') && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => handleFieldChange('branding', 'logoUrl', '')}
                          disabled={!canEdit}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Remove
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Color Scheme */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="primaryColor">Primary Color</Label>
                  <div className="mt-2 flex items-center gap-3">
                    <Input
                      id="primaryColor"
                      type="color"
                      value={getCurrentValue('branding', 'primaryColor') || '#3B82F6'}
                      onChange={(e) => handleFieldChange('branding', 'primaryColor', e.target.value)}
                      disabled={!canEdit}
                      className="w-16 h-10 p-1 border rounded"
                    />
                    <Input
                      value={getCurrentValue('branding', 'primaryColor') || '#3B82F6'}
                      onChange={(e) => handleFieldChange('branding', 'primaryColor', e.target.value)}
                      disabled={!canEdit}
                      placeholder="#3B82F6"
                      className="flex-1"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="secondaryColor">Secondary Color</Label>
                  <div className="mt-2 flex items-center gap-3">
                    <Input
                      id="secondaryColor"
                      type="color"
                      value={getCurrentValue('branding', 'secondaryColor') || '#6B7280'}
                      onChange={(e) => handleFieldChange('branding', 'secondaryColor', e.target.value)}
                      disabled={!canEdit}
                      className="w-16 h-10 p-1 border rounded"
                    />
                    <Input
                      value={getCurrentValue('branding', 'secondaryColor') || '#6B7280'}
                      onChange={(e) => handleFieldChange('branding', 'secondaryColor', e.target.value)}
                      disabled={!canEdit}
                      placeholder="#6B7280"
                      className="flex-1"
                    />
                  </div>
                </div>
              </div>

              {/* Theme Settings */}
              <div>
                <Label className="text-sm font-medium">Theme Settings</Label>
                <div className="mt-3 space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="darkMode" className="text-sm font-medium">Dark Mode Support</Label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Enable dark mode for your storefront
                      </p>
                    </div>
                    <Switch
                      id="darkMode"
                      checked={getCurrentValue('branding', 'darkModeEnabled') || false}
                      onCheckedChange={(checked) => handleFieldChange('branding', 'darkModeEnabled', checked)}
                      disabled={!canEdit}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="customCSS" className="text-sm font-medium">Custom CSS</Label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Allow custom CSS modifications
                      </p>
                    </div>
                    <Switch
                      id="customCSS"
                      checked={getCurrentValue('branding', 'customCSSEnabled') || false}
                      onCheckedChange={(checked) => handleFieldChange('branding', 'customCSSEnabled', checked)}
                      disabled={!canEdit}
                    />
                  </div>
                </div>
              </div>

              {/* Custom CSS Editor */}
              {getCurrentValue('branding', 'customCSSEnabled') && (
                <div>
                  <Label htmlFor="customCSSCode">Custom CSS Code</Label>
                  <Textarea
                    id="customCSSCode"
                    value={getCurrentValue('branding', 'customCSS') || ''}
                    onChange={(e) => handleFieldChange('branding', 'customCSS', e.target.value)}
                    disabled={!canEdit}
                    placeholder="/* Add your custom CSS here */"
                    rows={8}
                    className="font-mono text-sm"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Custom CSS will be applied to your storefront. Use with caution.
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'seo' && (
            <div className="space-y-6">
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">SEO & Analytics</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Optimize your store for search engines and track performance</p>
              </div>

              <div className="grid grid-cols-1 gap-6">
                <div>
                  <Label htmlFor="metaTitle">Meta Title</Label>
                  <Input
                    id="metaTitle"
                    value={getCurrentValue('seo', 'metaTitle') || ''}
                    onChange={(e) => handleFieldChange('seo', 'metaTitle', e.target.value)}
                    disabled={!canEdit}
                    placeholder="Your Store - Best Products Online"
                  />
                  <p className="text-xs text-gray-500 mt-1">Recommended: 50-60 characters</p>
                </div>

                <div>
                  <Label htmlFor="metaDescription">Meta Description</Label>
                  <Textarea
                    id="metaDescription"
                    value={getCurrentValue('seo', 'metaDescription') || ''}
                    onChange={(e) => handleFieldChange('seo', 'metaDescription', e.target.value)}
                    disabled={!canEdit}
                    placeholder="Discover amazing products at great prices. Fast shipping and excellent customer service."
                    rows={3}
                  />
                  <p className="text-xs text-gray-500 mt-1">Recommended: 150-160 characters</p>
                </div>

                <div>
                  <Label htmlFor="metaKeywords">Meta Keywords</Label>
                  <Input
                    id="metaKeywords"
                    value={getCurrentValue('seo', 'metaKeywords') || ''}
                    onChange={(e) => handleFieldChange('seo', 'metaKeywords', e.target.value)}
                    disabled={!canEdit}
                    placeholder="ecommerce, online store, products, shopping"
                  />
                  <p className="text-xs text-gray-500 mt-1">Separate keywords with commas</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="googleAnalyticsId">Google Analytics ID</Label>
                  <Input
                    id="googleAnalyticsId"
                    value={getCurrentValue('seo', 'googleAnalyticsId') || ''}
                    onChange={(e) => handleFieldChange('seo', 'googleAnalyticsId', e.target.value)}
                    disabled={!canEdit}
                    placeholder="G-XXXXXXXXXX"
                  />
                </div>

                <div>
                  <Label htmlFor="facebookPixelId">Facebook Pixel ID</Label>
                  <Input
                    id="facebookPixelId"
                    value={getCurrentValue('seo', 'facebookPixelId') || ''}
                    onChange={(e) => handleFieldChange('seo', 'facebookPixelId', e.target.value)}
                    disabled={!canEdit}
                    placeholder="123456789012345"
                  />
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="sitemapEnabled" className="text-sm font-medium">Enable Sitemap</Label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Automatically generate XML sitemap for search engines
                    </p>
                  </div>
                  <Switch
                    id="sitemapEnabled"
                    checked={getCurrentValue('seo', 'sitemapEnabled') || false}
                    onCheckedChange={(checked) => handleFieldChange('seo', 'sitemapEnabled', checked)}
                    disabled={!canEdit}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="robotsTxt">Robots.txt Content</Label>
                <Textarea
                  id="robotsTxt"
                  value={getCurrentValue('seo', 'robotsTxt') || ''}
                  onChange={(e) => handleFieldChange('seo', 'robotsTxt', e.target.value)}
                  disabled={!canEdit}
                  placeholder="User-agent: *\nDisallow: /admin/\nSitemap: https://yourstore.com/sitemap.xml"
                  rows={6}
                  className="font-mono text-sm"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Custom robots.txt content. Leave empty to use default.
                </p>
              </div>
            </div>
          )}

          {activeTab === 'email' && (
            <div className="space-y-6">
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Email & Notifications</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Configure email settings and notifications</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="fromEmail">From Email</Label>
                  <Input
                    id="fromEmail"
                    type="email"
                    value={getCurrentValue('email', 'fromEmail') || ''}
                    onChange={(e) => handleFieldChange('email', 'fromEmail', e.target.value)}
                    disabled={!canEdit}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="fromName">From Name</Label>
                  <Input
                    id="fromName"
                    value={getCurrentValue('email', 'fromName') || ''}
                    onChange={(e) => handleFieldChange('email', 'fromName', e.target.value)}
                    disabled={!canEdit}
                    placeholder="Your Store Name"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="smtpHost">SMTP Host</Label>
                  <Input
                    id="smtpHost"
                    value={getCurrentValue('email', 'smtpHost') || ''}
                    onChange={(e) => handleFieldChange('email', 'smtpHost', e.target.value)}
                    disabled={!canEdit}
                    placeholder="smtp.gmail.com"
                  />
                </div>
                <div>
                  <Label htmlFor="smtpPort">SMTP Port</Label>
                  <Input
                    id="smtpPort"
                    type="number"
                    value={getCurrentValue('email', 'smtpPort') || '587'}
                    onChange={(e) => handleFieldChange('email', 'smtpPort', parseInt(e.target.value))}
                    disabled={!canEdit}
                    placeholder="587"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="smtpUsername">SMTP Username</Label>
                  <Input
                    id="smtpUsername"
                    value={getCurrentValue('email', 'smtpUsername') || ''}
                    onChange={(e) => handleFieldChange('email', 'smtpUsername', e.target.value)}
                    disabled={!canEdit}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="smtpPassword">SMTP Password</Label>
                  <Input
                    id="smtpPassword"
                    type="password"
                    value={getCurrentValue('email', 'smtpPassword') || ''}
                    onChange={(e) => handleFieldChange('email', 'smtpPassword', e.target.value)}
                    disabled={!canEdit}
                    placeholder="••••••••••••••••"
                  />
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableEmailNotifications" className="text-sm font-medium">Enable Email Notifications</Label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Send email notifications for orders, updates, etc.
                    </p>
                  </div>
                  <Switch
                    id="enableEmailNotifications"
                    checked={getCurrentValue('email', 'enableEmailNotifications') || false}
                    onCheckedChange={(checked) => handleFieldChange('email', 'enableEmailNotifications', checked)}
                    disabled={!canEdit}
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'behavior' && (
            <div className="space-y-6">
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Storefront Behavior</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Configure user experience and feature toggles</p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableWishlist" className="text-sm font-medium">Enable Wishlist</Label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Allow customers to save products to wishlist
                    </p>
                  </div>
                  <Switch
                    id="enableWishlist"
                    checked={getCurrentValue('behavior', 'enableWishlist') || false}
                    onCheckedChange={(checked) => handleFieldChange('behavior', 'enableWishlist', checked)}
                    disabled={!canEdit}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableReviews" className="text-sm font-medium">Enable Reviews</Label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Allow customers to write product reviews
                    </p>
                  </div>
                  <Switch
                    id="enableReviews"
                    checked={getCurrentValue('behavior', 'enableReviews') || false}
                    onCheckedChange={(checked) => handleFieldChange('behavior', 'enableReviews', checked)}
                    disabled={!canEdit}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableRatings" className="text-sm font-medium">Enable Ratings</Label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Allow customers to rate products
                    </p>
                  </div>
                  <Switch
                    id="enableRatings"
                    checked={getCurrentValue('behavior', 'enableRatings') || false}
                    onCheckedChange={(checked) => handleFieldChange('behavior', 'enableRatings', checked)}
                    disabled={!canEdit}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="autoApproveReviews" className="text-sm font-medium">Auto-approve Reviews</Label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Automatically approve customer reviews without moderation
                    </p>
                  </div>
                  <Switch
                    id="autoApproveReviews"
                    checked={getCurrentValue('behavior', 'autoApproveReviews') || false}
                    onCheckedChange={(checked) => handleFieldChange('behavior', 'autoApproveReviews', checked)}
                    disabled={!canEdit}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableInventoryTracking" className="text-sm font-medium">Enable Inventory Tracking</Label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Track product stock levels and show availability
                    </p>
                  </div>
                  <Switch
                    id="enableInventoryTracking"
                    checked={getCurrentValue('behavior', 'enableInventoryTracking') || false}
                    onCheckedChange={(checked) => handleFieldChange('behavior', 'enableInventoryTracking', checked)}
                    disabled={!canEdit}
                  />
                </div>
              </div>

              {getCurrentValue('behavior', 'enableInventoryTracking') && (
                <div>
                  <Label htmlFor="lowStockThreshold">Low Stock Threshold</Label>
                  <Input
                    id="lowStockThreshold"
                    type="number"
                    value={getCurrentValue('behavior', 'lowStockThreshold') || '10'}
                    onChange={(e) => handleFieldChange('behavior', 'lowStockThreshold', parseInt(e.target.value))}
                    disabled={!canEdit}
                    placeholder="10"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Show low stock warning when inventory falls below this number
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'auth' && (
            <div className="space-y-6">
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Authentication & Access</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Configure user authentication and security settings</p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="allowRegistration" className="text-sm font-medium">Allow Registration</Label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Allow new users to register accounts
                    </p>
                  </div>
                  <Switch
                    id="allowRegistration"
                    checked={getCurrentValue('auth', 'allowRegistration') || false}
                    onCheckedChange={(checked) => handleFieldChange('auth', 'allowRegistration', checked)}
                    disabled={!canEdit}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="requireEmailVerification" className="text-sm font-medium">Require Email Verification</Label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Users must verify their email before accessing the account
                    </p>
                  </div>
                  <Switch
                    id="requireEmailVerification"
                    checked={getCurrentValue('auth', 'requireEmailVerification') || false}
                    onCheckedChange={(checked) => handleFieldChange('auth', 'requireEmailVerification', checked)}
                    disabled={!canEdit}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableSocialLogin" className="text-sm font-medium">Enable Social Login</Label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Allow users to login with social media accounts
                    </p>
                  </div>
                  <Switch
                    id="enableSocialLogin"
                    checked={getCurrentValue('auth', 'enableSocialLogin') || false}
                    onCheckedChange={(checked) => handleFieldChange('auth', 'enableSocialLogin', checked)}
                    disabled={!canEdit}
                  />
                </div>

                {getCurrentValue('auth', 'enableSocialLogin') && (
                  <div className="ml-6 space-y-4 border-l-2 border-gray-200 dark:border-gray-700 pl-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="googleLoginEnabled" className="text-sm font-medium">Google Login</Label>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Enable Google OAuth login
                        </p>
                      </div>
                      <Switch
                        id="googleLoginEnabled"
                        checked={getCurrentValue('auth', 'googleLoginEnabled') || false}
                        onCheckedChange={(checked) => handleFieldChange('auth', 'googleLoginEnabled', checked)}
                        disabled={!canEdit}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="facebookLoginEnabled" className="text-sm font-medium">Facebook Login</Label>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Enable Facebook OAuth login
                        </p>
                      </div>
                      <Switch
                        id="facebookLoginEnabled"
                        checked={getCurrentValue('auth', 'facebookLoginEnabled') || false}
                        onCheckedChange={(checked) => handleFieldChange('auth', 'facebookLoginEnabled', checked)}
                        disabled={!canEdit}
                      />
                    </div>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableTwoFactor" className="text-sm font-medium">Two-Factor Authentication</Label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Enable 2FA for enhanced security
                    </p>
                  </div>
                  <Switch
                    id="enableTwoFactor"
                    checked={getCurrentValue('auth', 'enableTwoFactor') || false}
                    onCheckedChange={(checked) => handleFieldChange('auth', 'enableTwoFactor', checked)}
                    disabled={!canEdit}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="passwordMinLength">Minimum Password Length</Label>
                <Input
                  id="passwordMinLength"
                  type="number"
                  min="6"
                  max="50"
                  value={getCurrentValue('auth', 'passwordMinLength') || '8'}
                  onChange={(e) => handleFieldChange('auth', 'passwordMinLength', parseInt(e.target.value))}
                  disabled={!canEdit}
                  placeholder="8"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Minimum number of characters required for passwords (6-50)
                </p>
              </div>
            </div>
          )}

          {activeTab === 'payment' && (
            <div className="space-y-6">
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Payment Gateway</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Configure payment methods and gateways</p>
              </div>

              {/* Stripe Configuration */}
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h4 className="text-md font-medium text-gray-900 dark:text-gray-100">Stripe</h4>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Accept credit cards, debit cards, and digital wallets
                    </p>
                  </div>
                  <Switch
                    checked={getCurrentValue('payment', 'stripeEnabled') || false}
                    onCheckedChange={(checked) => handleFieldChange('payment', 'stripeEnabled', checked)}
                    disabled={!canEdit}
                  />
                </div>

                {getCurrentValue('payment', 'stripeEnabled') && (
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="stripePublishableKey">Publishable Key</Label>
                      <Input
                        id="stripePublishableKey"
                        value={getCurrentValue('payment', 'stripePublishableKey') || ''}
                        onChange={(e) => handleFieldChange('payment', 'stripePublishableKey', e.target.value)}
                        disabled={!canEdit}
                        placeholder="pk_test_..."
                      />
                    </div>
                    <div>
                      <Label htmlFor="stripeSecretKey">Secret Key</Label>
                      <Input
                        id="stripeSecretKey"
                        type="password"
                        value={getCurrentValue('payment', 'stripeSecretKey') || ''}
                        onChange={(e) => handleFieldChange('payment', 'stripeSecretKey', e.target.value)}
                        disabled={!canEdit}
                        placeholder="sk_test_..."
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* PayPal Configuration */}
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h4 className="text-md font-medium text-gray-900 dark:text-gray-100">PayPal</h4>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Accept PayPal payments and digital wallets
                    </p>
                  </div>
                  <Switch
                    checked={getCurrentValue('payment', 'paypalEnabled') || false}
                    onCheckedChange={(checked) => handleFieldChange('payment', 'paypalEnabled', checked)}
                    disabled={!canEdit}
                  />
                </div>

                {getCurrentValue('payment', 'paypalEnabled') && (
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="paypalClientId">Client ID</Label>
                      <Input
                        id="paypalClientId"
                        value={getCurrentValue('payment', 'paypalClientId') || ''}
                        onChange={(e) => handleFieldChange('payment', 'paypalClientId', e.target.value)}
                        disabled={!canEdit}
                        placeholder="AXxxx..."
                      />
                    </div>
                    <div>
                      <Label htmlFor="paypalClientSecret">Client Secret</Label>
                      <Input
                        id="paypalClientSecret"
                        type="password"
                        value={getCurrentValue('payment', 'paypalClientSecret') || ''}
                        onChange={(e) => handleFieldChange('payment', 'paypalClientSecret', e.target.value)}
                        disabled={!canEdit}
                        placeholder="EXxxx..."
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Razorpay Configuration */}
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h4 className="text-md font-medium text-gray-900 dark:text-gray-100">Razorpay</h4>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Accept payments in India with UPI, cards, and wallets
                    </p>
                  </div>
                  <Switch
                    checked={getCurrentValue('payment', 'razorpayEnabled') || false}
                    onCheckedChange={(checked) => handleFieldChange('payment', 'razorpayEnabled', checked)}
                    disabled={!canEdit}
                  />
                </div>

                {getCurrentValue('payment', 'razorpayEnabled') && (
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="razorpayKeyId">Key ID</Label>
                      <Input
                        id="razorpayKeyId"
                        value={getCurrentValue('payment', 'razorpayKeyId') || ''}
                        onChange={(e) => handleFieldChange('payment', 'razorpayKeyId', e.target.value)}
                        disabled={!canEdit}
                        placeholder="rzp_test_..."
                      />
                    </div>
                    <div>
                      <Label htmlFor="razorpayKeySecret">Key Secret</Label>
                      <Input
                        id="razorpayKeySecret"
                        type="password"
                        value={getCurrentValue('payment', 'razorpayKeySecret') || ''}
                        onChange={(e) => handleFieldChange('payment', 'razorpayKeySecret', e.target.value)}
                        disabled={!canEdit}
                        placeholder="xxx..."
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'shipping' && (
            <div className="space-y-6">
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Shipping Settings</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Configure delivery options and shipping zones</p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableShipping" className="text-sm font-medium">Enable Shipping</Label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Allow customers to select shipping options
                    </p>
                  </div>
                  <Switch
                    id="enableShipping"
                    checked={getCurrentValue('shipping', 'enableShipping') || false}
                    onCheckedChange={(checked) => handleFieldChange('shipping', 'enableShipping', checked)}
                    disabled={!canEdit}
                  />
                </div>

                {getCurrentValue('shipping', 'enableShipping') && (
                  <div className="space-y-6 border-l-2 border-gray-200 dark:border-gray-700 pl-4 ml-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="freeShippingThreshold">Free Shipping Threshold</Label>
                        <Input
                          id="freeShippingThreshold"
                          type="number"
                          min="0"
                          step="0.01"
                          value={getCurrentValue('shipping', 'freeShippingThreshold') || '100'}
                          onChange={(e) => handleFieldChange('shipping', 'freeShippingThreshold', parseFloat(e.target.value))}
                          disabled={!canEdit}
                          placeholder="100.00"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          Minimum order value for free shipping
                        </p>
                      </div>

                      <div>
                        <Label htmlFor="defaultShippingRate">Default Shipping Rate</Label>
                        <Input
                          id="defaultShippingRate"
                          type="number"
                          min="0"
                          step="0.01"
                          value={getCurrentValue('shipping', 'defaultShippingRate') || '10'}
                          onChange={(e) => handleFieldChange('shipping', 'defaultShippingRate', parseFloat(e.target.value))}
                          disabled={!canEdit}
                          placeholder="10.00"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          Standard shipping cost
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="enableLocalDelivery" className="text-sm font-medium">Enable Local Delivery</Label>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Offer local delivery within a specific radius
                        </p>
                      </div>
                      <Switch
                        id="enableLocalDelivery"
                        checked={getCurrentValue('shipping', 'enableLocalDelivery') || false}
                        onCheckedChange={(checked) => handleFieldChange('shipping', 'enableLocalDelivery', checked)}
                        disabled={!canEdit}
                      />
                    </div>

                    {getCurrentValue('shipping', 'enableLocalDelivery') && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 ml-6 border-l-2 border-gray-200 dark:border-gray-700 pl-4">
                        <div>
                          <Label htmlFor="localDeliveryRadius">Delivery Radius (km)</Label>
                          <Input
                            id="localDeliveryRadius"
                            type="number"
                            min="1"
                            value={getCurrentValue('shipping', 'localDeliveryRadius') || '10'}
                            onChange={(e) => handleFieldChange('shipping', 'localDeliveryRadius', parseInt(e.target.value))}
                            disabled={!canEdit}
                            placeholder="10"
                          />
                        </div>

                        <div>
                          <Label htmlFor="localDeliveryFee">Local Delivery Fee</Label>
                          <Input
                            id="localDeliveryFee"
                            type="number"
                            min="0"
                            step="0.01"
                            value={getCurrentValue('shipping', 'localDeliveryFee') || '5'}
                            onChange={(e) => handleFieldChange('shipping', 'localDeliveryFee', parseFloat(e.target.value))}
                            disabled={!canEdit}
                            placeholder="5.00"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'tax' && (
            <div className="space-y-6">
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Tax Settings</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Configure tax rates and regional tax rules</p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableTax" className="text-sm font-medium">Enable Tax Calculation</Label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Apply tax to orders and products
                    </p>
                  </div>
                  <Switch
                    id="enableTax"
                    checked={getCurrentValue('tax', 'enableTax') || false}
                    onCheckedChange={(checked) => handleFieldChange('tax', 'enableTax', checked)}
                    disabled={!canEdit}
                  />
                </div>

                {getCurrentValue('tax', 'enableTax') && (
                  <div className="space-y-6 border-l-2 border-gray-200 dark:border-gray-700 pl-4 ml-6">
                    <div>
                      <Label htmlFor="defaultTaxRate">Default Tax Rate (%)</Label>
                      <Input
                        id="defaultTaxRate"
                        type="number"
                        min="0"
                        max="100"
                        step="0.01"
                        value={getCurrentValue('tax', 'defaultTaxRate') || '0'}
                        onChange={(e) => handleFieldChange('tax', 'defaultTaxRate', parseFloat(e.target.value))}
                        disabled={!canEdit}
                        placeholder="8.25"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Default tax rate applied to products (e.g., 8.25 for 8.25%)
                      </p>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="taxIncluded" className="text-sm font-medium">Tax Included in Prices</Label>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Product prices already include tax
                        </p>
                      </div>
                      <Switch
                        id="taxIncluded"
                        checked={getCurrentValue('tax', 'taxIncluded') || false}
                        onCheckedChange={(checked) => handleFieldChange('tax', 'taxIncluded', checked)}
                        disabled={!canEdit}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="enableRegionalTax" className="text-sm font-medium">Enable Regional Tax</Label>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Apply different tax rates based on customer location
                        </p>
                      </div>
                      <Switch
                        id="enableRegionalTax"
                        checked={getCurrentValue('tax', 'enableRegionalTax') || false}
                        onCheckedChange={(checked) => handleFieldChange('tax', 'enableRegionalTax', checked)}
                        disabled={!canEdit}
                      />
                    </div>

                    {getCurrentValue('tax', 'enableRegionalTax') && (
                      <div className="ml-6 border-l-2 border-gray-200 dark:border-gray-700 pl-4">
                        <p className="text-sm text-amber-600 dark:text-amber-400">
                          Regional tax configuration will be available in advanced tax settings.
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'compliance' && (
            <div className="space-y-6">
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Compliance & Legal</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Manage legal documents and compliance settings</p>
              </div>

              <div className="space-y-6">
                <div>
                  <Label htmlFor="privacyPolicyUrl">Privacy Policy URL</Label>
                  <Input
                    id="privacyPolicyUrl"
                    type="url"
                    value={getCurrentValue('compliance', 'privacyPolicyUrl') || ''}
                    onChange={(e) => handleFieldChange('compliance', 'privacyPolicyUrl', e.target.value)}
                    disabled={!canEdit}
                    placeholder="https://yourstore.com/privacy-policy"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Link to your privacy policy page
                  </p>
                </div>

                <div>
                  <Label htmlFor="termsOfServiceUrl">Terms of Service URL</Label>
                  <Input
                    id="termsOfServiceUrl"
                    type="url"
                    value={getCurrentValue('compliance', 'termsOfServiceUrl') || ''}
                    onChange={(e) => handleFieldChange('compliance', 'termsOfServiceUrl', e.target.value)}
                    disabled={!canEdit}
                    placeholder="https://yourstore.com/terms-of-service"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Link to your terms of service page
                  </p>
                </div>

                <div>
                  <Label htmlFor="cookiePolicyUrl">Cookie Policy URL</Label>
                  <Input
                    id="cookiePolicyUrl"
                    type="url"
                    value={getCurrentValue('compliance', 'cookiePolicyUrl') || ''}
                    onChange={(e) => handleFieldChange('compliance', 'cookiePolicyUrl', e.target.value)}
                    disabled={!canEdit}
                    placeholder="https://yourstore.com/cookie-policy"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Link to your cookie policy page
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="gdprEnabled" className="text-sm font-medium">GDPR Compliance</Label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Enable GDPR compliance features for EU customers
                      </p>
                    </div>
                    <Switch
                      id="gdprEnabled"
                      checked={getCurrentValue('compliance', 'gdprEnabled') || false}
                      onCheckedChange={(checked) => handleFieldChange('compliance', 'gdprEnabled', checked)}
                      disabled={!canEdit}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="cookieConsentEnabled" className="text-sm font-medium">Cookie Consent Banner</Label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Show cookie consent banner to visitors
                      </p>
                    </div>
                    <Switch
                      id="cookieConsentEnabled"
                      checked={getCurrentValue('compliance', 'cookieConsentEnabled') || false}
                      onCheckedChange={(checked) => handleFieldChange('compliance', 'cookieConsentEnabled', checked)}
                      disabled={!canEdit}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="dataRetentionDays">Data Retention Period (Days)</Label>
                  <Input
                    id="dataRetentionDays"
                    type="number"
                    min="30"
                    max="3650"
                    value={getCurrentValue('compliance', 'dataRetentionDays') || '365'}
                    onChange={(e) => handleFieldChange('compliance', 'dataRetentionDays', parseInt(e.target.value))}
                    disabled={!canEdit}
                    placeholder="365"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    How long to retain customer data (30-3650 days)
                  </p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'panel-preferences' && (
            <div className="space-y-6">
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Panel Preferences</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Customize admin panel settings and preferences</p>
              </div>

              <div className="space-y-6">
                <div>
                  <Label htmlFor="theme">Admin Panel Theme</Label>
                  <Select
                    value={getCurrentValue('panelPreferences', 'theme') || 'system'}
                    onValueChange={(value) => handleFieldChange('panelPreferences', 'theme', value)}
                    disabled={!canEdit}
                    options={[
                      { value: 'light', label: 'Light' },
                      { value: 'dark', label: 'Dark' },
                      { value: 'system', label: 'System (Auto)' }
                    ]}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Choose the appearance theme for the admin panel
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="sidebarCollapsed" className="text-sm font-medium">Collapsed Sidebar</Label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Start with sidebar collapsed by default
                      </p>
                    </div>
                    <Switch
                      id="sidebarCollapsed"
                      checked={getCurrentValue('panelPreferences', 'sidebarCollapsed') || false}
                      onCheckedChange={(checked) => handleFieldChange('panelPreferences', 'sidebarCollapsed', checked)}
                      disabled={!canEdit}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="enableNotifications" className="text-sm font-medium">Enable Notifications</Label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Show desktop notifications for important events
                      </p>
                    </div>
                    <Switch
                      id="enableNotifications"
                      checked={getCurrentValue('panelPreferences', 'enableNotifications') || false}
                      onCheckedChange={(checked) => handleFieldChange('panelPreferences', 'enableNotifications', checked)}
                      disabled={!canEdit}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="enableSounds" className="text-sm font-medium">Enable Sounds</Label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Play sound effects for notifications and actions
                      </p>
                    </div>
                    <Switch
                      id="enableSounds"
                      checked={getCurrentValue('panelPreferences', 'enableSounds') || false}
                      onCheckedChange={(checked) => handleFieldChange('panelPreferences', 'enableSounds', checked)}
                      disabled={!canEdit}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="autoSave" className="text-sm font-medium">Auto-save</Label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Automatically save changes as you type
                      </p>
                    </div>
                    <Switch
                      id="autoSave"
                      checked={getCurrentValue('panelPreferences', 'autoSave') || false}
                      onCheckedChange={(checked) => handleFieldChange('panelPreferences', 'autoSave', checked)}
                      disabled={!canEdit}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="itemsPerPage">Items Per Page</Label>
                  <Select
                    value={getCurrentValue('panelPreferences', 'itemsPerPage')?.toString() || '20'}
                    onValueChange={(value) => handleFieldChange('panelPreferences', 'itemsPerPage', parseInt(value))}
                    disabled={!canEdit}
                    options={[
                      { value: '10', label: '10 items' },
                      { value: '20', label: '20 items' },
                      { value: '50', label: '50 items' },
                      { value: '100', label: '100 items' }
                    ]}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Number of items to display per page in lists
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Other tabs show placeholder content */}
          {!['general', 'branding', 'seo', 'email', 'behavior', 'auth', 'payment', 'shipping', 'tax', 'compliance', 'panel-preferences'].includes(activeTab) && (
            <div className="space-y-6">
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  {tabs.find(t => t.id === activeTab)?.label} Settings
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Configure {tabs.find(t => t.id === activeTab)?.label.toLowerCase()} settings
                </p>
              </div>
              <div className="text-center text-gray-500 py-12">
                {tabs.find(t => t.id === activeTab)?.label} settings coming soon...
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
