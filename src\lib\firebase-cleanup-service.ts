import {
  collection,
  getDocs,
  deleteDoc,
  doc,
  writeBatch,
  query,
  where,
  connectFirestoreEmulator,
  terminate
} from 'firebase/firestore'
import { deleteUser, getAuth } from 'firebase/auth'
import { ref, listAll, deleteObject } from 'firebase/storage'
import { db, storage } from './firebase'
import { toast } from 'react-hot-toast'

export interface CleanupStats {
  stores: number
  users: number
  storeRequests: number
  products: number
  orders: number
  customers: number
  categories: number
  storageFiles: number
}

export class FirebaseCleanupService {
  
  /**
   * Get statistics of data to be cleaned
   */
  async getCleanupStats(): Promise<CleanupStats> {
    try {
      const stats: CleanupStats = {
        stores: 0,
        users: 0,
        storeRequests: 0,
        products: 0,
        orders: 0,
        customers: 0,
        categories: 0,
        storageFiles: 0
      }

      // Count stores
      const storesSnapshot = await getDocs(collection(db, 'stores'))
      stats.stores = storesSnapshot.size

      // Count users
      const usersSnapshot = await getDocs(collection(db, 'users'))
      stats.users = usersSnapshot.size

      // Count store requests
      const requestsSnapshot = await getDocs(collection(db, 'store-requests'))
      stats.storeRequests = requestsSnapshot.size

      // Count products (across all stores)
      let totalProducts = 0
      for (const storeDoc of storesSnapshot.docs) {
        const productsSnapshot = await getDocs(collection(db, 'stores', storeDoc.id, 'products'))
        totalProducts += productsSnapshot.size
      }
      stats.products = totalProducts

      // Count orders (across all stores)
      let totalOrders = 0
      for (const storeDoc of storesSnapshot.docs) {
        const ordersSnapshot = await getDocs(collection(db, 'stores', storeDoc.id, 'orders'))
        totalOrders += ordersSnapshot.size
      }
      stats.orders = totalOrders

      // Count customers
      const customersSnapshot = await getDocs(collection(db, 'customers'))
      stats.customers = customersSnapshot.size

      // Count categories (across all stores)
      let totalCategories = 0
      for (const storeDoc of storesSnapshot.docs) {
        const categoriesSnapshot = await getDocs(collection(db, 'stores', storeDoc.id, 'categories'))
        totalCategories += categoriesSnapshot.size
      }
      stats.categories = totalCategories

      // Count storage files
      try {
        const storageRef = ref(storage, '/')
        const storageList = await listAll(storageRef)
        stats.storageFiles = storageList.items.length
      } catch (error) {
        console.warn('Could not count storage files:', error)
        stats.storageFiles = 0
      }

      return stats
    } catch (error) {
      console.error('Error getting cleanup stats:', error)
      throw new Error('Failed to get cleanup statistics')
    }
  }

  /**
   * Clean all store data (products, orders, categories, users)
   */
  async cleanStoreData(): Promise<void> {
    try {
      const batch = writeBatch(db)
      let operationCount = 0

      // Get all stores
      const storesSnapshot = await getDocs(collection(db, 'stores'))
      
      for (const storeDoc of storesSnapshot.docs) {
        const storeId = storeDoc.id

        // Delete products
        const productsSnapshot = await getDocs(collection(db, 'stores', storeId, 'products'))
        for (const productDoc of productsSnapshot.docs) {
          batch.delete(doc(db, 'stores', storeId, 'products', productDoc.id))
          operationCount++
          
          // Commit batch if it gets too large
          if (operationCount >= 450) {
            await batch.commit()
            operationCount = 0
          }
        }

        // Delete orders
        const ordersSnapshot = await getDocs(collection(db, 'stores', storeId, 'orders'))
        for (const orderDoc of ordersSnapshot.docs) {
          batch.delete(doc(db, 'stores', storeId, 'orders', orderDoc.id))
          operationCount++
          
          if (operationCount >= 450) {
            await batch.commit()
            operationCount = 0
          }
        }

        // Delete categories
        const categoriesSnapshot = await getDocs(collection(db, 'stores', storeId, 'categories'))
        for (const categoryDoc of categoriesSnapshot.docs) {
          batch.delete(doc(db, 'stores', storeId, 'categories', categoryDoc.id))
          operationCount++
          
          if (operationCount >= 450) {
            await batch.commit()
            operationCount = 0
          }
        }

        // Delete store users
        const storeUsersSnapshot = await getDocs(collection(db, 'stores', storeId, 'users'))
        for (const userDoc of storeUsersSnapshot.docs) {
          batch.delete(doc(db, 'stores', storeId, 'users', userDoc.id))
          operationCount++
          
          if (operationCount >= 450) {
            await batch.commit()
            operationCount = 0
          }
        }

        // Delete the store itself
        batch.delete(doc(db, 'stores', storeId))
        operationCount++
      }

      // Commit remaining operations
      if (operationCount > 0) {
        await batch.commit()
      }

      toast.success('Store data cleaned successfully')
    } catch (error) {
      console.error('Error cleaning store data:', error)
      throw new Error('Failed to clean store data')
    }
  }

  /**
   * Clean all user data
   */
  async cleanUserData(): Promise<void> {
    try {
      const batch = writeBatch(db)
      let operationCount = 0

      // Delete all users from Firestore
      const usersSnapshot = await getDocs(collection(db, 'users'))
      for (const userDoc of usersSnapshot.docs) {
        batch.delete(doc(db, 'users', userDoc.id))
        operationCount++
        
        if (operationCount >= 450) {
          await batch.commit()
          operationCount = 0
        }
      }

      // Delete all customers
      const customersSnapshot = await getDocs(collection(db, 'customers'))
      for (const customerDoc of customersSnapshot.docs) {
        batch.delete(doc(db, 'customers', customerDoc.id))
        operationCount++
        
        if (operationCount >= 450) {
          await batch.commit()
          operationCount = 0
        }
      }

      // Commit remaining operations
      if (operationCount > 0) {
        await batch.commit()
      }

      toast.success('User data cleaned successfully')
    } catch (error) {
      console.error('Error cleaning user data:', error)
      throw new Error('Failed to clean user data')
    }
  }

  /**
   * Clean store registration requests
   */
  async cleanStoreRequests(): Promise<void> {
    try {
      const batch = writeBatch(db)
      let operationCount = 0

      const requestsSnapshot = await getDocs(collection(db, 'store-requests'))
      for (const requestDoc of requestsSnapshot.docs) {
        batch.delete(doc(db, 'store-requests', requestDoc.id))
        operationCount++
        
        if (operationCount >= 450) {
          await batch.commit()
          operationCount = 0
        }
      }

      if (operationCount > 0) {
        await batch.commit()
      }

      toast.success('Store requests cleaned successfully')
    } catch (error) {
      console.error('Error cleaning store requests:', error)
      throw new Error('Failed to clean store requests')
    }
  }

  /**
   * Clean storage files
   */
  async cleanStorageFiles(): Promise<void> {
    try {
      const storageRef = ref(storage, '/')
      const storageList = await listAll(storageRef)
      
      const deletePromises = storageList.items.map(item => deleteObject(item))
      await Promise.all(deletePromises)

      toast.success('Storage files cleaned successfully')
    } catch (error) {
      console.error('Error cleaning storage files:', error)
      throw new Error('Failed to clean storage files')
    }
  }

  /**
   * Clean all data (nuclear option)
   */
  async cleanAllData(): Promise<void> {
    try {
      await Promise.all([
        this.cleanStoreData(),
        this.cleanUserData(),
        this.cleanStoreRequests(),
        this.cleanStorageFiles()
      ])

      toast.success('All Firebase data cleaned successfully')
    } catch (error) {
      console.error('Error cleaning all data:', error)
      throw new Error('Failed to clean all data')
    }
  }
}

export const firebaseCleanupService = new FirebaseCleanupService()
