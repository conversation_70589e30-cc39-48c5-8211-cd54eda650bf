import { useMemo } from 'react'
import React from 'react'
import { useFirebaseAuthStore } from '../store/firebase-auth'

export interface UserPermissions {
  canManageProducts: boolean
  canManageOrders: boolean
  canManageCustomers: boolean
  canManageUsers: boolean
  canViewAnalytics: boolean
  canManageSettings: boolean
  canManageMarketing: boolean
  canExportData: boolean
}

// Role-based permissions according to STORE_REGISTRATION_SYSTEM.md requirements
const ROLE_PERMISSIONS: Record<string, UserPermissions> = {
  super_admin: {
    canManageProducts: true,      // ✅ Full CRUD
    canManageOrders: true,        // ✅ Full management
    canManageCustomers: true,     // ✅ Full management
    canManageUsers: true,         // ✅ Staff Management
    canViewAnalytics: true,       // ✅ Analytics access
    canManageSettings: true,      // ✅ Store Settings
    canManageMarketing: true,     // ✅ Promotions/Marketing
    canExportData: true          // ✅ File Storage
  },
  admin: {
    canManageProducts: true,      // ✅ Full CRUD
    canManageOrders: true,        // ✅ Full management
    canManageCustomers: true,     // ✅ Full management
    canManageUsers: false,        // ❌ Cannot manage staff
    canViewAnalytics: true,       // ✅ Analytics access
    canManageSettings: false,     // ❌ Cannot change store settings
    canManageMarketing: true,     // ✅ Promotions/Marketing
    canExportData: true          // ✅ File Storage
  },
  editor: {
    canManageProducts: true,      // ✅ Can edit products
    canManageOrders: true,        // ✅ Partial (cannot fulfill/cancel)
    canManageCustomers: true,     // ✅ Limited (cannot segment/assign groups)
    canManageUsers: false,        // ❌ No staff access
    canViewAnalytics: false,      // ❌ No analytics
    canManageSettings: false,     // ❌ No settings
    canManageMarketing: false,    // ❌ Cannot publish promotions
    canExportData: true          // ✅ File Storage
  },
  viewer: {
    canManageProducts: false,     // 🔍 View only
    canManageOrders: false,       // 🔍 View only
    canManageCustomers: false,    // 🔍 View only
    canManageUsers: false,        // ❌ No access
    canViewAnalytics: false,      // ❌ No analytics
    canManageSettings: false,     // ❌ No settings
    canManageMarketing: false,    // ❌ No marketing
    canExportData: false         // ❌ No file storage
  }
}

function getUserPermissions(role: string): UserPermissions {
  return ROLE_PERMISSIONS[role] || ROLE_PERMISSIONS.viewer
}

function hasUserPermission(user: any, permission: keyof UserPermissions): boolean {
  if (!user || !user.active) return false
  const permissions = getUserPermissions(user.role)
  return permissions[permission]
}

export function usePermissions() {
  const { user } = useFirebaseAuthStore()

  const permissions = useMemo(() => {
    if (!user) {
      return {
        canManageProducts: false,
        canManageOrders: false,
        canManageCustomers: false,
        canManageUsers: false,
        canViewAnalytics: false,
        canManageSettings: false,
        canManageMarketing: false,
        canExportData: false
      } as UserPermissions
    }

    return getUserPermissions(user.role)
  }, [user])

  const hasPermission = (permission: keyof UserPermissions): boolean => {
    return hasUserPermission(user, permission)
  }

  const hasAnyPermission = (permissionList: (keyof UserPermissions)[]): boolean => {
    return permissionList.some(permission => hasPermission(permission))
  }

  const hasAllPermissions = (permissionList: (keyof UserPermissions)[]): boolean => {
    return permissionList.every(permission => hasPermission(permission))
  }

  const canAccess = {
    // Page-level permissions
    dashboard: hasPermission('canViewAnalytics') || hasAnyPermission(['canManageProducts', 'canManageOrders']),
    products: hasPermission('canManageProducts'),
    orders: hasPermission('canManageOrders'),
    customers: hasPermission('canManageCustomers'),
    users: hasPermission('canManageUsers'),
    analytics: hasPermission('canViewAnalytics'),
    settings: hasPermission('canManageSettings'),
    marketing: hasPermission('canManageMarketing'),
    
    // Action-level permissions
    createProduct: hasPermission('canManageProducts'),
    editProduct: hasPermission('canManageProducts'),
    deleteProduct: hasPermission('canManageProducts'),
    
    createOrder: hasPermission('canManageOrders'),
    editOrder: hasPermission('canManageOrders'),
    deleteOrder: hasPermission('canManageOrders'),
    
    createCustomer: hasPermission('canManageCustomers'),
    editCustomer: hasPermission('canManageCustomers'),
    deleteCustomer: hasPermission('canManageCustomers'),
    
    createUser: hasPermission('canManageUsers'),
    editUser: hasPermission('canManageUsers'),
    deleteUser: hasPermission('canManageUsers'),
    
    exportData: hasPermission('canExportData'),
    
    // Settings permissions
    editStoreSettings: hasPermission('canManageSettings'),
    editPaymentSettings: hasPermission('canManageSettings'),
    editShippingSettings: hasPermission('canManageSettings'),
    
    // Marketing permissions
    createCampaign: hasPermission('canManageMarketing'),
    editCampaign: hasPermission('canManageMarketing'),
    deleteCampaign: hasPermission('canManageMarketing')
  }

  return {
    permissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canAccess,
    user,
    isAdmin: user?.role === 'admin' || user?.role === 'super_admin',
    isSuperAdmin: user?.role === 'super_admin'
  }
}

// Permission-based component wrapper
interface PermissionGuardProps {
  permission?: keyof UserPermissions
  permissions?: (keyof UserPermissions)[]
  requireAll?: boolean
  fallback?: React.ReactNode
  children: React.ReactNode
}

export function PermissionGuard({ 
  permission, 
  permissions, 
  requireAll = false, 
  fallback = null, 
  children 
}: PermissionGuardProps) {
  const { hasPermission, hasAnyPermission, hasAllPermissions } = usePermissions()

  let hasAccess = false

  if (permission) {
    hasAccess = hasPermission(permission)
  } else if (permissions) {
    hasAccess = requireAll 
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions)
  }

  if (!hasAccess) {
    return React.createElement(React.Fragment, null, fallback)
  }

  return React.createElement(React.Fragment, null, children)
}

// Role-based component wrapper
interface RoleGuardProps {
  roles: string[]
  fallback?: React.ReactNode
  children: React.ReactNode
}

export function RoleGuard({ roles, fallback = null, children }: RoleGuardProps) {
  const { user } = usePermissions()

  if (!user || !roles.includes(user.role)) {
    return React.createElement(React.Fragment, null, fallback)
  }

  return React.createElement(React.Fragment, null, children)
}
