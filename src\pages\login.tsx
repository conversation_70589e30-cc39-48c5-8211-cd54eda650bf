import { useState, useEffect } from 'react'
import { Navigate, Link, useNavigate } from 'react-router-dom'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { appAdminService } from '../lib/app-admin-service'
import { toast } from 'react-hot-toast'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import { cn } from '../lib/utils'
import {
  Eye,
  EyeOff,
  ShoppingBag,
  Shield,
  Zap,
  BarChart3,
  Users,
  Package,
  Loader2
} from 'lucide-react'

export function LoginPage() {
  const navigate = useNavigate()
  const { user, signIn, loading, error, clearError } = useFirebaseAuthStore()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [rememberMe, setRememberMe] = useState(false)
  const [isClearing, setIsClearing] = useState(false)
  const [localError, setLocalError] = useState('')
  const [successMessage, setSuccessMessage] = useState('')
  const [isAppAdminLogin, setIsAppAdminLogin] = useState(false)

  // Clear errors when user starts typing
  useEffect(() => {
    if ((error || localError) && (email || password)) {
      clearError()
      setLocalError('')
      setSuccessMessage('')
    }
  }, [email, password, error, localError, clearError])

  // Redirect if already authenticated
  if (user) {
    return <Navigate to="/admin/dashboard" replace />
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLocalError('')
    setSuccessMessage('')

    // Check if this is app admin login
    if (email === '<EMAIL>') {
      try {
        const appAdmin = await appAdminService.authenticateAppAdmin(email, password)
        localStorage.setItem('app_admin_session', JSON.stringify(appAdmin))
        toast.success('App admin login successful!')
        navigate('/app-admin')
        return
      } catch (error: any) {
        setLocalError(error.message || 'App admin authentication failed')
        return
      }
    }

    // Network connectivity check removed - handled by Firebase

    // Basic validation
    if (!email || !password) {
      setLocalError('Please enter both email and password')
      return
    }

    if (!email.includes('@')) {
      setLocalError('Please enter a valid email address')
      return
    }

    try {
      const result = await signIn(email, password)

      if (result.error) {
        // Handle specific error types
        if (result.error.includes('Invalid login credentials')) {
          setLocalError('Invalid email or password. Please check your credentials and try again.')
        } else if (result.error.includes('Email not confirmed')) {
          setLocalError('Please check your email and click the confirmation link before signing in.')
        } else if (result.error.includes('Too many requests')) {
          setLocalError('Too many login attempts. Please wait a few minutes and try again.')
        } else if (result.error.includes('User not found')) {
          setLocalError('No account found with this email address. Please contact your administrator.')
        } else {
          setLocalError(result.error)
        }
      } else {
        setSuccessMessage('Login successful! Redirecting...')
        // The redirect will happen automatically via the auth store
      }
    } catch (error) {
      console.error('Login error:', error)
      setLocalError('An unexpected error occurred. Please try again.')
    }
  }

  const handleClearSession = async () => {
    setIsClearing(true)
    try {
      console.log('🧹 Manually clearing session data...')
      await signOut()
      // Clear any cached data
      if ('caches' in window) {
        const cacheNames = await caches.keys()
        await Promise.all(cacheNames.map(name => caches.delete(name)))
      }
      // Force reload to ensure clean state
      window.location.reload()
    } catch (error) {
      console.error('Error clearing session:', error)
      setIsClearing(false)
    }
  }

  const features = [
    {
      icon: BarChart3,
      title: 'Analytics Dashboard',
      description: 'Real-time insights and performance metrics'
    },
    {
      icon: Package,
      title: 'Inventory Management',
      description: 'Track products, stock levels, and variants'
    },
    {
      icon: Users,
      title: 'Customer Management',
      description: 'Manage customers, orders, and relationships'
    },
    {
      icon: Shield,
      title: 'Secure & Reliable',
      description: 'Enterprise-grade security and uptime'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-2 gap-8 items-center min-h-screen">
          {/* Left Side - Branding & Features */}
          <div className="hidden lg:block space-y-8">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
                  <ShoppingBag className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Womanza
                  </h1>
                  <p className="text-gray-500">Admin Dashboard</p>
                </div>
              </div>
              <div className="space-y-2">
                <h2 className="text-4xl font-bold text-gray-900">
                  Manage your store with confidence
                </h2>
                <p className="text-xl text-gray-500">
                  Powerful e-commerce management tools designed for modern businesses
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-6">
              {features.map((feature, index) => (
                <div key={index} className="space-y-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <feature.icon className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{feature.title}</h3>
                    <p className="text-sm text-gray-500">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className="flex items-center gap-4">
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                <Zap className="h-3 w-3 mr-1" />
                Fast & Reliable
              </Badge>
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                <Shield className="h-3 w-3 mr-1" />
                Secure
              </Badge>
            </div>
          </div>

          {/* Right Side - Login Form */}
          <div className="flex items-center justify-center">
            <Card className="w-full max-w-md shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="text-center space-y-4 pb-8">
                <div className="flex lg:hidden items-center justify-center gap-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
                    <ShoppingBag className="h-5 w-5 text-white" />
                  </div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Womanza
                  </h1>
                </div>
                <div>
                  <CardTitle className="text-2xl font-bold text-gray-900">Welcome back</CardTitle>
                  <CardDescription className="text-base">
                    Sign in to access your admin dashboard
                  </CardDescription>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <form onSubmit={handleSubmit} className="space-y-5">
                  <div className="space-y-2">
                    <label htmlFor="email" className="text-sm font-medium text-gray-700">
                      Email Address
                    </label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      disabled={loading}
                      className="h-11"
                    />
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="password" className="text-sm font-medium text-gray-700">
                      Password
                    </label>
                    <div className="relative">
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        placeholder="Enter your password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        required
                        disabled={loading}
                        className="h-11 pr-10"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        disabled={loading}
                      >
                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <input
                        id="remember"
                        type="checkbox"
                        checked={rememberMe}
                        onChange={(e) => setRememberMe(e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        disabled={loading}
                      />
                      <label htmlFor="remember" className="text-sm text-gray-600">
                        Remember me
                      </label>
                    </div>
                    <button
                      type="button"
                      className="text-sm text-blue-600 font-medium hover:text-blue-500"
                      disabled={loading}
                    >
                      Forgot password?
                    </button>
                  </div>

                  {error && (
                    <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-3">
                      {error}
                    </div>
                  )}

                  {/* Error Message Display */}
                  {(error || localError) && (
                    <div className="p-3 rounded-lg bg-red-50 border border-red-200">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm text-red-800">
                            {localError || error}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Success Message Display */}
                  {successMessage && (
                    <div className="p-3 rounded-lg bg-green-50 border border-green-200">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm text-green-800">
                            {successMessage}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  <Button
                    type="submit"
                    className="w-full h-11 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium hover:from-blue-700 hover:to-purple-700"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Signing in...
                      </>
                    ) : (
                      'Sign In'
                    )}
                  </Button>
                </form>



                {/* Clear Session Button - Show if there are auth errors */}
                {(error || localError) && (
                  <div className="text-center">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleClearSession}
                      disabled={isClearing}
                      className="text-xs"
                    >
                      {isClearing ? (
                        <>
                          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                          Clearing...
                        </>
                      ) : (
                        'Clear Session Data'
                      )}
                    </Button>
                    <p className="text-xs text-gray-400 mt-1">
                      Try this if you're having login issues
                    </p>
                  </div>
                )}

                {/* Registration Link */}
                <div className="text-center">
                  <p className="text-sm text-gray-500">
                    Don't have a store yet?{' '}
                    <Link to="/register" className="text-accent-600 font-medium hover:text-accent-500">
                      Register your store
                    </Link>
                  </p>
                </div>

                {/* Support Info */}
                <div className="text-center">
                  <p className="text-sm text-gray-500">
                    Need help? Contact{' '}
                    <a href="mailto:<EMAIL>" className="text-accent-600 font-medium hover:text-accent-500">
                      <EMAIL>
                    </a>
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
