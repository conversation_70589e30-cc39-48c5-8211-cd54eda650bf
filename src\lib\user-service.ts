import {
  createUserWithEmailAndPassword,
  updateProfile,
  sendPasswordResetEmail,
  getAuth,
  signOut
} from 'firebase/auth'
import {
  doc,
  setDoc,
  updateDoc,
  deleteDoc,
  serverTimestamp,
  collection,
  query,
  where,
  getDocs,
  getDoc
} from 'firebase/firestore'
import { initializeApp } from 'firebase/app'
import { auth, db, type AdminUser, app } from './firebase'
import { activityLogger } from './activity-logger'

export interface CreateUserData {
  email: string
  password: string
  fullName: string
  role: 'admin' | 'editor' | 'viewer'
}

export interface UpdateUserData {
  fullName?: string
  role?: 'admin' | 'editor' | 'viewer'
  active?: boolean
}

export class UserService {
  private storeId = import.meta.env.VITE_STORE_ID || 'womanza'
  private secondaryAuth: any = null

  /**
   * Get or create secondary auth instance for user creation
   */
  private getSecondaryAuth() {
    if (!this.secondaryAuth) {
      try {
        const secondaryApp = initializeApp({
          apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
          authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
          projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
          storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
          messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
          appId: import.meta.env.VITE_FIREBASE_APP_ID
        }, 'secondary-' + Date.now())

        this.secondaryAuth = getAuth(secondaryApp)
      } catch (error) {
        console.error('Error creating secondary auth:', error)
        // Fallback to primary auth if secondary fails
        this.secondaryAuth = auth
      }
    }
    return this.secondaryAuth
  }

  /**
   * Create a new admin user with Firebase Auth and Firestore
   */
  async createUser(userData: CreateUserData, createdBy: string, targetStoreId?: string): Promise<AdminUser> {
    const storeId = targetStoreId || this.storeId
    try {
      // Get secondary auth instance to avoid signing out the current admin
      const secondaryAuth = this.getSecondaryAuth()

      // Create Firebase Auth user using secondary auth
      const userCredential = await createUserWithEmailAndPassword(
        secondaryAuth,
        userData.email,
        userData.password
      )

      const firebaseUser = userCredential.user

      // Update Firebase Auth profile
      await updateProfile(firebaseUser, {
        displayName: userData.fullName
      })

      // Sign out from secondary auth to avoid conflicts
      if (secondaryAuth !== auth) {
        await signOut(secondaryAuth)
      }

      // Create admin user document in Firestore
      const adminUser: AdminUser = {
        id: firebaseUser.uid,
        uid: firebaseUser.uid,
        email: userData.email,
        name: userData.fullName,
        fullName: userData.fullName,
        role: userData.role,
        storeId: storeId,
        primaryStoreId: storeId,
        storeIds: [storeId],
        active: true,
        createdBy,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastLoginAt: null,
        avatarUrl: null,
        profile: {
          firstName: userData.fullName.split(' ')[0] || userData.fullName,
          lastName: userData.fullName.split(' ').slice(1).join(' ') || '',
          phone: '',
          avatar: ''
        }
      }

      // Create user in global users collection (for authentication)
      await setDoc(doc(db, 'users', firebaseUser.uid), {
        ...adminUser,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      })

      // Create user in store-specific users collection (for store management)
      await setDoc(doc(db, 'stores', storeId, 'users', firebaseUser.uid), {
        ...adminUser,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      })

      // Log the activity
      try {
        await activityLogger.logUserCreated(
          createdBy,
          'System Admin', // This will be updated when we have the creator's name
          'super_admin',
          storeId,
          adminUser.fullName,
          adminUser.role
        )
      } catch (error) {
        console.warn('Failed to log user creation activity:', error)
      }

      return adminUser
    } catch (error: any) {
      console.error('Error creating user:', error)
      
      let errorMessage = 'Failed to create user'
      if (error.code === 'auth/email-already-in-use') {
        errorMessage = 'Email address is already in use'
      } else if (error.code === 'auth/weak-password') {
        errorMessage = 'Password is too weak'
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'Invalid email address'
      }
      
      throw new Error(errorMessage)
    }
  }

  /**
   * Update an existing admin user
   */
  async updateUser(userId: string, updates: UpdateUserData, targetStoreId?: string): Promise<AdminUser> {
    const storeId = targetStoreId || this.storeId
    try {
      const userRef = doc(db, 'stores', storeId, 'users', userId)

      // Update Firestore document
      await updateDoc(userRef, {
        ...updates,
        updatedAt: serverTimestamp()
      })

      // Fetch updated user data
      const userDoc = await getDoc(userRef)
      if (!userDoc.exists()) {
        throw new Error('User not found')
      }

      return { id: userDoc.id, ...userDoc.data() } as AdminUser
    } catch (error) {
      console.error('Error updating user:', error)
      throw new Error('Failed to update user')
    }
  }

  /**
   * Delete an admin user (soft delete by deactivating)
   */
  async deleteUser(userId: string, targetStoreId?: string): Promise<void> {
    const storeId = targetStoreId || this.storeId
    try {
      const userRef = doc(db, 'stores', storeId, 'users', userId)

      // Soft delete by deactivating the user
      await updateDoc(userRef, {
        active: false,
        updatedAt: serverTimestamp()
      })
    } catch (error) {
      console.error('Error deleting user:', error)
      throw new Error('Failed to delete user')
    }
  }

  /**
   * Get all users for the current store
   */
  async getUsers(targetStoreId?: string): Promise<AdminUser[]> {
    const storeId = targetStoreId || this.storeId
    try {
      const q = query(
        collection(db, 'stores', storeId, 'users')
      )
      
      const snapshot = await getDocs(q)
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
        updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
        lastLoginAt: doc.data().lastLoginAt?.toDate?.()?.toISOString() || doc.data().lastLoginAt
      } as AdminUser))
    } catch (error) {
      console.error('Error fetching users:', error)
      throw new Error('Failed to fetch users')
    }
  }

  /**
   * Get a specific user by ID
   */
  async getUser(userId: string): Promise<AdminUser | null> {
    try {
      const userDoc = await getDoc(doc(db, 'users', userId))
      
      if (!userDoc.exists()) {
        return null
      }

      const data = userDoc.data()
      return {
        id: userDoc.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
        lastLoginAt: data.lastLoginAt?.toDate?.()?.toISOString() || data.lastLoginAt
      } as AdminUser
    } catch (error) {
      console.error('Error fetching user:', error)
      throw new Error('Failed to fetch user')
    }
  }

  /**
   * Send password reset email
   */
  async sendPasswordReset(email: string): Promise<void> {
    try {
      await sendPasswordResetEmail(auth, email)
    } catch (error: any) {
      console.error('Error sending password reset:', error)
      
      let errorMessage = 'Failed to send password reset email'
      if (error.code === 'auth/user-not-found') {
        errorMessage = 'No user found with this email address'
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'Invalid email address'
      }
      
      throw new Error(errorMessage)
    }
  }

  /**
   * Check if current user can manage another user
   */
  canManageUser(currentUser: AdminUser, targetUser: AdminUser): boolean {
    // Super admin can manage everyone except other super admins
    if (currentUser.role === 'super_admin') {
      return targetUser.role !== 'super_admin' || currentUser.id === targetUser.id
    }
    
    // Admin can manage editors and viewers
    if (currentUser.role === 'admin') {
      return ['editor', 'viewer'].includes(targetUser.role)
    }
    
    // Editors and viewers cannot manage other users
    return false
  }

  /**
   * Validate user permissions for role assignment
   */
  canAssignRole(currentUser: AdminUser, targetRole: AdminUser['role']): boolean {
    // Super admin can assign any role except super_admin
    if (currentUser.role === 'super_admin') {
      return targetRole !== 'super_admin'
    }
    
    // Admin can assign editor and viewer roles
    if (currentUser.role === 'admin') {
      return ['editor', 'viewer'].includes(targetRole)
    }
    
    // Editors and viewers cannot assign roles
    return false
  }
}

export const userService = new UserService()
