import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'

import { Button } from '../components/ui/button'
import { Badge } from '../components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'

import {
  ArrowLeft,
  Edit,
  Trash2,
  Package,
  DollarSign,
  Image as ImageIcon,
  Video,
  Tag,
  Calendar,
  Globe,
  Link as LinkIcon,
  BarChart3,
  Star
} from 'lucide-react'

import { dataService } from '../lib/data-service'
import { useFirebaseAuthStore } from '../store/firebase-auth'

interface Product {
  id: string
  storeId: string
  categoryId: string | null
  name: string
  slug: string
  description: string | null
  sku: string | null
  tags: string[]
  status: 'draft' | 'published' | 'archived'
  featured: boolean

  // Images & Media
  images: Array<{ url: string; alt?: string }> | string[]
  videoUrl: string | null

  // Pricing & Inventory
  price: {
    regular: number
    sale?: number | null
  } | number
  stock: {
    quantity: number
    status: 'in_stock' | 'low_stock' | 'out_of_stock'
    allowBackorders: boolean
  }
  unit: string

  // Variants
  variants: any[]
  variantAttributes: {
    sizes?: string[]
    colors?: string[]
    materials?: string[]
    settings?: {
      trackInventory: boolean
      allowCustomerSelection: boolean
      showVariantImages: boolean
    }
  }

  // SEO
  seo: {
    title?: string
    description?: string
    canonicalUrl?: string
  }

  // Related Products
  relatedProducts: string[]
  relatedProductsSettings?: {
    showOnProductPage: boolean
    showInCart: boolean
    maxProducts: number
    sectionTitle: string
  }

  // Timestamps
  createdAt: string
  updatedAt: string
  publishedAt?: string | null

  // Category info (populated)
  categories?: {
    name: string
  }
  categoryPath?: string

  // Legacy fields for backward compatibility
  inventoryQuantity?: number
  compareAtPrice?: number | null
}

export function ProductDetailPage() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { hasPermission } = useFirebaseAuthStore()
  
  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)

  const canEdit = hasPermission(['super_admin', 'admin', 'editor'])
  const canDelete = hasPermission(['super_admin', 'admin'])

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Published</Badge>
      case 'draft':
        return <Badge variant="secondary">Draft</Badge>
      case 'archived':
        return <Badge variant="outline">Archived</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getStockBadge = (stockStatus: string, quantity: number) => {
    switch (stockStatus) {
      case 'in_stock':
        return <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">In Stock</Badge>
      case 'low_stock':
        return <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">Low Stock</Badge>
      case 'out_of_stock':
        return <Badge variant="destructive">Out of Stock</Badge>
      default:
        return quantity === 0 ? (
          <Badge variant="destructive">Out of Stock</Badge>
        ) : quantity <= 10 ? (
          <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">Low Stock</Badge>
        ) : (
          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">{quantity} in stock</Badge>
        )
    }
  }

  const fetchProduct = async (productId: string) => {
    try {
      setLoading(true)
      const data = await dataService.getProduct(productId)
      if (data) {
        const categories = await dataService.getCategories()
        const categoryPath = buildCategoryPath(data.categoryId, categories)
        setProduct({ ...data, categoryPath })
      }
    } catch (error) {
      console.error('Error fetching product:', error)
    } finally {
      setLoading(false)
    }
  }

  const buildCategoryPath = (categoryId: string | null, categories: any[]): string => {
    if (!categoryId || !categories.length) return 'Uncategorized'
    
    const findCategoryPath = (id: string, visited = new Set()): string[] => {
      if (visited.has(id)) return []
      visited.add(id)
      
      const category = categories?.find(cat => cat.id === id)
      if (!category) return []
      
      if (category.parentId) {
        const parentPath = findCategoryPath(category.parentId, visited)
        return [...parentPath, category.name]
      }
      
      return [category.name]
    }
    
    const path = findCategoryPath(categoryId)
    return path.length > 0 ? path.join(' > ') : 'Uncategorized'
  }

  const handleDelete = async () => {
    if (!product || !canDelete) return
    
    if (window.confirm('Are you sure you want to delete this product?')) {
      try {
        await dataService.deleteProduct(product.id)
        toast.success('Product deleted successfully')
        navigate('/admin/products')
      } catch (error) {
        console.error('Error deleting product:', error)
        toast.error('Failed to delete product')
      }
    }
  }

  useEffect(() => {
    if (id) {
      fetchProduct(id)
    }
  }, [id])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading product...</p>
        </div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Product Not Found</h2>
          <p className="text-gray-500 mb-4">The product you are looking for does not exist.</p>
          <Button onClick={() => navigate('/admin/products')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/admin/products')}
            className="hover:bg-gray-100"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{product.name}</h1>
            <p className="text-gray-500">Product Details</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          {canEdit && (
            <Button
              onClick={() => navigate(`/admin/products/${product.id}/edit`)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit Product
            </Button>
          )}
          {canDelete && (
            <Button
              variant="destructive"
              onClick={handleDelete}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          )}
        </div>
      </div>

      {/* Product Content Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
        {/* Main Content - 3 columns */}
        <div className="xl:col-span-3 space-y-6">
          {/* Basic Info & Pricing Row */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Basic Information */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Package className="h-5 w-5" />
                  Product Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Name</label>
                    <p className="text-gray-900 dark:text-gray-100 font-medium mt-1">{product.name}</p>
                  </div>
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">SKU</label>
                    <p className="text-gray-900 dark:text-gray-100 font-mono text-sm mt-1">{product.sku || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Status</label>
                    <div className="mt-1">{getStatusBadge(product.status)}</div>
                  </div>
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Featured</label>
                    <div className="mt-1">
                      <Badge variant={product.featured ? "default" : "secondary"} className="text-xs">
                        {product.featured ? 'Yes' : 'No'}
                      </Badge>
                    </div>
                  </div>
                  <div className="col-span-2">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Category</label>
                    <p className="text-gray-900 dark:text-gray-100 mt-1">{product.categoryPath || product.categories?.name || 'Uncategorized'}</p>
                  </div>
                  {product.slug && (
                    <div className="col-span-2">
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Slug</label>
                      <p className="text-gray-900 dark:text-gray-100 font-mono text-sm mt-1">{product.slug}</p>
                    </div>
                  )}
                </div>

                {product.description && (
                  <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Description</label>
                    <p className="text-gray-900 dark:text-gray-100 mt-1 text-sm leading-relaxed">{product.description}</p>
                  </div>
                )}

                {product.tags && product.tags.length > 0 && (
                  <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Tags</label>
                    <div className="flex flex-wrap gap-1 mt-2">
                      {product.tags.map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs px-2 py-0.5">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Pricing & Inventory */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <DollarSign className="h-5 w-5" />
                  Pricing & Inventory
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Regular Price</label>
                    <p className="text-2xl font-bold text-gray-900 dark:text-gray-100 mt-1">
                      {formatPrice(typeof product.price === 'number' ? product.price : product.price?.regular || 0)}
                    </p>
                  </div>
                  {((typeof product.price === 'object' && product.price?.sale) || product.compareAtPrice) && (
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Sale Price</label>
                      <p className="text-xl font-semibold text-green-600 dark:text-green-400 mt-1">
                        {formatPrice(
                          (typeof product.price === 'object' ? product.price.sale : product.compareAtPrice) || 0
                        )}
                      </p>
                      {typeof product.price === 'object' && product.price.sale && product.price.regular && (
                        <p className="text-xs text-gray-500 mt-1">
                          Save {Math.round(((product.price.regular - product.price.sale) / product.price.regular) * 100)}%
                        </p>
                      )}
                    </div>
                  )}
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Stock Status</label>
                    <div className="mt-1">
                      {getStockBadge(
                        product.stock?.status || 'in_stock',
                        product.stock?.quantity || product.inventoryQuantity || 0
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Quantity</label>
                    <p className="text-gray-900 dark:text-gray-100 font-medium mt-1">
                      {product.stock?.quantity || product.inventoryQuantity || 0} {product.unit || 'pcs'}
                    </p>
                  </div>
                </div>

                {product.stock?.allowBackorders && (
                  <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-between">
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Backorders</label>
                      <Badge variant="outline" className="text-xs">
                        Allowed
                      </Badge>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Images & Media */}
          {((product.images && product.images.length > 0) || product.videoUrl) && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <ImageIcon className="h-5 w-5" />
                  Product Media
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {product.images && product.images.length > 0 && (
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3 block">Images</label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {product.images.slice(0, 8).map((image, index) => (
                        <div key={index} className="aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
                          <img
                            src={typeof image === 'string' ? image : image.url}
                            alt={typeof image === 'string' ? `${product.name} ${index + 1}` : image.alt || `${product.name} ${index + 1}`}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              const parent = target.parentElement;
                              if (parent && !parent.querySelector('.image-placeholder')) {
                                const placeholder = document.createElement('div');
                                placeholder.className = 'image-placeholder w-full h-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-400 text-xs';
                                placeholder.textContent = 'Image not found';
                                parent.appendChild(placeholder);
                              }
                            }}
                          />
                        </div>
                      ))}
                      {product.images.length > 8 && (
                        <div className="aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                          <div className="text-center text-gray-500">
                            <ImageIcon className="h-8 w-8 mx-auto mb-1" />
                            <p className="text-xs">+{product.images.length - 8} more</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {product.videoUrl && (
                  <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3 block">Video</label>
                    <div className="aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                      <div className="text-center">
                        <Video className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-500 break-all px-4">{product.videoUrl}</p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Variants */}
          {(product.variantAttributes?.sizes?.length ||
            product.variantAttributes?.colors?.length ||
            product.variantAttributes?.materials?.length) && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Tag className="h-5 w-5" />
                  Product Variants
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {product.variantAttributes?.sizes && product.variantAttributes.sizes.length > 0 && (
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Sizes</label>
                      <div className="flex flex-wrap gap-1 mt-2">
                        {product.variantAttributes.sizes.map((size, index) => (
                          <Badge key={index} variant="outline" className="text-xs px-2 py-0.5">
                            {size}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {product.variantAttributes?.colors && product.variantAttributes.colors.length > 0 && (
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Colors</label>
                      <div className="flex flex-wrap gap-1 mt-2">
                        {product.variantAttributes.colors.map((color, index) => (
                          <Badge key={index} variant="outline" className="text-xs px-2 py-0.5">
                            {color}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {product.variantAttributes?.materials && product.variantAttributes.materials.length > 0 && (
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Materials</label>
                      <div className="flex flex-wrap gap-1 mt-2">
                        {product.variantAttributes.materials.map((material, index) => (
                          <Badge key={index} variant="outline" className="text-xs px-2 py-0.5">
                            {material}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {product.variantAttributes?.settings && (
                  <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2 block">Variant Settings</label>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600 dark:text-gray-400">Track inventory:</span>
                        <Badge variant={product.variantAttributes.settings.trackInventory ? "default" : "secondary"} className="text-xs">
                          {product.variantAttributes.settings.trackInventory ? 'Yes' : 'No'}
                        </Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600 dark:text-gray-400">Customer selection:</span>
                        <Badge variant={product.variantAttributes.settings.allowCustomerSelection ? "default" : "secondary"} className="text-xs">
                          {product.variantAttributes.settings.allowCustomerSelection ? 'Yes' : 'No'}
                        </Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600 dark:text-gray-400">Variant images:</span>
                        <Badge variant={product.variantAttributes.settings.showVariantImages ? "default" : "secondary"} className="text-xs">
                          {product.variantAttributes.settings.showVariantImages ? 'Yes' : 'No'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar - 1 column */}
        <div className="space-y-4">
          {/* SEO Information */}
          {(product.seo?.title || product.seo?.description || product.seo?.canonicalUrl) && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-base">
                  <Globe className="h-4 w-4" />
                  SEO
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {product.seo?.title && (
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Title</label>
                    <p className="text-sm text-gray-900 dark:text-gray-100 mt-1 leading-tight">{product.seo.title}</p>
                  </div>
                )}
                {product.seo?.description && (
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Description</label>
                    <p className="text-sm text-gray-900 dark:text-gray-100 mt-1 leading-tight">{product.seo.description}</p>
                  </div>
                )}
                {product.seo?.canonicalUrl && (
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Canonical URL</label>
                    <p className="text-xs text-gray-900 dark:text-gray-100 font-mono mt-1 break-all">
                      {product.seo.canonicalUrl}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Related Products */}
          {product.relatedProducts && product.relatedProducts.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-base">
                  <LinkIcon className="h-4 w-4" />
                  Related Products
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Product IDs</label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {product.relatedProducts.slice(0, 3).map((productId, index) => (
                      <Badge key={index} variant="outline" className="text-xs font-mono px-2 py-0.5">
                        {productId.slice(0, 8)}...
                      </Badge>
                    ))}
                    {product.relatedProducts.length > 3 && (
                      <Badge variant="secondary" className="text-xs px-2 py-0.5">
                        +{product.relatedProducts.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>

                {product.relatedProductsSettings && (
                  <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2 block">Settings</label>
                    <div className="space-y-1 text-xs">
                      <div className="flex justify-between items-center">
                        <span>Product page:</span>
                        <Badge variant={product.relatedProductsSettings.showOnProductPage ? "default" : "secondary"} className="text-xs px-1.5 py-0.5">
                          {product.relatedProductsSettings.showOnProductPage ? 'On' : 'Off'}
                        </Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Cart:</span>
                        <Badge variant={product.relatedProductsSettings.showInCart ? "default" : "secondary"} className="text-xs px-1.5 py-0.5">
                          {product.relatedProductsSettings.showInCart ? 'On' : 'Off'}
                        </Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Max products:</span>
                        <span className="text-gray-900 dark:text-gray-100 font-medium">{product.relatedProductsSettings.maxProducts}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Section title:</span>
                        <span className="text-gray-900 dark:text-gray-100 text-xs">"{product.relatedProductsSettings.sectionTitle}"</span>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Timestamps */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-base">
                <Calendar className="h-4 w-4" />
                Timestamps
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Created</label>
                <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                  {new Date(product.createdAt).toLocaleDateString()}
                </p>
                <p className="text-xs text-gray-500">
                  {new Date(product.createdAt).toLocaleTimeString()}
                </p>
              </div>
              <div>
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Updated</label>
                <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                  {new Date(product.updatedAt).toLocaleDateString()}
                </p>
                <p className="text-xs text-gray-500">
                  {new Date(product.updatedAt).toLocaleTimeString()}
                </p>
              </div>
              {product.publishedAt && (
                <div>
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Published</label>
                  <p className="text-sm text-gray-900 dark:text-gray-100 mt-1">
                    {new Date(product.publishedAt).toLocaleDateString()}
                  </p>
                  <p className="text-xs text-gray-500">
                    {new Date(product.publishedAt).toLocaleTimeString()}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
