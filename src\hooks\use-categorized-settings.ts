import { useState, useEffect, useCallback } from 'react'
import { 
  settingsService, 
  type BrandingSettings,
  type GeneralSettings,
  type SEOSettings,
  type AuthSettings,
  type EmailSettings,
  type PaymentSettings,
  type ShippingSettings,
  type TaxSettings,
  type ComplianceSettings,
  type BehaviorSettings,
  type PanelPreferences,
  type CombinedStoreSettings
} from '../lib/settings-service'
import { useStoreManagement } from '../store/store-management'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { toast } from 'react-hot-toast'

interface UseCategorizedSettingsReturn {
  settings: CombinedStoreSettings | null
  loading: boolean
  saving: boolean
  error: string | null
  updateBranding: (updates: Partial<BrandingSettings>) => Promise<void>
  updateGeneral: (updates: Partial<GeneralSettings>) => Promise<void>
  updateSEO: (updates: Partial<SEOSettings>) => Promise<void>
  updateAuth: (updates: Partial<AuthSettings>) => Promise<void>
  updateEmail: (updates: Partial<EmailSettings>) => Promise<void>
  updatePayment: (updates: Partial<PaymentSettings>) => Promise<void>
  updateShipping: (updates: Partial<ShippingSettings>) => Promise<void>
  updateTax: (updates: Partial<TaxSettings>) => Promise<void>
  updateCompliance: (updates: Partial<ComplianceSettings>) => Promise<void>
  updateBehavior: (updates: Partial<BehaviorSettings>) => Promise<void>
  updatePanelPreferences: (updates: Partial<PanelPreferences>) => Promise<void>
  uploadFile: (file: File, path: string) => Promise<string>
  refreshSettings: () => Promise<void>
}

export function useCategorizedSettings(storeId?: string): UseCategorizedSettingsReturn {
  const { currentStore } = useStoreManagement()
  const { user } = useFirebaseAuthStore()
  const [settings, setSettings] = useState<CombinedStoreSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Determine store ID
  const resolvedStoreId = storeId || currentStore?.id || user?.primaryStoreId || user?.storeId

  // Load all settings
  const loadSettings = useCallback(async () => {
    if (!resolvedStoreId) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)
      const allSettings = await settingsService.getAllCategorizedSettings(resolvedStoreId)
      setSettings(allSettings)
    } catch (error) {
      console.error('Error loading categorized settings:', error)
      setError('Failed to load settings')
    } finally {
      setLoading(false)
    }
  }, [resolvedStoreId])

  // Update functions for each category
  const updateBranding = useCallback(async (updates: Partial<BrandingSettings>) => {
    if (!resolvedStoreId) return
    
    try {
      setSaving(true)
      await settingsService.updateBrandingSettings(resolvedStoreId, updates)
      toast.success('Branding settings updated successfully')
      await loadSettings()
    } catch (error) {
      console.error('Error updating branding settings:', error)
      toast.error('Failed to update branding settings')
      throw error
    } finally {
      setSaving(false)
    }
  }, [resolvedStoreId, loadSettings])

  const updateGeneral = useCallback(async (updates: Partial<GeneralSettings>) => {
    if (!resolvedStoreId) return
    
    try {
      setSaving(true)
      await settingsService.updateGeneralSettings(resolvedStoreId, updates)
      toast.success('General settings updated successfully')
      await loadSettings()
    } catch (error) {
      console.error('Error updating general settings:', error)
      toast.error('Failed to update general settings')
      throw error
    } finally {
      setSaving(false)
    }
  }, [resolvedStoreId, loadSettings])

  const updateSEO = useCallback(async (updates: Partial<SEOSettings>) => {
    if (!resolvedStoreId) return
    
    try {
      setSaving(true)
      const docRef = settingsService['getCategoryDocRef'](resolvedStoreId, 'seo')
      await settingsService['setDoc'](docRef, {
        ...updates,
        updatedAt: settingsService['serverTimestamp']()
      }, { merge: true })
      toast.success('SEO settings updated successfully')
      await loadSettings()
    } catch (error) {
      console.error('Error updating SEO settings:', error)
      toast.error('Failed to update SEO settings')
      throw error
    } finally {
      setSaving(false)
    }
  }, [resolvedStoreId, loadSettings])

  // Add similar update functions for other categories...
  const updateAuth = useCallback(async (updates: Partial<AuthSettings>) => {
    if (!resolvedStoreId) return
    setSaving(true)
    try {
      // Implementation will be added
      toast.success('Auth settings updated successfully')
    } catch (error) {
      toast.error('Failed to update auth settings')
      throw error
    } finally {
      setSaving(false)
    }
  }, [resolvedStoreId])

  const updateEmail = useCallback(async (updates: Partial<EmailSettings>) => {
    if (!resolvedStoreId) return
    setSaving(true)
    try {
      // Implementation will be added
      toast.success('Email settings updated successfully')
    } catch (error) {
      toast.error('Failed to update email settings')
      throw error
    } finally {
      setSaving(false)
    }
  }, [resolvedStoreId])

  const updatePayment = useCallback(async (updates: Partial<PaymentSettings>) => {
    if (!resolvedStoreId) return
    setSaving(true)
    try {
      // Implementation will be added
      toast.success('Payment settings updated successfully')
    } catch (error) {
      toast.error('Failed to update payment settings')
      throw error
    } finally {
      setSaving(false)
    }
  }, [resolvedStoreId])

  const updateShipping = useCallback(async (updates: Partial<ShippingSettings>) => {
    if (!resolvedStoreId) return
    setSaving(true)
    try {
      // Implementation will be added
      toast.success('Shipping settings updated successfully')
    } catch (error) {
      toast.error('Failed to update shipping settings')
      throw error
    } finally {
      setSaving(false)
    }
  }, [resolvedStoreId])

  const updateTax = useCallback(async (updates: Partial<TaxSettings>) => {
    if (!resolvedStoreId) return
    setSaving(true)
    try {
      // Implementation will be added
      toast.success('Tax settings updated successfully')
    } catch (error) {
      toast.error('Failed to update tax settings')
      throw error
    } finally {
      setSaving(false)
    }
  }, [resolvedStoreId])

  const updateCompliance = useCallback(async (updates: Partial<ComplianceSettings>) => {
    if (!resolvedStoreId) return
    setSaving(true)
    try {
      // Implementation will be added
      toast.success('Compliance settings updated successfully')
    } catch (error) {
      toast.error('Failed to update compliance settings')
      throw error
    } finally {
      setSaving(false)
    }
  }, [resolvedStoreId])

  const updateBehavior = useCallback(async (updates: Partial<BehaviorSettings>) => {
    if (!resolvedStoreId) return
    setSaving(true)
    try {
      // Implementation will be added
      toast.success('Behavior settings updated successfully')
    } catch (error) {
      toast.error('Failed to update behavior settings')
      throw error
    } finally {
      setSaving(false)
    }
  }, [resolvedStoreId])

  const updatePanelPreferences = useCallback(async (updates: Partial<PanelPreferences>) => {
    if (!resolvedStoreId) return
    setSaving(true)
    try {
      // Implementation will be added
      toast.success('Panel preferences updated successfully')
    } catch (error) {
      toast.error('Failed to update panel preferences')
      throw error
    } finally {
      setSaving(false)
    }
  }, [resolvedStoreId])

  // File upload function
  const uploadFile = useCallback(async (file: File, path: string): Promise<string> => {
    if (!resolvedStoreId) throw new Error('Store ID not available')
    
    try {
      const downloadURL = await settingsService.uploadFile(resolvedStoreId, file, path)
      return downloadURL
    } catch (error) {
      console.error('Error uploading file:', error)
      throw error
    }
  }, [resolvedStoreId])

  // Refresh settings
  const refreshSettings = useCallback(async () => {
    await loadSettings()
  }, [loadSettings])

  // Load settings on mount and when store ID changes
  useEffect(() => {
    loadSettings()
  }, [loadSettings])

  // Set up real-time listeners
  useEffect(() => {
    if (!resolvedStoreId) return

    const unsubscribe = settingsService.subscribeToAllCategorizedSettings(
      resolvedStoreId,
      (newSettings) => {
        setSettings(newSettings)
        setError(null)
      }
    )

    return unsubscribe
  }, [resolvedStoreId])

  return {
    settings,
    loading,
    saving,
    error,
    updateBranding,
    updateGeneral,
    updateSEO,
    updateAuth,
    updateEmail,
    updatePayment,
    updateShipping,
    updateTax,
    updateCompliance,
    updateBehavior,
    updatePanelPreferences,
    uploadFile,
    refreshSettings
  }
}
