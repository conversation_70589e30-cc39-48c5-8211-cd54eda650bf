// Product types matching the Firestore schema from add product.md

export interface ProductVariant {
  id: string
  attributes: Record<string, string> // e.g., { color: "Gold", size: "M" }
  price: {
    regular: number
    sale?: number
  }
  sku: string
  stock: {
    quantity: number
    status: 'in_stock' | 'out_of_stock'
    allowBackorders: boolean
  }
  image?: string
}

export interface ProductPrice {
  regular: number
  sale?: number
}

export interface ProductStock {
  quantity: number
  status: 'in_stock' | 'out_of_stock'
  allowBackorders: boolean
  lowStockThreshold?: number
  trackInventory?: boolean
}

export interface ProductSEO {
  title: string
  description: string
  canonicalUrl?: string
  structuredData?: Record<string, any>
}

export interface ProductVariantAttributes {
  [key: string]: string[] // e.g., { color: ["Gold", "Silver"], size: ["S", "M"] }
}

export interface Product {
  id?: string
  name: string
  slug: string
  description: string
  categoryId: string
  tags: string[]
  images: string[]
  videoUrl?: string
  price: ProductPrice
  sku: string
  stock: ProductStock
  unit: 'gm' | 'ml' | 'pcs'
  variants?: ProductVariant[]
  variantAttributes?: ProductVariantAttributes
  seo: ProductSEO
  relatedProducts: string[]
  status: 'draft' | 'published' | 'archived'
  featured?: boolean

  // Inventory fields
  inventoryQuantity?: number
  lowStockThreshold?: number
  trackInventory?: boolean
  isOutOfStock?: boolean
  hasVariants?: boolean
  totalStock?: number
  lastStockUpdate?: string

  createdAt?: string
  updatedAt?: string
  publishedAt?: string
  storeId: string
}

// Form interfaces for the add product page
export interface ProductFormData {
  // Basic Info
  name: string
  slug: string
  description: string
  categoryId: string
  tags: string[]
  status: 'draft' | 'published' | 'archived'

  // Images & Media
  images: string[]
  videoUrl: string

  // Pricing & Inventory
  price: ProductPrice
  sku: string
  stock: ProductStock
  unit: 'gm' | 'ml' | 'pcs'

  // Variants
  variants: ProductVariant[]
  variantAttributes: ProductVariantAttributes

  // SEO
  seo: ProductSEO

  // Related Products
  relatedProducts: string[]

  // Additional fields
  featured: boolean
}

export interface ValidationErrors {
  [key: string]: string
}

export interface UploadProgress {
  progress: number
  url?: string
  error?: string
}

// Category interface for hierarchical selection
export interface Category {
  id: string
  name: string
  slug: string
  parentId?: string
  description?: string
  image?: string
  status: 'active' | 'inactive'
  sortOrder: number
  createdAt: string
  updatedAt: string
}

// Product attribute types for variants
export interface ProductAttribute {
  name: string
  values: string[]
  type: 'color' | 'size' | 'material' | 'custom'
}

// Related product suggestion
export interface RelatedProductSuggestion {
  id: string
  name: string
  image?: string
  price: number
  category: string
  relevanceScore: number
}

// Form validation rules
export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: any) => string | null
}

export interface FormValidationRules {
  [fieldName: string]: ValidationRule
}
