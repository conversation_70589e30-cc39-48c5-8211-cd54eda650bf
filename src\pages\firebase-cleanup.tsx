import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Badge } from '../components/ui/badge'
import { Alert, AlertDescription } from '../components/ui/alert'
import { 
  Trash2, 
  Database, 
  Users, 
  Store, 
  Package, 
  ShoppingCart, 
  UserCheck, 
  FolderOpen,
  AlertTriangle,
  RefreshCw,
  Loader2
} from 'lucide-react'
import { firebaseCleanupService, CleanupStats } from '../lib/firebase-cleanup-service'
import { toast } from 'react-hot-toast'

export function FirebaseCleanupPage() {
  const [stats, setStats] = useState<CleanupStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [loadingStats, setLoadingStats] = useState(true)
  const [confirmations, setConfirmations] = useState<Record<string, boolean>>({})

  // Load cleanup statistics
  const loadStats = async () => {
    try {
      setLoadingStats(true)
      const cleanupStats = await firebaseCleanupService.getCleanupStats()
      setStats(cleanupStats)
    } catch (error) {
      console.error('Error loading stats:', error)
      toast.error('Failed to load cleanup statistics')
    } finally {
      setLoadingStats(false)
    }
  }

  useEffect(() => {
    loadStats()
  }, [])

  const handleCleanup = async (type: string, cleanupFn: () => Promise<void>) => {
    if (!confirmations[type]) {
      toast.error('Please confirm the action by checking the checkbox')
      return
    }

    try {
      setLoading(true)
      await cleanupFn()
      await loadStats() // Refresh stats
      setConfirmations(prev => ({ ...prev, [type]: false })) // Reset confirmation
    } catch (error: any) {
      toast.error(error.message || 'Cleanup failed')
    } finally {
      setLoading(false)
    }
  }

  const toggleConfirmation = (type: string) => {
    setConfirmations(prev => ({ ...prev, [type]: !prev[type] }))
  }

  const cleanupActions = [
    {
      id: 'stores',
      title: 'Clean Store Data',
      description: 'Remove all stores, products, orders, categories, and store users',
      icon: Store,
      count: stats ? stats.stores + stats.products + stats.orders + stats.categories : 0,
      action: () => firebaseCleanupService.cleanStoreData(),
      color: 'bg-blue-500',
      dangerous: true
    },
    {
      id: 'users',
      title: 'Clean User Data',
      description: 'Remove all users and customers from the system',
      icon: Users,
      count: stats ? stats.users + stats.customers : 0,
      action: () => firebaseCleanupService.cleanUserData(),
      color: 'bg-green-500',
      dangerous: true
    },
    {
      id: 'requests',
      title: 'Clean Store Requests',
      description: 'Remove all pending store registration requests',
      icon: UserCheck,
      count: stats?.storeRequests || 0,
      action: () => firebaseCleanupService.cleanStoreRequests(),
      color: 'bg-yellow-500',
      dangerous: false
    },
    {
      id: 'storage',
      title: 'Clean Storage Files',
      description: 'Remove all uploaded files from Firebase Storage',
      icon: FolderOpen,
      count: stats?.storageFiles || 0,
      action: () => firebaseCleanupService.cleanStorageFiles(),
      color: 'bg-purple-500',
      dangerous: true
    },
    {
      id: 'all',
      title: 'Clean All Data',
      description: 'Nuclear option: Remove ALL data from Firebase',
      icon: Database,
      count: stats ? Object.values(stats).reduce((sum, count) => sum + count, 0) : 0,
      action: () => firebaseCleanupService.cleanAllData(),
      color: 'bg-red-500',
      dangerous: true
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Firebase Data Cleanup</h1>
          <p className="text-gray-600 mt-2">
            Manage and clean Firebase data for development and testing
          </p>
        </div>
        <Button
          onClick={loadStats}
          disabled={loadingStats}
          variant="outline"
          className="flex items-center gap-2"
        >
          {loadingStats ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
          Refresh Stats
        </Button>
      </div>

      {/* Warning Alert */}
      <Alert className="border-red-200 bg-red-50">
        <AlertTriangle className="h-4 w-4 text-red-600" />
        <AlertDescription className="text-red-800">
          <strong>Warning:</strong> These actions are irreversible and will permanently delete data from Firebase.
          Only use this in development environments. Always backup important data before proceeding.
        </AlertDescription>
      </Alert>

      {/* Statistics Overview */}
      {stats && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Current Data Statistics
            </CardTitle>
            <CardDescription>
              Overview of data currently stored in Firebase
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <Store className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-blue-900">{stats.stores}</div>
                <div className="text-sm text-blue-700">Stores</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-green-900">{stats.users}</div>
                <div className="text-sm text-green-700">Users</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <Package className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-purple-900">{stats.products}</div>
                <div className="text-sm text-purple-700">Products</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <ShoppingCart className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-orange-900">{stats.orders}</div>
                <div className="text-sm text-orange-700">Orders</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Cleanup Actions */}
      <div className="grid gap-6">
        {cleanupActions.map((action) => (
          <Card key={action.id} className="border-l-4" style={{ borderLeftColor: action.color.replace('bg-', '#') }}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${action.color} text-white`}>
                    <action.icon className="h-5 w-5" />
                  </div>
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      {action.title}
                      {action.dangerous && (
                        <Badge variant="destructive" className="text-xs">
                          Dangerous
                        </Badge>
                      )}
                    </CardTitle>
                    <CardDescription>{action.description}</CardDescription>
                  </div>
                </div>
                <Badge variant="secondary" className="text-lg px-3 py-1">
                  {action.count} items
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    id={`confirm-${action.id}`}
                    checked={confirmations[action.id] || false}
                    onChange={() => toggleConfirmation(action.id)}
                    className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                  />
                  <label htmlFor={`confirm-${action.id}`} className="text-sm text-gray-700">
                    I understand this action cannot be undone
                  </label>
                </div>
                <Button
                  onClick={() => handleCleanup(action.id, action.action)}
                  disabled={loading || !confirmations[action.id] || action.count === 0}
                  variant={action.dangerous ? "destructive" : "default"}
                  className="flex items-center gap-2"
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Trash2 className="h-4 w-4" />
                  )}
                  Clean {action.count} Items
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
