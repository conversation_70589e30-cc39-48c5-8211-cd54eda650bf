{"indexes": [{"collectionGroup": "categories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "sortOrder", "order": "ASCENDING"}]}, {"collectionGroup": "customers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "customers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "customers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "totalSpent", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "categoryId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "role", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "pending_store_requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "submittedAt", "order": "DESCENDING"}]}, {"collectionGroup": "stores", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "settings", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "settings", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}