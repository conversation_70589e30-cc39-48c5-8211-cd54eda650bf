import { useState, useEffect } from 'react'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Textarea } from '../../components/ui/textarea'
import { Select } from '../../components/ui/select'
import { Button } from '../../components/ui/button'
import { toast } from 'react-hot-toast'
import { Save, Loader2 } from 'lucide-react'
import { useRealtimeSettings } from '../../hooks/use-realtime-settings'
import { useStoreManagement } from '../../store/store-management'
import { useFirebaseAuthStore } from '../../store/firebase-auth'

export function GeneralSettingsPage() {
  const { hasPermission } = useFirebaseAuthStore()
  const { currentStore, syncStoreNameFromSettings } = useStoreManagement()
  const {
    settings,
    loading,
    updateGeneral,
    initializeSettings
  } = useRealtimeSettings(currentStore?.id || 'default')

  const [formData, setFormData] = useState({})
  const [isSaving, setIsSaving] = useState(false)

  const canEdit = hasPermission(['super_admin', 'admin'])

  // Debug logging
  console.log('🔍 General Settings Debug:', {
    currentStore: currentStore?.id,
    settings: settings,
    loading,
    canEdit,
    formData
  })

  // Initialize form data when settings load
  useEffect(() => {
    if (settings?.general) {
      console.log('📝 Setting form data from settings:', settings.general)
      setFormData(settings.general)
    }
  }, [settings?.general])

  // Initialize settings if they don't exist
  useEffect(() => {
    if (!loading && !settings?.general && currentStore?.id) {
      console.log('🔧 Initializing general settings for store:', currentStore.id)
      initializeSettings()
    }
  }, [loading, settings?.general, currentStore?.id, initializeSettings])

  // Function to generate slug from store name
  const generateSlug = (name) => {
    return name
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
      .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
  }

  // Handle field changes
  const handleFieldChange = (field, value) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value }
      
      // Auto-update store slug when store name changes
      if (field === 'storeName' && value) {
        newData.storeSlug = generateSlug(value)
      }
      
      return newData
    })
  }

  // Handle save
  const handleSave = async () => {
    if (!canEdit) return

    setIsSaving(true)
    try {
      await updateGeneral(formData)
      
      // Sync store name to main store document if it was updated
      if (formData.storeName && currentStore?.id) {
        await syncStoreNameFromSettings(currentStore.id, formData.storeName, formData.storeSlug)
      }
      
      toast.success('General settings saved successfully!')
    } catch (error) {
      console.error('Error saving general settings:', error)
      toast.error('Failed to save settings. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">General Settings</h1>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            Basic store information and configuration
          </p>
        </div>
        <Button
          onClick={handleSave}
          disabled={isSaving || !canEdit}
          className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
        >
          {isSaving ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          {isSaving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>

      {/* Store Information */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Store Information</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="storeName">Store Name</Label>
            <Input
              id="storeName"
              value={formData.storeName || ''}
              onChange={(e) => handleFieldChange('storeName', e.target.value)}
              disabled={!canEdit}
              placeholder="My Store"
            />
          </div>
          <div>
            <Label htmlFor="storeSlug">Store Slug</Label>
            <Input
              id="storeSlug"
              value={formData.storeSlug || ''}
              disabled={true}
              placeholder="my-store"
              className="bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-400"
            />
            <p className="text-xs text-gray-500 mt-1">
              Auto-generated from store name. This will be used in your store URL.
            </p>
          </div>
        </div>

        <div className="mt-6">
          <Label htmlFor="description">Store Description</Label>
          <Textarea
            id="description"
            value={formData.description || ''}
            onChange={(e) => handleFieldChange('description', e.target.value)}
            disabled={!canEdit}
            placeholder="Brief description of your store"
            rows={3}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <div>
            <Label htmlFor="contactEmail">Contact Email</Label>
            <Input
              id="contactEmail"
              type="email"
              value={formData.contactEmail || ''}
              onChange={(e) => handleFieldChange('contactEmail', e.target.value)}
              disabled={!canEdit}
              placeholder="<EMAIL>"
            />
          </div>
          <div>
            <Label htmlFor="contactPhone">Contact Phone</Label>
            <Input
              id="contactPhone"
              value={formData.contactPhone || ''}
              onChange={(e) => handleFieldChange('contactPhone', e.target.value)}
              disabled={!canEdit}
              placeholder="+****************"
            />
          </div>
        </div>
      </div>

      {/* Localization */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Localization</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <Label htmlFor="currency">Currency</Label>
            <Select
              value={formData.currency || 'USD'}
              onValueChange={(value) => handleFieldChange('currency', value)}
              disabled={!canEdit}
              options={[
                { value: 'USD', label: 'US Dollar ($)' },
                { value: 'EUR', label: 'Euro (€)' },
                { value: 'GBP', label: 'British Pound (£)' },
                { value: 'JPY', label: 'Japanese Yen (¥)' },
                { value: 'CAD', label: 'Canadian Dollar (C$)' },
                { value: 'AUD', label: 'Australian Dollar (A$)' },
                { value: 'PKR', label: 'Pakistani Rupee (₨)' }
              ]}
            />
          </div>
          <div>
            <Label htmlFor="locale">Locale</Label>
            <Select
              value={formData.locale || 'en-US'}
              onValueChange={(value) => handleFieldChange('locale', value)}
              disabled={!canEdit}
              options={[
                { value: 'en-US', label: 'English (US)' },
                { value: 'en-GB', label: 'English (UK)' },
                { value: 'es-ES', label: 'Spanish' },
                { value: 'fr-FR', label: 'French' },
                { value: 'de-DE', label: 'German' },
                { value: 'it-IT', label: 'Italian' },
                { value: 'pt-BR', label: 'Portuguese (Brazil)' },
                { value: 'ja-JP', label: 'Japanese' },
                { value: 'ko-KR', label: 'Korean' },
                { value: 'zh-CN', label: 'Chinese (Simplified)' }
              ]}
            />
          </div>
          <div>
            <Label htmlFor="timeZone">Time Zone</Label>
            <Select
              value={formData.timeZone || 'UTC'}
              onValueChange={(value) => handleFieldChange('timeZone', value)}
              disabled={!canEdit}
              options={[
                { value: 'UTC', label: 'UTC' },
                { value: 'America/New_York', label: 'Eastern Time (ET)' },
                { value: 'America/Chicago', label: 'Central Time (CT)' },
                { value: 'America/Denver', label: 'Mountain Time (MT)' },
                { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
                { value: 'Europe/London', label: 'London (GMT)' },
                { value: 'Europe/Paris', label: 'Paris (CET)' },
                { value: 'Asia/Tokyo', label: 'Tokyo (JST)' },
                { value: 'Asia/Shanghai', label: 'Shanghai (CST)' },
                { value: 'Australia/Sydney', label: 'Sydney (AEST)' },
                { value: 'Asia/Karachi', label: 'Karachi (PKT)' }
              ]}
            />
          </div>
        </div>

        <div className="mt-6">
          <Label htmlFor="status">Store Status</Label>
          <Select
            value={formData.status || 'active'}
            onValueChange={(value) => handleFieldChange('status', value)}
            disabled={!canEdit}
            options={[
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' },
              { value: 'maintenance', label: 'Maintenance' }
            ]}
          />
        </div>
      </div>
    </div>
  )
}
