import { useState, useEffect } from 'react'
import { Wifi, WifiOff, Clock, CheckCircle } from 'lucide-react'
import { cn } from '../../lib/utils'

interface RealTimeIndicatorProps {
  isConnected: boolean
  lastUpdated?: Date | null
  className?: string
  showText?: boolean
}

export function RealTimeIndicator({ 
  isConnected, 
  lastUpdated, 
  className,
  showText = true 
}: RealTimeIndicatorProps) {
  const [timeAgo, setTimeAgo] = useState<string>('')

  useEffect(() => {
    if (!lastUpdated) return

    const updateTimeAgo = () => {
      const now = new Date()
      const diff = now.getTime() - lastUpdated.getTime()
      const seconds = Math.floor(diff / 1000)
      const minutes = Math.floor(seconds / 60)
      const hours = Math.floor(minutes / 60)

      if (seconds < 60) {
        setTimeAgo('just now')
      } else if (minutes < 60) {
        setTimeAgo(`${minutes}m ago`)
      } else if (hours < 24) {
        setTimeAgo(`${hours}h ago`)
      } else {
        setTimeAgo('over a day ago')
      }
    }

    updateTimeAgo()
    const interval = setInterval(updateTimeAgo, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [lastUpdated])

  const getIcon = () => {
    if (!isConnected) {
      return <WifiOff className="h-4 w-4 text-red-500" />
    }
    if (lastUpdated) {
      return <CheckCircle className="h-4 w-4 text-green-500" />
    }
    return <Clock className="h-4 w-4 text-yellow-500" />
  }

  const getStatusText = () => {
    if (!isConnected) {
      return 'Disconnected'
    }
    if (lastUpdated) {
      return `Updated ${timeAgo}`
    }
    return 'Connecting...'
  }

  const getStatusColor = () => {
    if (!isConnected) {
      return 'text-red-600'
    }
    if (lastUpdated) {
      return 'text-green-600'
    }
    return 'text-yellow-600'
  }

  return (
    <div className={cn(
      'flex items-center gap-2 text-sm',
      getStatusColor(),
      className
    )}>
      {getIcon()}
      {showText && (
        <span className="font-medium">
          {getStatusText()}
        </span>
      )}
      {isConnected && (
        <div className="flex items-center gap-1">
          <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
          <span className="text-xs text-gray-500">Live</span>
        </div>
      )}
    </div>
  )
}

// Compact version for headers
export function RealTimeIndicatorCompact({ 
  isConnected, 
  lastUpdated, 
  className 
}: RealTimeIndicatorProps) {
  return (
    <RealTimeIndicator
      isConnected={isConnected}
      lastUpdated={lastUpdated}
      showText={false}
      className={cn('text-xs', className)}
    />
  )
}

// Status badge version
export function RealTimeStatusBadge({ 
  isConnected, 
  lastUpdated, 
  className 
}: RealTimeIndicatorProps) {
  const getStatusColor = () => {
    if (!isConnected) return 'bg-red-100 text-red-800 border-red-200'
    if (lastUpdated) return 'bg-green-100 text-green-800 border-green-200'
    return 'bg-yellow-100 text-yellow-800 border-yellow-200'
  }

  const getStatusText = () => {
    if (!isConnected) return 'Offline'
    if (lastUpdated) return 'Live'
    return 'Connecting'
  }

  return (
    <div className={cn(
      'inline-flex items-center gap-1 px-2 py-1 rounded-full border text-xs font-medium',
      getStatusColor(),
      className
    )}>
      <div className={cn(
        'h-1.5 w-1.5 rounded-full',
        isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'
      )} />
      {getStatusText()}
    </div>
  )
}
