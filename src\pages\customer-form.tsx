import React, { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { toast } from 'react-hot-toast'
import { useCustomers } from '../hooks/use-customers'
import { Button } from '../components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import {
  ArrowLeft,
  Save,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Tag,
  FileText,
  Settings
} from 'lucide-react'
import type { Customer } from '../lib/firebase'

interface CustomerFormData {
  fullName: string
  email: string
  phone: string
  isRegistered: boolean
  group: string | null
  tags: string[]
  notes: string

  // Additional fields for enhanced functionality
  dateOfBirth: string
  gender: string
  status: 'active' | 'inactive' | 'blocked'
  marketingOptIn: boolean
  preferredLanguage: string
  timezone: string
  address: {
    shipping: {
      line1: string
      city: string
      zip: string
      country: string
    }
    billing: {
      line1: string
      city: string
      zip: string
      country: string
    }
  }
}

export function CustomerFormPage() {
  const navigate = useNavigate()
  const { id } = useParams<{ id: string }>()
  const { user, hasPermission } = useFirebaseAuthStore()
  const isEditing = Boolean(id && id !== 'new')

  const {
    customers,
    createCustomer,
    updateCustomer,
    loading: customersLoading
  } = useCustomers({
    realtime: false
  })

  const [formData, setFormData] = useState<CustomerFormData>({
    fullName: '',
    email: '',
    phone: '',
    isRegistered: false,
    group: null,
    tags: [],
    notes: '',
    dateOfBirth: '',
    gender: '',
    status: 'active',
    marketingOptIn: true,
    preferredLanguage: 'en',
    timezone: 'Asia/Karachi',
    address: {
      shipping: {
        line1: '',
        city: '',
        zip: '',
        country: 'Pakistan'
      },
      billing: {
        line1: '',
        city: '',
        zip: '',
        country: 'Pakistan'
      }
    }
  })

  const [loading, setLoading] = useState(false)
  const [sameAsShipping, setSameAsShipping] = useState(true)

  const canEdit = hasPermission(['super_admin', 'admin', 'editor'])

  // Load customer data if editing
  useEffect(() => {
    if (isEditing && customers.length > 0) {
      const customer = customers.find(c => c.id === id)
      if (customer) {
        setFormData({
          name: customer.name || '',
          email: customer.email || '',
          phone: customer.phone || '',
          dateOfBirth: customer.dateOfBirth || '',
          gender: customer.gender || '',
          status: customer.status || 'active',
          groups: customer.groups || [],
          tags: customer.tags || [],
          notes: customer.notes || '',
          marketingOptIn: customer.marketingOptIn ?? true,
          preferredLanguage: customer.preferredLanguage || 'en',
          timezone: customer.timezone || 'Asia/Karachi',
          address: {
            shipping: {
              line1: customer.address?.shipping?.line1 || '',
              city: customer.address?.shipping?.city || '',
              zip: customer.address?.shipping?.zip || '',
              country: customer.address?.shipping?.country || 'Pakistan'
            },
            billing: {
              line1: customer.address?.billing?.line1 || customer.address?.shipping?.line1 || '',
              city: customer.address?.billing?.city || customer.address?.shipping?.city || '',
              zip: customer.address?.billing?.zip || customer.address?.shipping?.zip || '',
              country: customer.address?.billing?.country || customer.address?.shipping?.country || 'Pakistan'
            }
          }
        })
      }
    }
  }, [isEditing, id, customers])

  // Copy shipping to billing when checkbox is checked
  useEffect(() => {
    if (sameAsShipping) {
      setFormData(prev => ({
        ...prev,
        address: {
          ...prev.address,
          billing: { ...prev.address.shipping }
        }
      }))
    }
  }, [sameAsShipping, formData.address.shipping])

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleAddressChange = (type: 'shipping' | 'billing', field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      address: {
        ...prev.address,
        [type]: {
          ...prev.address[type],
          [field]: value
        }
      }
    }))
  }

  const handleArrayChange = (field: 'groups' | 'tags', value: string) => {
    const items = value.split(',').map(item => item.trim()).filter(Boolean)
    setFormData(prev => ({
      ...prev,
      [field]: items
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!canEdit) {
      toast.error('You do not have permission to perform this action')
      return
    }

    if (!formData.fullName || !formData.email) {
      toast.error('Full name and email are required')
      return
    }

    try {
      setLoading(true)

      const customerData: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'> = {
        ...formData,
        lifetimeValue: 0, // Will be calculated from orders
        orderCount: 0, // Will be updated when orders are placed
        storeId: user?.storeIds?.[0] || 'womanza-jewelry-store'
      }

      if (isEditing) {
        await updateCustomer(id!, customerData)
        toast.success('Customer updated successfully')
      } else {
        await createCustomer(customerData)
        toast.success('Customer created successfully')
      }

      navigate('/admin/customers')
    } catch (error) {
      console.error('Error saving customer:', error)
      toast.error('Failed to save customer')
    } finally {
      setLoading(false)
    }
  }

  if (!canEdit) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Access Denied</h3>
          <p className="text-gray-500 dark:text-gray-400">
            You do not have permission to create or edit customers.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center gap-3">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate('/admin/customers')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Customers
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
            {isEditing ? 'Edit Customer' : 'Add New Customer'}
          </h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {isEditing ? 'Update customer information' : 'Create a new customer profile'}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Basic Information
            </CardTitle>
            <CardDescription>
              Essential customer details and contact information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Full Name *
                </label>
                <input
                  type="text"
                  value={formData.fullName}
                  onChange={(e) => handleInputChange('fullName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  placeholder="Enter customer's full name"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  placeholder="+92 300 1234567"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Date of Birth
                </label>
                <input
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Gender
                </label>
                <select
                  value={formData.gender}
                  onChange={(e) => handleInputChange('gender', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value="">Select Gender</option>
                  <option value="female">Female</option>
                  <option value="male">Male</option>
                  <option value="other">Other</option>
                  <option value="prefer_not_to_say">Prefer not to say</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="blocked">Blocked</option>
                </select>
              </div>

              {/* Customer Group and Registration Status */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Customer Group
                  </label>
                  <select
                    value={formData.group || ''}
                    onChange={(e) => handleInputChange('group', e.target.value || null)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  >
                    <option value="">Select Group</option>
                    <option value="VIP">VIP</option>
                    <option value="Loyal">Loyal</option>
                    <option value="Wholesale">Wholesale</option>
                    <option value="Retail">Retail</option>
                    <option value="New">New</option>
                    <option value="At Risk">At Risk</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Registration Type
                  </label>
                  <select
                    value={formData.isRegistered ? 'registered' : 'manual'}
                    onChange={(e) => handleInputChange('isRegistered', e.target.value === 'registered')}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  >
                    <option value="manual">Manual Entry</option>
                    <option value="registered">Registered Account</option>
                  </select>
                </div>
              </div>

              {/* Tags and Notes */}
              <div className="grid grid-cols-1 gap-4 mt-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Tags
                  </label>
                  <input
                    type="text"
                    value={formData.tags.join(', ')}
                    onChange={(e) => handleInputChange('tags', e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                    placeholder="Enter tags separated by commas (e.g., vip, high-value, frequent-buyer)"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Notes
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                    placeholder="Add any notes about this customer..."
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate('/admin/customers')}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading}
          >
            <Save className="h-4 w-4 mr-2" />
            {loading ? 'Saving...' : isEditing ? 'Update Customer' : 'Create Customer'}
          </Button>
        </div>
      </form>
    </div>
  )
}

export default CustomerFormPage
