import { useMemo, useRef, useState, useEffect } from 'react'

/**
 * Hook to provide stable data references to prevent unnecessary re-renders
 * Particularly useful for chart components that are sensitive to data changes
 */
export function useStableData<T>(data: T, compareFn?: (prev: T, next: T) => boolean): T {
  const prevDataRef = useRef<T>(data)
  
  const stableData = useMemo(() => {
    // Use custom compare function if provided
    if (compareFn) {
      if (compareFn(prevDataRef.current, data)) {
        return prevDataRef.current
      }
    } else {
      // Default comparison using JSON.stringify for deep comparison
      try {
        if (JSON.stringify(prevDataRef.current) === JSON.stringify(data)) {
          return prevDataRef.current
        }
      } catch (error) {
        // Fallback to reference comparison if JSON.stringify fails
        if (prevDataRef.current === data) {
          return prevDataRef.current
        }
      }
    }
    
    // Data has changed, update reference and return new data
    prevDataRef.current = data
    return data
  }, [data, compareFn])
  
  return stableData
}

/**
 * Hook specifically for chart data that prevents re-renders when data structure is the same
 */
export function useStableChartData<T extends any[]>(data: T): T {
  return useStableData(data, (prev, next) => {
    // Quick checks first
    if (prev === next) return true
    if (!prev || !next) return false
    if (prev.length !== next.length) return false
    
    // Deep comparison for chart data
    try {
      return JSON.stringify(prev) === JSON.stringify(next)
    } catch {
      return false
    }
  })
}

/**
 * Hook for memoizing chart props to prevent unnecessary re-renders
 */
export function useStableChartProps<T extends Record<string, any>>(props: T): T {
  return useMemo(() => props, [JSON.stringify(props)])
}

/**
 * Debounced data hook to prevent rapid updates
 */
export function useDebouncedData<T>(data: T, delay: number = 300): T {
  const timeoutRef = useRef<NodeJS.Timeout>()
  const [debouncedData, setDebouncedData] = useState<T>(data)
  
  useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    timeoutRef.current = setTimeout(() => {
      setDebouncedData(data)
    }, delay)
    
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [data, delay])
  
  return debouncedData
}
