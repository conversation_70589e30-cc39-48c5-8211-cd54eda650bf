import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { useStoreManagement } from '../store/store-management'
import { activityLogger, type ActivityLog } from '../lib/activity-logger'
import toast from 'react-hot-toast'
import {
  Activity,
  Search,
  Filter,
  Calendar,
  User,
  Eye,
  Edit,
  Trash2,
  Plus,
  Settings,
  ShoppingCart,
  Package,
  Users,
  Download,
  RefreshCw,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info
} from 'lucide-react'



export function ActivityLogsPage() {
  const { user } = useFirebaseAuthStore()
  const { currentStore } = useStoreManagement()
  const [logs, setLogs] = useState<ActivityLog[]>([])
  const [filteredLogs, setFilteredLogs] = useState<ActivityLog[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedAction, setSelectedAction] = useState<string>('all')
  const [selectedResource, setSelectedResource] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedUser, setSelectedUser] = useState<string>('all')
  const [dateRange, setDateRange] = useState<string>('all')
  const [loading, setLoading] = useState(true)

  // Check if user can view activity logs (super_admin and admin)
  const canViewLogs = user?.role === 'super_admin' || user?.role === 'admin'

  // Get store ID for current user
  const storeId = currentStore?.id || user?.storeId || user?.primaryStoreId || import.meta.env.VITE_STORE_ID || 'womanza'

  // Subscribe to real-time activity logs
  useEffect(() => {
    if (!canViewLogs || !storeId) {
      setLoading(false)
      return
    }

    setLoading(true)

    const unsubscribe = activityLogger.subscribeToActivityLogs(
      storeId,
      (newLogs) => {
        setLogs(newLogs)
        setLoading(false)
      },
      {
        limitCount: 1000 // Limit to last 1000 activities
      }
    )

    return () => {
      unsubscribe()
    }
  }, [canViewLogs, storeId])

  // Get unique values for filters
  const actions = Array.from(new Set(logs.map(log => log.action)))
  const resources = Array.from(new Set(logs.map(log => log.resource)))
  const users = Array.from(new Set(logs.map(log => log.userName)))

  // Filter logs based on search and filters
  useEffect(() => {
    let filtered = logs

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(log =>
        log.details.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.resource.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Action filter
    if (selectedAction !== 'all') {
      filtered = filtered.filter(log => log.action === selectedAction)
    }

    // Resource filter
    if (selectedResource !== 'all') {
      filtered = filtered.filter(log => log.resource === selectedResource)
    }

    // Status filter
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(log => log.status === selectedStatus)
    }

    // User filter
    if (selectedUser !== 'all') {
      filtered = filtered.filter(log => log.userName === selectedUser)
    }

    // Date range filter
    if (dateRange !== 'all') {
      const now = new Date()
      let cutoffDate = new Date()

      switch (dateRange) {
        case '1h':
          cutoffDate = new Date(now.getTime() - 60 * 60 * 1000)
          break
        case '24h':
          cutoffDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
          break
        case '7d':
          cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          cutoffDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
      }

      filtered = filtered.filter(log => new Date(log.timestamp) >= cutoffDate)
    }

    setFilteredLogs(filtered)
  }, [logs, searchTerm, selectedAction, selectedResource, selectedStatus, selectedUser, dateRange])

  const getActionIcon = (action: string) => {
    switch (action.toLowerCase()) {
      case 'create': return <Plus className="h-4 w-4 text-green-600" />
      case 'update': return <Edit className="h-4 w-4 text-blue-600" />
      case 'delete': return <Trash2 className="h-4 w-4 text-red-600" />
      case 'view': return <Eye className="h-4 w-4 text-gray-600" />
      case 'login': return <User className="h-4 w-4 text-green-600" />
      case 'login_failed': return <XCircle className="h-4 w-4 text-red-600" />
      case 'logout': return <User className="h-4 w-4 text-gray-600" />
      default: return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  const getResourceIcon = (resource: string) => {
    switch (resource.toLowerCase()) {
      case 'product': return <Package className="h-4 w-4" />
      case 'category': return <Filter className="h-4 w-4" />
      case 'order': return <ShoppingCart className="h-4 w-4" />
      case 'user': return <Users className="h-4 w-4" />
      case 'settings': return <Settings className="h-4 w-4" />
      case 'auth': return <User className="h-4 w-4" />
      default: return <Activity className="h-4 w-4" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Success</Badge>
      case 'error':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Error</Badge>
      case 'warning':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Warning</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Unknown</Badge>
    }
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'super_admin':
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Super Admin</Badge>
      case 'admin':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Admin</Badge>
      case 'editor':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Editor</Badge>
      case 'viewer':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Viewer</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">{role}</Badge>
    }
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
  }

  const handleExport = () => {
    toast.success('Export functionality coming soon')
  }

  const handleRefresh = () => {
    if (!canViewLogs || !storeId) return

    setLoading(true)
    toast.success('Refreshing activity logs...')

    // The real-time subscription will automatically update the data
    // Just show a brief loading state
    setTimeout(() => {
      setLoading(false)
      toast.success('Activity logs refreshed')
    }, 500)
  }

  if (!canViewLogs) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <Activity className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Restricted</h2>
          <p className="text-gray-600">
            You don't have permission to view activity logs.
            Only Super Admins and Admins can access this section.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Activity Logs</h1>
          <p className="text-gray-600 mt-1">
            Track all user activities and system events in your store
          </p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" onClick={handleRefresh} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            {/* Search */}
            <div className="lg:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search activities..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Action Filter */}
            <select
              value={selectedAction}
              onChange={(e) => setSelectedAction(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Actions</option>
              {actions.map(action => (
                <option key={action} value={action}>{action}</option>
              ))}
            </select>

            {/* Resource Filter */}
            <select
              value={selectedResource}
              onChange={(e) => setSelectedResource(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Resources</option>
              {resources.map(resource => (
                <option key={resource} value={resource}>{resource}</option>
              ))}
            </select>

            {/* Status Filter */}
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="success">Success</option>
              <option value="error">Error</option>
              <option value="warning">Warning</option>
            </select>

            {/* Date Range Filter */}
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Time</option>
              <option value="1h">Last Hour</option>
              <option value="24h">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Activity Logs */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Recent Activities ({filteredLogs.length})
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredLogs.length === 0 ? (
              <div className="text-center py-8">
                <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No activity logs found matching your filters.</p>
              </div>
            ) : (
              filteredLogs.map((log) => (
                <div
                  key={log.id}
                  className="flex items-start gap-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex-shrink-0 mt-1">
                    {getActionIcon(log.action)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-gray-900">{log.userName}</span>
                      {getRoleBadge(log.userRole)}
                      <span className="text-gray-500">•</span>
                      <div className="flex items-center gap-1">
                        {getResourceIcon(log.resource)}
                        <span className="text-sm text-gray-600 capitalize">{log.resource}</span>
                      </div>
                      <span className="text-gray-500">•</span>
                      {getStatusBadge(log.status)}
                    </div>
                    
                    <p className="text-gray-700 mb-2">{log.details}</p>
                    
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {formatTimestamp(log.timestamp)}
                      </div>
                      <span>IP: {log.ipAddress}</span>
                      {log.resourceId && <span>ID: {log.resourceId}</span>}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
