import { useEffect, ReactNode } from 'react'
import { useThemeStore } from '../../store/theme'
import { useFirebaseAuthStore } from '../../store/firebase-auth'

interface ThemeProviderProps {
  children: ReactNode
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const { user } = useFirebaseAuthStore()
  const { loadTheme, applyTheme, currentTheme } = useThemeStore()

  useEffect(() => {
    // Load theme when user is authenticated
    if (user?.storeId) {
      loadTheme(user.storeId)
    }
  }, [user, loadTheme])

  useEffect(() => {
    // Apply theme when it changes
    if (currentTheme) {
      applyTheme(currentTheme)
    }
  }, [currentTheme, applyTheme])

  return <>{children}</>
}

// Theme customization component for admin settings
export function ThemeCustomizer() {
  const { currentTheme, updateTheme, isLoading } = useThemeStore()

  if (!currentTheme) return null

  const handleColorChange = (colorType: string, value: string) => {
    updateTheme({ [colorType]: value })
  }

  const handleFontChange = (fontFamily: string) => {
    updateTheme({ font_family: fontFamily })
  }

  const handleRadiusChange = (borderRadius: 'none' | 'sm' | 'md' | 'lg' | 'xl') => {
    updateTheme({ border_radius: borderRadius })
  }

  const handleButtonStyleChange = (buttonStyle: 'rounded' | 'square' | 'pill') => {
    updateTheme({ button_style: buttonStyle })
  }

  return (
    <div className="space-y-6 p-6 bg-white rounded-lg shadow-sm border">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Store Theme Customization</h3>
        <p className="text-sm text-gray-600 mb-6">
          Customize your store's appearance. Changes will be applied immediately.
        </p>
      </div>

      {/* Color Settings */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Colors</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Primary Color
            </label>
            <div className="flex items-center space-x-3">
              <input
                type="color"
                value={currentTheme.primary_color}
                onChange={(e) => handleColorChange('primary_color', e.target.value)}
                className="w-12 h-10 rounded border border-gray-300"
                disabled={isLoading}
              />
              <input
                type="text"
                value={currentTheme.primary_color}
                onChange={(e) => handleColorChange('primary_color', e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                placeholder="#2563eb"
                disabled={isLoading}
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Secondary Color
            </label>
            <div className="flex items-center space-x-3">
              <input
                type="color"
                value={currentTheme.secondary_color}
                onChange={(e) => handleColorChange('secondary_color', e.target.value)}
                className="w-12 h-10 rounded border border-gray-300"
                disabled={isLoading}
              />
              <input
                type="text"
                value={currentTheme.secondary_color}
                onChange={(e) => handleColorChange('secondary_color', e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                placeholder="#9333ea"
                disabled={isLoading}
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Accent Color
            </label>
            <div className="flex items-center space-x-3">
              <input
                type="color"
                value={currentTheme.accent_color}
                onChange={(e) => handleColorChange('accent_color', e.target.value)}
                className="w-12 h-10 rounded border border-gray-300"
                disabled={isLoading}
              />
              <input
                type="text"
                value={currentTheme.accent_color}
                onChange={(e) => handleColorChange('accent_color', e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                placeholder="#06b6d4"
                disabled={isLoading}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Typography Settings */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Typography</h4>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Font Family
          </label>
          <select
            value={currentTheme.font_family}
            onChange={(e) => handleFontChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            disabled={isLoading}
          >
            <option value="Inter">Inter</option>
            <option value="Roboto">Roboto</option>
            <option value="Open Sans">Open Sans</option>
            <option value="Lato">Lato</option>
            <option value="Poppins">Poppins</option>
            <option value="Montserrat">Montserrat</option>
          </select>
        </div>
      </div>

      {/* Style Settings */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Style</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Border Radius
            </label>
            <select
              value={currentTheme.border_radius}
              onChange={(e) => handleRadiusChange(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              disabled={isLoading}
            >
              <option value="none">None</option>
              <option value="sm">Small</option>
              <option value="md">Medium</option>
              <option value="lg">Large</option>
              <option value="xl">Extra Large</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Button Style
            </label>
            <select
              value={currentTheme.button_style}
              onChange={(e) => handleButtonStyleChange(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              disabled={isLoading}
            >
              <option value="rounded">Rounded</option>
              <option value="square">Square</option>
              <option value="pill">Pill</option>
            </select>
          </div>
        </div>
      </div>

      {/* Preview */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Preview</h4>
        <div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
          <div className="space-y-3">
            <button 
              className="px-4 py-2 text-white font-medium"
              style={{ 
                backgroundColor: currentTheme.primary_color,
                borderRadius: currentTheme.border_radius === 'none' ? '0' :
                            currentTheme.border_radius === 'sm' ? '0.125rem' :
                            currentTheme.border_radius === 'md' ? '0.375rem' :
                            currentTheme.border_radius === 'lg' ? '0.5rem' : '0.75rem'
              }}
            >
              Primary Button
            </button>
            <button 
              className="px-4 py-2 text-white font-medium ml-3"
              style={{ 
                backgroundColor: currentTheme.secondary_color,
                borderRadius: currentTheme.button_style === 'pill' ? '9999px' :
                            currentTheme.button_style === 'square' ? '0' : '0.375rem'
              }}
            >
              Secondary Button
            </button>
          </div>
        </div>
      </div>

      {isLoading && (
        <div className="text-sm text-gray-500">
          Saving changes...
        </div>
      )}
    </div>
  )
}
