import { initializeApp } from 'firebase/app'
import { getAuth, connectAuthEmulator } from 'firebase/auth'
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore'
import { getStorage, connectStorageEmulator } from 'firebase/storage'

// Firebase configuration - Updated to match integration specifications
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || import.meta.env.PUBLIC_FIREBASE_API_KEY || 'demo-api-key',
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || import.meta.env.PUBLIC_FIREBASE_AUTH_DOMAIN || 'demo-project.firebaseapp.com',
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || import.meta.env.PUBLIC_FIREBASE_PROJECT_ID || 'demo-project',
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || import.meta.env.PUBLIC_FIREBASE_STORAGE_BUCKET || 'demo-project.appspot.com',
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || import.meta.env.PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '123456789',
  appId: import.meta.env.VITE_FIREBASE_APP_ID || import.meta.env.PUBLIC_FIREBASE_APP_ID || '1:123456789:web:abcdef123456'
}

// Check if we have real credentials
const hasValidCredentials = firebaseConfig.apiKey !== 'demo-api-key' &&
                           firebaseConfig.authDomain !== 'demo-project.firebaseapp.com' &&
                           firebaseConfig.projectId !== 'demo-project'

if (!hasValidCredentials) {
  console.warn('⚠️ Firebase environment variables not configured. Using emulator mode.')
}

// Initialize Firebase
export const app = initializeApp(firebaseConfig)

// Initialize Firebase services
export const auth = getAuth(app)
export const db = getFirestore(app)
export const storage = getStorage(app)

// Connect to emulators in development
const isEmulatorMode = import.meta.env.VITE_USE_FIREBASE_EMULATOR === 'true'

if (isEmulatorMode && !auth.emulatorConfig) {
  console.log('🔧 Connecting to Firebase emulators...')

  try {
    connectAuthEmulator(auth, 'http://localhost:9099', { disableWarnings: true })
    connectFirestoreEmulator(db, 'localhost', 8080)
    connectStorageEmulator(storage, 'localhost', 9199)
    console.log('✅ Connected to Firebase emulators')
  } catch (error) {
    console.warn('⚠️ Could not connect to emulators:', error)
  }
} else if (hasValidCredentials) {
  console.log('🔥 Connected to Firebase production:', firebaseConfig.projectId)

  // Test Firebase connectivity
  auth.onAuthStateChanged(() => {
    console.log('🔥 Firebase Auth connection established')
  }, (error) => {
    console.error('🔥 Firebase Auth connection error:', error)
  })
} else {
  console.warn('⚠️ No Firebase credentials found. App will use mock data.')
}

// Add network status monitoring
if (typeof window !== 'undefined') {
  window.addEventListener('online', () => {
    console.log('🌐 Network connection restored')
  })

  window.addEventListener('offline', () => {
    console.warn('🌐 Network connection lost')
  })
}

// Export configuration flags
export const isFirebaseConfigured = hasValidCredentials
export const isUsingEmulator = isEmulatorMode

// Database types for Firebase (converted from Supabase types)
export interface AdminUser {
  id: string
  uid?: string // Firebase UID (optional for backward compatibility)
  email: string
  name?: string // Display name (optional for backward compatibility)
  fullName: string
  role: 'super_admin' | 'admin' | 'editor' | 'viewer' | 'app_admin'
  createdBy: string | null
  createdAt: string
  updatedAt?: string
  storeIds: string[] // Array of store IDs user has access to
  primaryStoreId?: string | null // Primary store ID
  storeId?: string // Legacy store ID (for backward compatibility)
  activeStoreId?: string // Currently selected store
  active: boolean
  lastLoginAt: string | null
  avatarUrl: string | null
  phone?: string | null
  profile?: {
    firstName?: string
    lastName?: string
    phone?: string
    avatar?: string
  }
  currentStore?: {
    id: string
    name: string
    slug: string
  }
  permissions?: {
    canManageUsers?: boolean
    canManageProducts?: boolean
    canManageOrders?: boolean
    canViewAnalytics?: boolean
    canManageSettings?: boolean
    canManageCategories?: boolean
    canManageMarketing?: boolean
    canExportData?: boolean
  }
}

export interface Store {
  id: string
  name: string
  slug: string
  domain: string | null
  description: string | null
  contactEmail: string | null
  contactPhone: string | null
  currency: string
  country: string
  logoUrl: string | null
  primaryColor: string
  secondaryColor: string
  taxRate: number
  status: 'active' | 'suspended' | 'pending'
  ownerId: string
  settings: {
    timezone?: string
    language?: string
    theme?: string
    [key: string]: any
  }
  createdAt: string
  updatedAt: string
}

export interface PendingStoreRequest {
  id: string
  owner: {
    email: string
    fullName: string
    uid: string
  }
  store: {
    name: string
    slug: string
    currency: string
    country: string
    description?: string
  }
  status: 'pending' | 'approved' | 'rejected'
  createdAt: string
  reviewedAt?: string
  reviewedBy?: string
  rejectionReason?: string
}

export interface Product {
  id: string
  storeId: string
  categoryId: string | null
  name: string
  slug: string
  description: string | null
  shortDescription: string | null
  sku: string | null
  barcode: string | null
  price: number
  compareAtPrice: number | null
  costPrice: number | null
  inventoryQuantity: number
  lowStockThreshold: number
  trackInventory: boolean
  status: 'draft' | 'active' | 'archived'
  isFeatured: boolean
  requiresShipping: boolean
  weightGrams: number | null
  dimensions: any
  images: any[]
  variants: any[]
  options: any[]
  seoTitle: string | null
  seoDescription: string | null
  vendor: string | null
  productType: string | null
  publishedAt: string | null
  createdAt: string
  updatedAt: string
  categories?: {
    name: string
  }
}

export interface Category {
  id: string
  storeId: string
  name: string
  slug: string
  description: string | null
  imageUrl: string | null
  sortOrder: number
  createdAt: string
  updatedAt: string
}

export interface Order {
  id: string
  storeId: string
  customerId: string | null
  orderNumber: string
  status: string
  financialStatus: string
  fulfillmentStatus: string
  subtotal: number
  taxAmount: number
  shippingAmount: number
  total: number
  customerEmail: string | null
  billingAddress: any
  shippingAddress: any
  shippingMethod: string | null
  paymentMethod: string | null
  notes: string | null
  processedAt: string | null
  shippedAt: string | null
  deliveredAt: string | null
  createdAt: string
  updatedAt: string
}

export interface Customer {
  id: string
  storeId?: string
  fullName: string
  email: string
  phone?: string | null
  isRegistered: boolean // true = frontend account, false = manual entry
  group: string | null // VIP, Retail, Wholesale, etc.
  lifetimeValue: number
  orderCount: number
  notes: string
  tags: string[]

  // Additional fields for enhanced functionality
  firstName?: string | null
  lastName?: string | null
  dateOfBirth?: string | null
  gender?: string | null
  address?: {
    shipping?: {
      line1?: string
      city?: string
      zip?: string
      country?: string
    }
    billing?: {
      line1?: string
      city?: string
      zip?: string
      country?: string
    }
  }
  preferences?: any
  totalSpent?: number
  totalOrders?: number
  loyaltyPoints?: number
  lastOrderDate?: string | null
  status: 'active' | 'inactive' | 'blocked'
  groups?: string[] // For backward compatibility
  marketingOptIn?: boolean
  preferredLanguage?: string
  timezone?: string
  createdAt: string
  updatedAt: string
}

export interface Tag {
  id: string
  storeId: string
  name: string
  slug: string
  color: string
  createdAt: string
  updatedAt: string
}
