import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'

import { Button } from '../components/ui/button'
import { Badge } from '../components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'

import {
  ArrowLeft,
  Edit,
  Trash2,
  Users,
  DollarSign,
  ShoppingCart,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Star,
  Crown,
  Award,
  Gift,
  BarChart3,
  Activity,
  Heart,
  AlertTriangle,
  CheckCircle,
  Clock,
  Eye,
  FileText,
  MessageSquare
} from 'lucide-react'

import { dataService } from '../lib/data-service'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { formatCurrency, formatDateTime, cn } from '../lib/utils'

interface Customer {
  id: string
  email: string
  first_name: string
  last_name: string
  full_name: string
  phone?: string
  date_of_birth?: string
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say'
  avatar?: string
  
  address: {
    street?: string
    city?: string
    state?: string
    zipCode?: string
    country?: string
  }
  
  preferences: {
    emailMarketing: boolean
    smsMarketing: boolean
    pushNotifications: boolean
    language: string
    currency: string
    favoriteCategories: string[]
  }
  
  total_spent: number
  total_orders: number
  average_order_value: number
  lifetime_value: number
  
  status: 'active' | 'inactive' | 'blocked' | 'vip'
  customer_group: 'regular' | 'vip' | 'wholesale' | 'new' | 'at_risk' | 'loyal'
  loyalty_points: number
  loyalty_tier: 'bronze' | 'silver' | 'gold' | 'platinum'
  
  last_login?: string
  last_order_date?: string
  first_order_date?: string
  order_frequency: number
  return_rate: number
  referral_count: number
  
  email_opens: number
  email_clicks: number
  support_tickets: number
  notes: string[]
  
  created_at: string
  updated_at: string
  
  days_since_last_order?: number
  customer_lifetime_days?: number
  is_repeat_customer: boolean
  risk_score: number
}

export function CustomerDetailPage() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { hasPermission } = useFirebaseAuthStore()
  
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')

  const canEdit = hasPermission(['super_admin', 'admin', 'editor'])
  const canDelete = hasPermission(['super_admin', 'admin'])

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Active</Badge>
      case 'inactive':
        return <Badge className="bg-gray-100 text-gray-800"><Clock className="h-3 w-3 mr-1" />Inactive</Badge>
      case 'blocked':
        return <Badge variant="destructive"><AlertTriangle className="h-3 w-3 mr-1" />Blocked</Badge>
      case 'vip':
        return <Badge className="bg-purple-100 text-purple-800"><Crown className="h-3 w-3 mr-1" />VIP</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getGroupBadge = (group: string) => {
    switch (group) {
      case 'vip':
        return <Badge className="bg-purple-100 text-purple-800"><Crown className="h-3 w-3 mr-1" />VIP</Badge>
      case 'loyal':
        return <Badge className="bg-blue-100 text-blue-800"><Heart className="h-3 w-3 mr-1" />Loyal</Badge>
      case 'new':
        return <Badge className="bg-green-100 text-green-800"><Star className="h-3 w-3 mr-1" />New</Badge>
      case 'at_risk':
        return <Badge className="bg-red-100 text-red-800"><AlertTriangle className="h-3 w-3 mr-1" />At Risk</Badge>
      case 'wholesale':
        return <Badge className="bg-orange-100 text-orange-800">Wholesale</Badge>
      default:
        return <Badge variant="outline">{group}</Badge>
    }
  }

  const getTierBadge = (tier: string) => {
    switch (tier) {
      case 'platinum':
        return <Badge className="bg-gray-800 text-white"><Crown className="h-3 w-3 mr-1" />Platinum</Badge>
      case 'gold':
        return <Badge className="bg-yellow-100 text-yellow-800"><Award className="h-3 w-3 mr-1" />Gold</Badge>
      case 'silver':
        return <Badge className="bg-gray-100 text-gray-800"><Award className="h-3 w-3 mr-1" />Silver</Badge>
      case 'bronze':
        return <Badge className="bg-orange-100 text-orange-800"><Award className="h-3 w-3 mr-1" />Bronze</Badge>
      default:
        return <Badge variant="outline">{tier}</Badge>
    }
  }

  const getRiskIndicator = (riskScore: number) => {
    if (riskScore >= 70) {
      return <Badge variant="destructive" className="text-xs"><AlertTriangle className="h-3 w-3 mr-1" />High Risk</Badge>
    } else if (riskScore >= 40) {
      return <Badge className="bg-yellow-100 text-yellow-800 text-xs"><Clock className="h-3 w-3 mr-1" />Medium Risk</Badge>
    } else {
      return <Badge className="bg-green-100 text-green-800 text-xs"><CheckCircle className="h-3 w-3 mr-1" />Low Risk</Badge>
    }
  }

  const fetchCustomer = async (customerId: string) => {
    try {
      setLoading(true)
      // Mock customer data for demonstration
      const mockCustomer: Customer = {
        id: customerId,
        email: '<EMAIL>',
        first_name: 'Sarah',
        last_name: 'Johnson',
        full_name: 'Sarah Johnson',
        phone: '+****************',
        date_of_birth: '1985-03-15',
        gender: 'female',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        address: {
          street: '123 Oak Street',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'USA'
        },
        preferences: {
          emailMarketing: true,
          smsMarketing: false,
          pushNotifications: true,
          language: 'en',
          currency: 'USD',
          favoriteCategories: ['jewelry', 'accessories']
        },
        total_spent: 2450.75,
        total_orders: 12,
        average_order_value: 204.23,
        lifetime_value: 2450.75,
        status: 'active',
        customer_group: 'vip',
        loyalty_points: 2451,
        loyalty_tier: 'gold',
        last_login: new Date(Date.now() - 86400000).toISOString(),
        last_order_date: new Date(Date.now() - 172800000).toISOString(),
        first_order_date: new Date(Date.now() - 31536000000).toISOString(),
        order_frequency: 1.2,
        return_rate: 5.5,
        referral_count: 3,
        email_opens: 45,
        email_clicks: 12,
        support_tickets: 2,
        notes: ['VIP customer', 'Prefers gold jewelry', 'Birthday: March 15'],
        created_at: new Date(Date.now() - 31536000000).toISOString(),
        updated_at: new Date().toISOString(),
        days_since_last_order: 2,
        customer_lifetime_days: 365,
        is_repeat_customer: true,
        risk_score: 15
      }
      setCustomer(mockCustomer)
    } catch (error) {
      console.error('Error fetching customer:', error)
      toast.error('Failed to load customer')
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async () => {
    if (!customer || !canDelete) return
    
    if (window.confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
      try {
        console.log('Delete customer:', customer.id)
        toast.success('Customer deleted successfully')
        navigate('/admin/customers')
      } catch (error) {
        console.error('Error deleting customer:', error)
        toast.error('Failed to delete customer')
      }
    }
  }

  useEffect(() => {
    if (id) {
      fetchCustomer(id)
    }
  }, [id])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading customer...</p>
        </div>
      </div>
    )
  }

  if (!customer) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Customer Not Found</h2>
          <p className="text-gray-500 mb-4">The customer you are looking for does not exist.</p>
          <Button onClick={() => navigate('/admin/customers')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/admin/customers')}
            className="hover:bg-gray-100"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex items-center gap-4">
            {customer.avatar ? (
              <img 
                src={customer.avatar} 
                alt={customer.full_name}
                className="w-16 h-16 rounded-full object-cover"
              />
            ) : (
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                {customer.full_name.charAt(0).toUpperCase()}
              </div>
            )}
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{customer.full_name}</h1>
              <p className="text-gray-500">{customer.email}</p>
              <div className="flex items-center gap-2 mt-1">
                {getStatusBadge(customer.status)}
                {getGroupBadge(customer.customer_group)}
                {getTierBadge(customer.loyalty_tier)}
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-3">
          {canEdit && (
            <Button
              onClick={() => {
                console.log('Edit customer:', customer.id)
                toast.success('Edit functionality coming soon')
              }}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit Customer
            </Button>
          )}
          {canDelete && (
            <Button
              variant="destructive"
              onClick={handleDelete}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          )}
        </div>
      </div>

      {/* Customer Detail Content */}
      <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
        {/* Main Content */}
        <div className="xl:col-span-3 space-y-6">
          {/* Customer Information & Contact */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">First Name</label>
                    <p className="text-gray-900">{customer.first_name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Last Name</label>
                    <p className="text-gray-900">{customer.last_name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Email</label>
                    <p className="text-gray-900">{customer.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Phone</label>
                    <p className="text-gray-900">{customer.phone || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Date of Birth</label>
                    <p className="text-gray-900">
                      {customer.date_of_birth ? new Date(customer.date_of_birth).toLocaleDateString() : 'Not provided'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Gender</label>
                    <p className="text-gray-900 capitalize">{customer.gender || 'Not specified'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Address Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                {customer.address.street ? (
                  <div className="space-y-2">
                    <p className="text-gray-900">{customer.address.street}</p>
                    <p className="text-gray-900">
                      {customer.address.city}, {customer.address.state} {customer.address.zipCode}
                    </p>
                    <p className="text-gray-900">{customer.address.country}</p>
                  </div>
                ) : (
                  <p className="text-gray-500">No address information available</p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Customer Metrics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total Orders</span>
                <span className="font-semibold">{customer.total_orders}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total Spent</span>
                <span className="font-semibold text-green-600">{formatCurrency(customer.total_spent)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Avg Order Value</span>
                <span className="font-semibold">{formatCurrency(customer.average_order_value)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Lifetime Value</span>
                <span className="font-semibold text-purple-600">{formatCurrency(customer.lifetime_value)}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-base">Risk Assessment</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                {getRiskIndicator(customer.risk_score)}
                <div className="mt-2">
                  <div className="text-lg font-semibold">
                    {customer.risk_score}/100
                  </div>
                  <div className="text-sm text-gray-500">Risk Score</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
