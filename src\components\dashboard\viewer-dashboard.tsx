import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { useDashboardData } from '../../hooks/useDashboardData'
import { useStoreManagement } from '../../store/store-management'
import {
  Package,
  ShoppingCart,
  Users,
  RefreshCw,
  Eye,
  Lock
} from 'lucide-react'
import { RealTimeIndicator } from '../ui/real-time-indicator'
import { formatNumber, getStatusColor, getStatusLabel } from '../../lib/utils'

export function ViewerDashboard() {
  const { stats, recentOrders, topProducts, loading, error, refetch } = useDashboardData()
  const { currentStore } = useStoreManagement()

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-500 mb-2">Error loading dashboard data</p>
          <Button onClick={refetch} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            Viewer Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Read-only access to store data
          </p>
        </div>
        <div className="flex items-center gap-3">
          <RealTimeIndicator />
          <Button onClick={refetch} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview Metrics Only */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats?.totalOrders || 0)}</div>
            <p className="text-xs text-muted-foreground">
              Total orders in system
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats?.totalProducts || 0)}</div>
            <p className="text-xs text-muted-foreground">
              Total products available
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats?.totalCustomers || 0)}</div>
            <p className="text-xs text-muted-foreground">
              Total customers
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Data Tables in View-Only Mode */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Recent Orders
            </CardTitle>
            <CardDescription>View-only access to order data</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentOrders?.length > 0 ? (
                recentOrders.slice(0, 5).map((order) => (
                  <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg bg-gray-50 dark:bg-gray-800">
                    <div>
                      <p className="font-medium text-gray-700 dark:text-gray-300">#{order.orderNumber}</p>
                      <p className="text-sm text-gray-500">{order.customerEmail}</p>
                    </div>
                    <div className="text-right">
                      <Badge variant="secondary" className={getStatusColor(order.status)}>
                        {getStatusLabel(order.status)}
                      </Badge>
                      <div className="flex items-center gap-1 mt-1">
                        <Eye className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-400">View only</span>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">No recent orders</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Products Overview
            </CardTitle>
            <CardDescription>View-only access to product data</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topProducts?.length > 0 ? (
                topProducts.slice(0, 5).map((product) => (
                  <div key={product.id} className="flex items-center justify-between p-3 border rounded-lg bg-gray-50 dark:bg-gray-800">
                    <div>
                      <p className="font-medium text-gray-700 dark:text-gray-300">{product.name}</p>
                      <p className="text-sm text-gray-500">SKU: {product.sku}</p>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center gap-1">
                        <Eye className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-400">View only</span>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">No products available</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Viewer Limitations Notice */}
      <Card className="border-blue-200 bg-blue-50 dark:bg-blue-900/20">
        <CardHeader>
          <CardTitle className="text-blue-800 dark:text-blue-200 flex items-center gap-2">
            <Lock className="h-5 w-5" />
            Viewer Access Level
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-blue-700 dark:text-blue-300 space-y-2">
            <p className="font-medium">You have viewer access with the following permissions:</p>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>👁️ View products, orders, and customers</li>
              <li>📊 Access to basic overview metrics</li>
              <li>❌ No write actions (create, edit, delete)</li>
              <li>❌ No access to analytics or revenue data</li>
              <li>❌ No access to marketing, settings, or staff management</li>
              <li>❌ No file storage or export capabilities</li>
            </ul>
            <p className="text-sm mt-3">
              All data is displayed in read-only mode. Contact your store administrator to request additional permissions.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Quick Navigation for Viewers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Available Views
          </CardTitle>
          <CardDescription>
            Navigate to different sections you can view
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="/admin/products" className="block">
              <div className="p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                <div className="flex items-center gap-3">
                  <Package className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="font-medium">Products</p>
                    <p className="text-sm text-gray-500">View product catalog</p>
                  </div>
                </div>
              </div>
            </a>
            
            <a href="/admin/orders" className="block">
              <div className="p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                <div className="flex items-center gap-3">
                  <ShoppingCart className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="font-medium">Orders</p>
                    <p className="text-sm text-gray-500">View order history</p>
                  </div>
                </div>
              </div>
            </a>
            
            <a href="/admin/customers" className="block">
              <div className="p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                <div className="flex items-center gap-3">
                  <Users className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="font-medium">Customers</p>
                    <p className="text-sm text-gray-500">View customer list</p>
                  </div>
                </div>
              </div>
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
