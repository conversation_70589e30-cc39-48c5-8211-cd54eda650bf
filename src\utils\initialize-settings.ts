/**
 * Client-side Settings Initialization Utility
 * 
 * This utility can be used to initialize default settings for a store
 * from within the application (browser environment)
 */

import { doc, setDoc, serverTimestamp, getDoc } from 'firebase/firestore'
import { db } from '../lib/firebase'
import { toast } from 'react-hot-toast'

// Default settings structure
export const defaultSettingsStructure = {
  general: {
    storeName: 'My Store',
    storeSlug: 'my-store',
    description: 'Your online store powered by Womanza',
    currency: 'PKR',
    locale: 'en-PK',
    timeZone: 'Asia/Karachi',
    status: 'active' as const,
    contactEmail: '',
    contactPhone: '',
    address: '',
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  
  branding: {
    logoUrl: '',
    faviconUrl: '',
    bannerUrl: '',
    primaryColor: '#3B82F6',
    accentColor: '#8B5CF6',
    layout: 'full-width' as const,
    typography: 'Inter',
    theme: 'auto' as const,
    customCSS: '',
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  
  seo: {
    metaTitle: '',
    metaDescription: '',
    keywords: '',
    googleAnalyticsId: '',
    facebookPixelId: '',
    gtmId: '',
    sitemapUrl: '/sitemap.xml',
    robotsTxt: 'User-agent: *\nAllow: /',
    structuredData: {},
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  
  auth: {
    allowPublicSignup: true,
    requireEmailVerification: false,
    enableTwoFactor: false,
    sessionTimeout: 1440,
    maxLoginAttempts: 5,
    lockoutDuration: 30,
    passwordPolicy: {
      minLength: 8,
      requireUppercase: false,
      requireNumbers: false,
      requireSpecialChars: false
    },
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  
  emailConfig: {
    fromEmail: '',
    fromName: '',
    smtpHost: '',
    smtpPort: 587,
    smtpUser: '',
    smtpPassword: '',
    orderNotifications: true,
    marketingEmails: false,
    templates: {
      orderConfirmation: '',
      orderShipped: '',
      passwordReset: '',
      welcome: ''
    },
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  
  payment: {
    currency: 'PKR',
    enableCOD: true,
    enableStripe: false,
    enablePayPal: false,
    stripePublishableKey: '',
    stripeSecretKey: '',
    paypalClientId: '',
    paypalClientSecret: '',
    testMode: true,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  
  shipping: {
    enabled: true,
    freeShippingThreshold: 0,
    defaultCost: 0,
    zones: [],
    methods: [],
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  
  tax: {
    enabled: false,
    defaultRate: 0,
    displayMode: 'inclusive' as const,
    regions: [],
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  
  compliance: {
    privacyPolicyUrl: '/privacy-policy',
    termsOfServiceUrl: '/terms-of-service',
    refundPolicyUrl: '/refund-policy',
    gdprEnabled: false,
    cookieConsent: false,
    dataRetentionDays: 365,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  
  behavior: {
    allowGuestCheckout: true,
    enableWishlist: true,
    enableReviews: true,
    enableLiveChat: false,
    enableNewsletter: true,
    enableSocialSharing: true,
    maintenanceMode: false,
    maintenanceMessage: 'We are currently under maintenance. Please check back soon.',
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  },
  
  panelPreferences: {
    itemsPerPage: 25,
    defaultView: 'grid' as const,
    showTooltips: true,
    autoSaveDrafts: true,
    compactMode: false,
    theme: 'auto' as const,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  }
}

/**
 * Initialize default settings for a store
 */
export async function initializeStoreSettings(storeId: string): Promise<void> {
  // Validate storeId parameter
  if (!storeId || typeof storeId !== 'string') {
    throw new Error(`Invalid storeId: ${storeId}. Must be a non-empty string.`)
  }

  console.log(`🚀 Initializing settings for store: ${storeId}`)

  try {
    const storeSettingsRef = doc(db, 'stores', storeId)
    
    // Create each settings category as a separate document
    for (const [category, defaultData] of Object.entries(defaultSettingsStructure)) {
      const categoryRef = doc(db, 'stores', storeId, 'settings', category)
      const categoryDoc = await getDoc(categoryRef)
      
      if (!categoryDoc.exists()) {
        // Create new document with default data
        await setDoc(categoryRef, defaultData)
        console.log(`  ✅ Created ${category} settings`)
      } else {
        // Update existing document with new fields (merge)
        const updateData = {
          ...defaultData,
          updatedAt: serverTimestamp()
        }
        delete (updateData as any).createdAt // Don't overwrite creation timestamp
        
        await setDoc(categoryRef, updateData, { merge: true })
        console.log(`  🔄 Updated ${category} settings`)
      }
    }
    
    // Create store metadata document
    const storeMetaDoc = await getDoc(storeSettingsRef)
    
    if (!storeMetaDoc.exists()) {
      await setDoc(storeSettingsRef, {
        id: storeId,
        name: defaultSettingsStructure.general.storeName,
        slug: defaultSettingsStructure.general.storeSlug,
        status: 'active',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        settingsVersion: '1.0.0'
      })
      console.log(`  ✅ Created store metadata`)
    }
    
    console.log(`✅ Successfully initialized settings for store: ${storeId}`)
    toast.success(`Settings initialized for ${storeId}`)
    
  } catch (error) {
    console.error(`❌ Error initializing store ${storeId}:`, error)
    toast.error(`Failed to initialize settings for ${storeId}`)
    throw error
  }
}

/**
 * Initialize settings for multiple stores
 */
export async function initializeMultipleStores(storeIds: string[]): Promise<void> {
  // Validate storeIds parameter
  if (!Array.isArray(storeIds)) {
    throw new Error(`Invalid storeIds: ${storeIds}. Must be an array of strings.`)
  }

  console.log('🚀 Initializing settings for multiple stores...')

  for (const storeId of storeIds) {
    try {
      await initializeStoreSettings(storeId)
    } catch (error) {
      console.error(`Failed to initialize ${storeId}, continuing with others...`, error)
    }
  }

  console.log('🎉 Multi-store initialization complete!')
}

/**
 * Check if store settings exist
 */
export async function checkStoreSettings(storeId: string): Promise<boolean> {
  try {
    const generalRef = doc(db, 'stores', storeId, 'settings', 'general')
    const generalDoc = await getDoc(generalRef)
    return generalDoc.exists()
  } catch (error) {
    console.error('Error checking store settings:', error)
    return false
  }
}

// Export for browser console usage
if (typeof window !== 'undefined') {
  (window as any).womanzaSettings = {
    initializeStoreSettings,
    initializeMultipleStores,
    checkStoreSettings
  }

  console.log('🔧 Settings initialization utilities available:')
  console.log('- womanzaSettings.initializeStoreSettings("store-id")')
  console.log('- womanzaSettings.initializeMultipleStores(["store1", "store2"])')
  console.log('- womanzaSettings.checkStoreSettings("store-id")')
}
