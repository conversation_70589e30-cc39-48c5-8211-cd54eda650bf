import { create } from 'zustand'
import {
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  startAfter,
  Timestamp
} from 'firebase/firestore'
import { db } from '../lib/firebase'
import { useStoreManagement } from './store-management'

// Get current store ID dynamically
const getCurrentStoreId = () => {
  return useStoreManagement.getState().currentStoreId || import.meta.env.VITE_STORE_ID || 'womanza'
}

export interface DashboardMetrics {
  totalRevenue: number
  totalOrders: number
  totalCustomers: number
  totalProducts: number
  averageOrderValue: number
  conversionRate: number
  revenueGrowth: number
  ordersGrowth: number
  customersGrowth: number
}

export interface SalesData {
  date: string
  revenue: number
  orders: number
  customers: number
}

export interface TopProduct {
  id: string
  name: string
  revenue: number
  orders: number
  quantity: number
}

export interface TopCustomer {
  id: string
  name: string
  email: string
  totalSpent: number
  ordersCount: number
}

export interface RecentOrder {
  id: string
  orderNumber: string
  customerName: string
  total: number
  status: string
  createdAt: string
}

interface AnalyticsState {
  metrics: DashboardMetrics | null
  salesData: SalesData[]
  topProducts: TopProduct[]
  topCustomers: TopCustomer[]
  recentOrders: RecentOrder[]
  loading: boolean
  error: string | null
  // Actions
  fetchDashboardMetrics: (period?: 'today' | '7days' | '30days' | '90days') => Promise<void>
  fetchSalesData: (period?: 'today' | '7days' | '30days' | '90days') => Promise<void>
  fetchTopProducts: (period?: 'today' | '7days' | '30days' | '90days') => Promise<void>
  fetchTopCustomers: () => Promise<void>
  fetchRecentOrders: (limit?: number) => Promise<void>
  clearError: () => void
}

export const useAnalyticsStore = create<AnalyticsState>((set, get) => ({
  metrics: null,
  salesData: [],
  topProducts: [],
  topCustomers: [],
  recentOrders: [],
  loading: false,
  error: null,

  clearError: () => set({ error: null }),

  fetchDashboardMetrics: async (period = '30days') => {
    set({ loading: true, error: null })
    try {
      const now = new Date()
      let startDate: Date
      
      switch (period) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          break
        case '7days':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30days':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case '90days':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
        default:
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      }

      const currentStoreId = getCurrentStoreId()

      // Fetch orders for the period from store-specific collection
      const ordersQuery = query(
        collection(db, 'stores', currentStoreId, 'orders'),
        where('createdAt', '>=', Timestamp.fromDate(startDate)),
        orderBy('createdAt', 'desc')
      )
      
      const ordersSnapshot = await getDocs(ordersQuery)
      const orders = ordersSnapshot.docs.map(doc => doc.data()).filter(order =>
        order.status !== 'cancelled' && order.status !== 'refunded'
      )
      
      // Fetch customers from store-specific collection
      const customersQuery = query(
        collection(db, 'stores', currentStoreId, 'customers')
      )
      
      const customersSnapshot = await getDocs(customersQuery)
      
      // Fetch products from store-specific collection
      const productsQuery = query(
        collection(db, 'stores', currentStoreId, 'products')
      )
      
      const productsSnapshot = await getDocs(productsQuery)
      
      // Calculate metrics
      const totalRevenue = orders.reduce((sum, order) => sum + (order.total || 0), 0)
      const totalOrders = orders.length
      const totalCustomers = customersSnapshot.size
      const totalProducts = productsSnapshot.size
      const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0
      
      // Calculate growth (compare with previous period) - simplified
      const previousStartDate = new Date(startDate.getTime() - (now.getTime() - startDate.getTime()))
      const previousOrdersQuery = query(
        collection(db, 'stores', currentStoreId, 'orders'),
        where('createdAt', '>=', Timestamp.fromDate(previousStartDate)),
        where('createdAt', '<', Timestamp.fromDate(startDate)),
        orderBy('createdAt', 'desc')
      )
      
      const previousOrdersSnapshot = await getDocs(previousOrdersQuery)
      const previousOrders = previousOrdersSnapshot.docs.map(doc => doc.data()).filter(order =>
        order.status !== 'cancelled' && order.status !== 'refunded'
      )
      const previousRevenue = previousOrders.reduce((sum, order) => sum + (order.total || 0), 0)
      const previousOrdersCount = previousOrders.length
      
      const revenueGrowth = previousRevenue > 0 ? ((totalRevenue - previousRevenue) / previousRevenue) * 100 : 0
      const ordersGrowth = previousOrdersCount > 0 ? ((totalOrders - previousOrdersCount) / previousOrdersCount) * 100 : 0
      
      const metrics: DashboardMetrics = {
        totalRevenue,
        totalOrders,
        totalCustomers,
        totalProducts,
        averageOrderValue,
        conversionRate: 0, // Would need website analytics data
        revenueGrowth,
        ordersGrowth,
        customersGrowth: 0 // Would need to track customer creation dates
      }
      
      set({ metrics, loading: false })
    } catch (error: any) {
      console.error('Error fetching dashboard metrics:', error)
      set({ error: error.message, loading: false })
    }
  },

  fetchSalesData: async (period = '30days') => {
    set({ error: null })
    try {
      const currentStoreId = getCurrentStoreId()
      const now = new Date()
      let startDate: Date
      let days: number
      
      switch (period) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          days = 1
          break
        case '7days':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          days = 7
          break
        case '30days':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          days = 30
          break
        case '90days':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          days = 90
          break
        default:
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          days = 30
      }

      const ordersQuery = query(
        collection(db, 'stores', currentStoreId, 'orders'),
        where('createdAt', '>=', Timestamp.fromDate(startDate)),
        orderBy('createdAt', 'asc')
      )
      
      const ordersSnapshot = await getDocs(ordersQuery)
      const orders = ordersSnapshot.docs.map(doc => ({
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(doc.data().createdAt)
      })).filter(order => order.status !== 'cancelled' && order.status !== 'refunded')
      
      // Group orders by date
      const salesByDate: { [key: string]: { revenue: number; orders: number; customers: Set<string> } } = {}
      
      orders.forEach(order => {
        const date = order.createdAt.toISOString().split('T')[0]
        if (!salesByDate[date]) {
          salesByDate[date] = { revenue: 0, orders: 0, customers: new Set() }
        }
        salesByDate[date].revenue += order.total || 0
        salesByDate[date].orders += 1
        salesByDate[date].customers.add(order.customerId)
      })
      
      // Convert to array format
      const salesData: SalesData[] = []
      for (let i = 0; i < days; i++) {
        const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000)
        const dateStr = date.toISOString().split('T')[0]
        const dayData = salesByDate[dateStr] || { revenue: 0, orders: 0, customers: new Set() }
        
        salesData.push({
          date: dateStr,
          revenue: dayData.revenue,
          orders: dayData.orders,
          customers: dayData.customers.size
        })
      }
      
      set({ salesData })
    } catch (error: any) {
      console.error('Error fetching sales data:', error)
      set({ error: error.message })
    }
  },

  fetchTopProducts: async (period = '30days') => {
    set({ error: null })
    try {
      const currentStoreId = getCurrentStoreId()
      const now = new Date()
      let startDate: Date
      
      switch (period) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          break
        case '7days':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30days':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case '90days':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
        default:
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      }

      const ordersQuery = query(
        collection(db, 'stores', currentStoreId, 'orders'),
        where('createdAt', '>=', Timestamp.fromDate(startDate)),
        orderBy('createdAt', 'desc')
      )
      
      const ordersSnapshot = await getDocs(ordersQuery)
      const orders = ordersSnapshot.docs.map(doc => doc.data()).filter(order =>
        order.status !== 'cancelled' && order.status !== 'refunded'
      )
      
      // Aggregate product sales
      const productSales: { [key: string]: { name: string; revenue: number; orders: number; quantity: number } } = {}
      
      orders.forEach(order => {
        order.items?.forEach((item: any) => {
          if (!productSales[item.productId]) {
            productSales[item.productId] = {
              name: item.productName,
              revenue: 0,
              orders: 0,
              quantity: 0
            }
          }
          productSales[item.productId].revenue += item.total || 0
          productSales[item.productId].orders += 1
          productSales[item.productId].quantity += item.quantity || 0
        })
      })
      
      // Convert to array and sort by revenue
      const topProducts: TopProduct[] = Object.entries(productSales)
        .map(([id, data]) => ({ id, ...data }))
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, 10)
      
      set({ topProducts })
    } catch (error: any) {
      console.error('Error fetching top products:', error)
      set({ error: error.message })
    }
  },

  fetchTopCustomers: async () => {
    set({ error: null })
    try {
      const currentStoreId = getCurrentStoreId()
      const customersQuery = query(
        collection(db, 'stores', currentStoreId, 'customers'),
        orderBy('totalSpent', 'desc'),
        limit(10)
      )
      
      const customersSnapshot = await getDocs(customersQuery)
      const topCustomers: TopCustomer[] = customersSnapshot.docs.map(doc => {
        const data = doc.data()
        return {
          id: doc.id,
          name: `${data.firstName} ${data.lastName}`,
          email: data.email,
          totalSpent: data.totalSpent || 0,
          ordersCount: data.ordersCount || 0
        }
      })
      
      set({ topCustomers })
    } catch (error: any) {
      console.error('Error fetching top customers:', error)
      set({ error: error.message })
    }
  },

  fetchRecentOrders: async (limitCount = 10) => {
    set({ error: null })
    try {
      const currentStoreId = getCurrentStoreId()
      const ordersQuery = query(
        collection(db, 'stores', currentStoreId, 'orders'),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      )
      
      const ordersSnapshot = await getDocs(ordersQuery)
      const recentOrders: RecentOrder[] = ordersSnapshot.docs.map(doc => {
        const data = doc.data()
        return {
          id: doc.id,
          orderNumber: data.orderNumber,
          customerName: data.customerName,
          total: data.total,
          status: data.status,
          createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt
        }
      })
      
      set({ recentOrders })
    } catch (error: any) {
      console.error('Error fetching recent orders:', error)
      set({ error: error.message })
    }
  }
}))

// Auto-fetch initial data (disabled until indexes are ready)
// Uncomment after creating Firestore indexes
// const store = useAnalyticsStore.getState()
// store.fetchDashboardMetrics()
// store.fetchSalesData()
// store.fetchTopProducts()
// store.fetchTopCustomers()
// store.fetchRecentOrders()
