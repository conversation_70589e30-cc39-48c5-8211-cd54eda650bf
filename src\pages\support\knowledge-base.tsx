import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'
import {
  Search,
  BookOpen,
  Video,
  FileText,
  HelpCircle,
  ExternalLink,
  Star,
  Clock,
  Users,
  ChevronRight
} from 'lucide-react'

interface Article {
  id: string
  title: string
  description: string
  category: string
  readTime: string
  rating: number
  views: number
  lastUpdated: string
  tags: string[]
}

const knowledgeBaseArticles: Article[] = [
  {
    id: '1',
    title: 'Getting Started with Your Store',
    description: 'Learn the basics of setting up and managing your online store',
    category: 'Getting Started',
    readTime: '5 min',
    rating: 4.8,
    views: 1250,
    lastUpdated: '2024-01-15',
    tags: ['setup', 'basics', 'store']
  },
  {
    id: '2',
    title: 'Adding and Managing Products',
    description: 'Complete guide to product management, inventory, and pricing',
    category: 'Products',
    readTime: '8 min',
    rating: 4.9,
    views: 980,
    lastUpdated: '2024-01-12',
    tags: ['products', 'inventory', 'pricing']
  },
  {
    id: '3',
    title: 'Order Processing and Fulfillment',
    description: 'How to handle orders from placement to delivery',
    category: 'Orders',
    readTime: '6 min',
    rating: 4.7,
    views: 750,
    lastUpdated: '2024-01-10',
    tags: ['orders', 'fulfillment', 'shipping']
  },
  {
    id: '4',
    title: 'Customer Management Best Practices',
    description: 'Tips for managing customer relationships and support',
    category: 'Customers',
    readTime: '7 min',
    rating: 4.6,
    views: 650,
    lastUpdated: '2024-01-08',
    tags: ['customers', 'support', 'relationships']
  },
  {
    id: '5',
    title: 'Marketing and Promotions Guide',
    description: 'Create effective marketing campaigns and promotional offers',
    category: 'Marketing',
    readTime: '10 min',
    rating: 4.8,
    views: 890,
    lastUpdated: '2024-01-05',
    tags: ['marketing', 'promotions', 'campaigns']
  },
  {
    id: '6',
    title: 'Analytics and Reporting',
    description: 'Understanding your store metrics and performance data',
    category: 'Analytics',
    readTime: '9 min',
    rating: 4.5,
    views: 420,
    lastUpdated: '2024-01-03',
    tags: ['analytics', 'reports', 'metrics']
  }
]

const categories = ['All', 'Getting Started', 'Products', 'Orders', 'Customers', 'Marketing', 'Analytics']

export default function KnowledgeBasePage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All')

  const filteredArticles = knowledgeBaseArticles.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = selectedCategory === 'All' || article.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Knowledge Base</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Find answers to common questions and learn how to use your admin panel effectively
        </p>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search articles..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2 flex-wrap">
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </Button>
          ))}
        </div>
      </div>

      {/* Quick Links */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                <Video className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h3 className="font-semibold">Video Tutorials</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Watch step-by-step guides</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                <FileText className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h3 className="font-semibold">API Documentation</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Technical documentation</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                <HelpCircle className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <h3 className="font-semibold">Contact Support</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Get personalized help</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Articles Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredArticles.map((article) => (
          <Card key={article.id} className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg">{article.title}</CardTitle>
                  <CardDescription className="mt-2">{article.description}</CardDescription>
                </div>
                <ChevronRight className="h-5 w-5 text-gray-400 mt-1" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>{article.readTime}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span>{article.rating}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    <span>{article.views}</span>
                  </div>
                </div>
                <Badge variant="secondary">{article.category}</Badge>
              </div>
              <div className="flex flex-wrap gap-1 mt-3">
                {article.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredArticles.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No articles found</h3>
          <p className="text-gray-600 dark:text-gray-400">
            Try adjusting your search terms or browse different categories
          </p>
        </div>
      )}
    </div>
  )
}
