import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  serverTimestamp,
  onSnapshot,
  writeBatch,
  Timestamp
} from 'firebase/firestore'
import { db, isFirebaseConfigured } from './firebase'
import type { Customer } from './firebase'

export interface CustomerStats {
  total: number
  active: number
  inactive: number
  blocked: number
  vip: number
  newThisMonth: number
  totalSpent: number
  avgOrderValue: number
  avgLifetimeValue: number
  repeatCustomers: number
  loyalCustomers: number
  atRiskCustomers: number
  totalLoyaltyPoints: number
  avgOrderFrequency: number
  customerRetentionRate: number
  monthlyGrowthRate: number
}

export interface CustomerSegment {
  id: string
  name: string
  description: string
  criteria: {
    totalSpent?: { min?: number; max?: number }
    orderCount?: { min?: number; max?: number }
    lastOrderDays?: { min?: number; max?: number }
    loyaltyPoints?: { min?: number; max?: number }
    status?: string[]
    tags?: string[]
  }
  customerCount: number
  createdAt: string
  updatedAt: string
}

export interface CustomerActivity {
  id: string
  customerId: string
  type: 'order' | 'login' | 'registration' | 'support' | 'review' | 'wishlist' | 'cart'
  description: string
  metadata?: any
  createdAt: string
}

class CustomersService {
  private storeId: string

  constructor(storeId: string) {
    this.storeId = storeId
  }

  /**
   * Get all customers for the store
   */
  async getCustomers(options?: {
    limit?: number
    startAfter?: any
    orderBy?: string
    orderDirection?: 'asc' | 'desc'
    filters?: {
      status?: string
      group?: string
      search?: string
    }
  }): Promise<Customer[]> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      console.log('🔍 Fetching customers for store:', this.storeId)

      let q = query(
        collection(db, 'stores', this.storeId, 'customers'),
        orderBy(options?.orderBy || 'createdAt', options?.orderDirection || 'desc')
      )

      // Apply filters
      if (options?.filters?.status && options.filters.status !== 'all') {
        q = query(q, where('status', '==', options.filters.status))
      }

      if (options?.filters?.group && options.filters.group !== 'all') {
        q = query(q, where('group', '==', options.filters.group))
      }

      if (options?.limit) {
        q = query(q, limit(options.limit))
      }

      if (options?.startAfter) {
        q = query(q, startAfter(options.startAfter))
      }

      const snapshot = await getDocs(q)
      const customers = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
        updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
        dateOfBirth: doc.data().dateOfBirth?.toDate?.()?.toISOString() || doc.data().dateOfBirth,
        lastOrderDate: doc.data().lastOrderDate?.toDate?.()?.toISOString() || doc.data().lastOrderDate
      } as Customer))

      // Apply search filter (client-side for now)
      let filteredCustomers = customers
      if (options?.filters?.search) {
        const searchTerm = options.filters.search.toLowerCase()
        filteredCustomers = customers.filter(customer =>
          customer.name?.toLowerCase().includes(searchTerm) ||
          customer.email?.toLowerCase().includes(searchTerm) ||
          customer.phone?.includes(searchTerm)
        )
      }

      console.log('✅ Fetched', filteredCustomers.length, 'customers')
      return filteredCustomers
    } catch (error) {
      console.error('❌ Error fetching customers:', error)
      throw new Error('Failed to fetch customers')
    }
  }

  /**
   * Get a single customer by ID
   */
  async getCustomer(customerId: string): Promise<Customer | null> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      const docRef = doc(db, 'stores', this.storeId, 'customers', customerId)
      const docSnap = await getDoc(docRef)

      if (!docSnap.exists()) {
        return null
      }

      const data = docSnap.data()
      return {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
        dateOfBirth: data.dateOfBirth?.toDate?.()?.toISOString() || data.dateOfBirth,
        lastOrderDate: data.lastOrderDate?.toDate?.()?.toISOString() || data.lastOrderDate
      } as Customer
    } catch (error) {
      console.error('❌ Error fetching customer:', error)
      throw new Error('Failed to fetch customer')
    }
  }

  /**
   * Create a new customer
   */
  async createCustomer(customerData: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      console.log('📝 Creating customer for store:', this.storeId)

      const newCustomer = {
        ...customerData,
        storeId: this.storeId,
        status: customerData.status || 'active',
        group: customerData.group || 'regular',
        totalSpent: customerData.totalSpent || 0,
        orderCount: customerData.orderCount || 0,
        loyaltyPoints: customerData.loyaltyPoints || 0,
        tags: customerData.tags || [],
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      }

      const docRef = await addDoc(
        collection(db, 'stores', this.storeId, 'customers'),
        newCustomer
      )

      console.log('✅ Customer created with ID:', docRef.id)
      return docRef.id
    } catch (error) {
      console.error('❌ Error creating customer:', error)
      throw new Error('Failed to create customer')
    }
  }

  /**
   * Update a customer
   */
  async updateCustomer(customerId: string, updates: Partial<Customer>): Promise<void> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      console.log('📝 Updating customer:', customerId)

      const docRef = doc(db, 'stores', this.storeId, 'customers', customerId)
      await updateDoc(docRef, {
        ...updates,
        updatedAt: serverTimestamp()
      })

      console.log('✅ Customer updated successfully')
    } catch (error) {
      console.error('❌ Error updating customer:', error)
      throw new Error('Failed to update customer')
    }
  }

  /**
   * Delete a customer
   */
  async deleteCustomer(customerId: string): Promise<void> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      console.log('🗑️ Deleting customer:', customerId)

      const docRef = doc(db, 'stores', this.storeId, 'customers', customerId)
      await deleteDoc(docRef)

      console.log('✅ Customer deleted successfully')
    } catch (error) {
      console.error('❌ Error deleting customer:', error)
      throw new Error('Failed to delete customer')
    }
  }

  /**
   * Get customer statistics
   */
  async getCustomerStats(): Promise<CustomerStats> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      console.log('📊 Calculating customer statistics for store:', this.storeId)

      const customers = await this.getCustomers()
      const now = new Date()
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)

      const stats: CustomerStats = {
        total: customers.length,
        active: customers.filter(c => c.status === 'active').length,
        inactive: customers.filter(c => c.status === 'inactive').length,
        blocked: customers.filter(c => c.status === 'blocked').length,
        vip: customers.filter(c => c.group === 'vip').length,
        newThisMonth: customers.filter(c => 
          new Date(c.createdAt) >= thisMonth
        ).length,
        totalSpent: customers.reduce((sum, c) => sum + (c.totalSpent || 0), 0),
        avgOrderValue: 0,
        avgLifetimeValue: 0,
        repeatCustomers: customers.filter(c => (c.orderCount || 0) > 1).length,
        loyalCustomers: customers.filter(c => (c.orderCount || 0) >= 5).length,
        atRiskCustomers: customers.filter(c => {
          if (!c.lastOrderDate) return false
          const daysSinceLastOrder = (now.getTime() - new Date(c.lastOrderDate).getTime()) / (1000 * 60 * 60 * 24)
          return daysSinceLastOrder > 90
        }).length,
        totalLoyaltyPoints: customers.reduce((sum, c) => sum + (c.loyaltyPoints || 0), 0),
        avgOrderFrequency: 0,
        customerRetentionRate: 0,
        monthlyGrowthRate: 0
      }

      // Calculate averages
      if (stats.total > 0) {
        stats.avgLifetimeValue = stats.totalSpent / stats.total
        
        const customersWithOrders = customers.filter(c => (c.orderCount || 0) > 0)
        if (customersWithOrders.length > 0) {
          stats.avgOrderValue = stats.totalSpent / customersWithOrders.reduce((sum, c) => sum + (c.orderCount || 0), 0)
        }
      }

      // Calculate growth rate
      const lastMonthCustomers = customers.filter(c => 
        new Date(c.createdAt) >= lastMonth && new Date(c.createdAt) < thisMonth
      ).length
      
      if (lastMonthCustomers > 0) {
        stats.monthlyGrowthRate = ((stats.newThisMonth - lastMonthCustomers) / lastMonthCustomers) * 100
      }

      console.log('✅ Customer statistics calculated')
      return stats
    } catch (error) {
      console.error('❌ Error calculating customer statistics:', error)
      throw new Error('Failed to calculate customer statistics')
    }
  }

  /**
   * Subscribe to real-time customer updates
   */
  subscribeToCustomers(
    callback: (customers: Customer[]) => void,
    options?: {
      limit?: number
      orderBy?: string
      orderDirection?: 'asc' | 'desc'
    }
  ) {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    console.log('🔔 Setting up real-time customer subscription for store:', this.storeId)

    let q = query(
      collection(db, 'stores', this.storeId, 'customers'),
      orderBy(options?.orderBy || 'createdAt', options?.orderDirection || 'desc')
    )

    if (options?.limit) {
      q = query(q, limit(options.limit))
    }

    return onSnapshot(q, (snapshot) => {
      const customers = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
        updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
        dateOfBirth: doc.data().dateOfBirth?.toDate?.()?.toISOString() || doc.data().dateOfBirth,
        lastOrderDate: doc.data().lastOrderDate?.toDate?.()?.toISOString() || doc.data().lastOrderDate
      } as Customer))

      console.log('🔄 Real-time customer update:', customers.length, 'customers')
      callback(customers)
    }, (error) => {
      console.error('❌ Real-time customer subscription error:', error)
    })
  }
}

export { CustomersService }
