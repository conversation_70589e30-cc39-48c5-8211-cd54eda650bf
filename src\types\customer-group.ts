export interface CustomerGroup {
  id: string
  name: string
  description: string
  benefits: string[]
  
  // Group criteria for automatic assignment
  criteria?: {
    minLifetimeValue?: number
    maxLifetimeValue?: number
    minOrderCount?: number
    maxOrderCount?: number
    minDaysSinceLastOrder?: number
    maxDaysSinceLastOrder?: number
    requiredTags?: string[]
    excludedTags?: string[]
  }
  
  // Group settings
  isAutomatic: boolean // true = auto-assigned based on criteria, false = manual
  color: string
  icon: string
  priority: number // Higher priority groups take precedence
  
  // Statistics
  customerCount: number
  totalRevenue: number
  averageOrderValue: number
  conversionRate: number
  retentionRate: number
  
  // Metadata
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
  createdBy: string
}

export interface CustomerGroupFormData {
  name: string
  description: string
  benefits: string[]
  isAutomatic: boolean
  criteria?: {
    minLifetimeValue?: number
    maxLifetimeValue?: number
    minOrderCount?: number
    maxOrderCount?: number
    minDaysSinceLastOrder?: number
    maxDaysSinceLastOrder?: number
    requiredTags?: string[]
    excludedTags?: string[]
  }
  color: string
  icon: string
  priority: number
  status: 'active' | 'inactive'
}

// Predefined group templates
export const DEFAULT_CUSTOMER_GROUPS: Omit<CustomerGroup, 'id' | 'customerCount' | 'totalRevenue' | 'averageOrderValue' | 'conversionRate' | 'retentionRate' | 'createdAt' | 'updatedAt' | 'createdBy'>[] = [
  {
    name: 'VIP',
    description: 'High-value customers with premium status',
    benefits: ['Priority support', 'Exclusive discounts', 'Early access to new products', 'Free shipping'],
    isAutomatic: true,
    criteria: {
      minLifetimeValue: 50000,
      minOrderCount: 10
    },
    color: '#FFD700',
    icon: 'crown',
    priority: 1,
    status: 'active'
  },
  {
    name: 'Loyal',
    description: 'Repeat customers with strong engagement',
    benefits: ['Member discounts', 'Birthday rewards', 'Loyalty points'],
    isAutomatic: true,
    criteria: {
      minLifetimeValue: 15000,
      minOrderCount: 5
    },
    color: '#9333EA',
    icon: 'heart',
    priority: 2,
    status: 'active'
  },
  {
    name: 'New',
    description: 'Recently registered customers',
    benefits: ['Welcome discount', 'Onboarding support'],
    isAutomatic: true,
    criteria: {
      maxOrderCount: 2
    },
    color: '#10B981',
    icon: 'star',
    priority: 4,
    status: 'active'
  },
  {
    name: 'Wholesale',
    description: 'Bulk purchase customers',
    benefits: ['Volume discounts', 'Net payment terms', 'Dedicated account manager'],
    isAutomatic: false,
    color: '#3B82F6',
    icon: 'building',
    priority: 1,
    status: 'active'
  },
  {
    name: 'Retail',
    description: 'Regular retail customers',
    benefits: ['Standard pricing', 'Regular promotions'],
    isAutomatic: true,
    criteria: {
      minLifetimeValue: 1000,
      maxLifetimeValue: 14999
    },
    color: '#6B7280',
    icon: 'user',
    priority: 3,
    status: 'active'
  },
  {
    name: 'At Risk',
    description: 'Customers who haven\'t ordered recently',
    benefits: ['Win-back offers', 'Special attention'],
    isAutomatic: true,
    criteria: {
      minDaysSinceLastOrder: 90,
      minOrderCount: 1
    },
    color: '#EF4444',
    icon: 'alert-triangle',
    priority: 5,
    status: 'active'
  }
]
