import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage'
import { storage } from './firebase'

export interface UploadProgress {
  progress: number
  url?: string
  error?: string
}

export class StorageService {
  private storeId = (typeof process !== 'undefined' && process.env && process.env.VITE_STORE_ID) ? process.env.VITE_STORE_ID : 'womanza_store_id'

  /**
   * Upload a file to Firebase Storage
   */
  async uploadFile(
    file: File,
    path: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<string> {
    try {
      // Validate file
      if (!file) {
        throw new Error('No file provided')
      }

      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        throw new Error('File size must be less than 5MB')
      }

      // Check file type
      if (!file.type.startsWith('image/')) {
        throw new Error('Only image files are allowed')
      }

      // Use mock upload in development/emulator mode when storage emulator is not available
      if (this.isEmulatorMode()) {
        throw new Error('File upload is not available in emulator mode in production.')
      }

      // Create storage reference with proper organization
      const timestamp = Date.now()
      const sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_')
      const storageRef = ref(storage, `stores/${this.storeId}/${path}/${timestamp}_${sanitizedFileName}`)

      // Report initial progress
      onProgress?.({ progress: 0 })

      // Upload file
      const snapshot = await uploadBytes(storageRef, file)

      // Report completion
      onProgress?.({ progress: 100 })

      // Get download URL
      const downloadURL = await getDownloadURL(snapshot.ref)

      onProgress?.({ progress: 100, url: downloadURL })

      return downloadURL
    } catch (error) {
      console.warn('Firebase Storage upload failed, falling back to mock upload:', error)
      throw error
    }
  }

  /**
   * Upload multiple files
   */
  async uploadFiles(
    files: File[],
    path: string,
    onProgress?: (fileIndex: number, progress: UploadProgress) => void
  ): Promise<string[]> {
    const uploadPromises = files.map((file, index) =>
      this.uploadFile(file, path, (progress) => onProgress?.(index, progress))
    )

    return Promise.all(uploadPromises)
  }

  /**
   * Upload product images
   */
  async uploadProductImages(
    files: File[],
    onProgress?: (fileIndex: number, progress: UploadProgress) => void
  ): Promise<string[]> {
    return this.uploadFiles(files, 'products/images', onProgress)
  }

  /**
   * Upload category images
   */
  async uploadCategoryImages(
    files: File[],
    onProgress?: (fileIndex: number, progress: UploadProgress) => void
  ): Promise<string[]> {
    return this.uploadFiles(files, 'categories/images', onProgress)
  }

  /**
   * Upload promotion banners
   */
  async uploadPromotionBanners(
    files: File[],
    onProgress?: (fileIndex: number, progress: UploadProgress) => void
  ): Promise<string[]> {
    return this.uploadFiles(files, 'promotions/banners', onProgress)
  }

  /**
   * Upload user avatars
   */
  async uploadUserAvatar(
    file: File,
    userId: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<string> {
    return this.uploadFile(file, `users/avatars/${userId}`, onProgress)
  }

  /**
   * Upload store logo
   */
  async uploadStoreLogo(
    file: File,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<string> {
    return this.uploadFile(file, 'store/branding', onProgress)
  }

  /**
   * Delete a file from storage
   */
  async deleteFile(url: string): Promise<void> {
    try {
      const fileRef = ref(storage, url)
      await deleteObject(fileRef)
    } catch (error) {
      console.error('Error deleting file:', error)
      throw error
    }
  }

  /**
   * Mock upload for development/emulator mode
   */
  private mockUpload(
    file: File,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<string> {
    return new Promise((resolve) => {
      // Report initial progress
      onProgress?.({ progress: 0 })

      // Simulate upload progress
      let progress = 0
      const interval = setInterval(() => {
        progress += 20
        onProgress?.({ progress })

        if (progress >= 100) {
          clearInterval(interval)

          // Create a mock URL using object URL
          const mockUrl = URL.createObjectURL(file)

          // Store in localStorage for persistence across page reloads
          const mockImages = JSON.parse(localStorage.getItem('mock-uploaded-images') || '[]')
          mockImages.push({
            url: mockUrl,
            filename: file.name,
            timestamp: Date.now()
          })
          localStorage.setItem('mock-uploaded-images', JSON.stringify(mockImages))

          onProgress?.({ progress: 100, url: mockUrl })
          resolve(mockUrl)
        }
      }, 200) // Simulate 1 second upload
    })
  }

  /**
   * Check if we're in emulator mode
   */
  private isEmulatorMode(): boolean {
    return (typeof process !== 'undefined' && process.env && (
      process.env.VITE_USE_FIREBASE_EMULATOR === 'true' ||
      process.env.VITE_FIREBASE_PROJECT_ID === 'demo-project' ||
      !process.env.VITE_FIREBASE_API_KEY ||
      process.env.VITE_FIREBASE_API_KEY === 'demo-api-key'
    ))
  }
}

export const storageService = new StorageService()
