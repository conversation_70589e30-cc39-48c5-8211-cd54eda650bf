import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from '../../components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Select } from '../../components/ui/select'
import { Switch } from '../../components/ui/switch'
import { Textarea } from '../../components/ui/textarea'
import { MarketingService, type Promotion } from '../../lib/marketing-service'
import { toast } from 'react-hot-toast'
import { ArrowLeft, Save, Megaphone } from 'lucide-react'

export default function PromotionFormPage() {
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)
  const marketingService = new MarketingService('womanza-jewelry-store')

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    discountType: 'percentage' as 'percentage' | 'fixed_amount' | 'free_shipping',
    discountValue: 0,
    applicableCategories: [] as string[],
    applicableProducts: [] as string[],
    minCartValue: 0,
    customerGroup: '',
    startDate: '',
    endDate: '',
    autoApply: false,
    active: true,
    priority: 1,
    imageUrl: ''
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim()) {
      toast.error('Promotion title is required')
      return
    }

    if (!formData.description.trim()) {
      toast.error('Promotion description is required')
      return
    }

    if (formData.discountType !== 'free_shipping' && formData.discountValue <= 0) {
      toast.error('Discount value must be greater than 0')
      return
    }

    if (!formData.startDate || !formData.endDate) {
      toast.error('Start and end dates are required')
      return
    }

    if (new Date(formData.startDate) >= new Date(formData.endDate)) {
      toast.error('End date must be after start date')
      return
    }

    try {
      setLoading(true)
      
      const promotionData = {
        ...formData,
        createdBy: 'admin' // TODO: Get from auth context
      }

      await marketingService.createPromotion(promotionData)
      
      toast.success('Promotion created successfully!', {
        style: {
          background: '#10B981',
          color: 'white',
        }
      })
      
      navigate('/admin/marketing')
    } catch (error) {
      console.error('Error creating promotion:', error)
      toast.error('Failed to create promotion', {
        style: {
          background: '#EF4444',
          color: 'white',
        }
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => navigate('/admin/marketing')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Marketing
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Create Promotion</h1>
            <p className="text-gray-600">Create a new promotional campaign for your store</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Megaphone className="h-5 w-5 mr-2" />
                  Promotion Details
                </CardTitle>
                <CardDescription>
                  Configure the basic details of your promotion
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="title">Promotion Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    placeholder="e.g., Summer Sale"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Describe your promotion..."
                    rows={3}
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="discountType">Discount Type *</Label>
                    <Select
                      value={formData.discountType}
                      onValueChange={(value: 'percentage' | 'fixed_amount' | 'free_shipping') =>
                        setFormData({ ...formData, discountType: value, discountValue: value === 'free_shipping' ? 0 : formData.discountValue })
                      }
                      options={[
                        { value: 'percentage', label: 'Percentage Off' },
                        { value: 'fixed_amount', label: 'Fixed Amount Off' },
                        { value: 'free_shipping', label: 'Free Shipping' }
                      ]}
                    />
                  </div>

                  {formData.discountType !== 'free_shipping' && (
                    <div>
                      <Label htmlFor="discountValue">
                        Discount Value * {formData.discountType === 'percentage' ? '(%)' : '(₹)'}
                      </Label>
                      <Input
                        id="discountValue"
                        type="number"
                        value={formData.discountValue}
                        onChange={(e) => setFormData({ ...formData, discountValue: Number(e.target.value) })}
                        placeholder={formData.discountType === 'percentage' ? '20' : '1000'}
                        min="0"
                        max={formData.discountType === 'percentage' ? '100' : undefined}
                        required
                      />
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="minCartValue">Minimum Cart Value (₹)</Label>
                    <Input
                      id="minCartValue"
                      type="number"
                      value={formData.minCartValue}
                      onChange={(e) => setFormData({ ...formData, minCartValue: Number(e.target.value) })}
                      placeholder="0"
                      min="0"
                    />
                  </div>

                  <div>
                    <Label htmlFor="priority">Priority</Label>
                    <Select
                      value={formData.priority.toString()}
                      onValueChange={(value) => setFormData({ ...formData, priority: Number(value) })}
                      options={[
                        { value: '1', label: 'High (1)' },
                        { value: '2', label: 'Medium (2)' },
                        { value: '3', label: 'Low (3)' }
                      ]}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="startDate">Start Date *</Label>
                    <Input
                      id="startDate"
                      type="datetime-local"
                      value={formData.startDate}
                      onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="endDate">End Date *</Label>
                    <Input
                      id="endDate"
                      type="datetime-local"
                      value={formData.endDate}
                      onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="autoApply"
                      checked={formData.autoApply}
                      onCheckedChange={(checked) => setFormData({ ...formData, autoApply: checked })}
                    />
                    <Label htmlFor="autoApply">Auto-apply</Label>
                    <p className="text-sm text-gray-500">
                      Automatically apply this promotion to eligible orders
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="active"
                      checked={formData.active}
                      onCheckedChange={(checked) => setFormData({ ...formData, active: checked })}
                    />
                    <Label htmlFor="active">Active</Label>
                    <p className="text-sm text-gray-500">
                      Inactive promotions will not be applied
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-lg">
                  <div className="text-center">
                    <div className="text-lg font-bold mb-2">
                      {formData.title || 'Promotion Title'}
                    </div>
                    <div className="text-sm mb-2 opacity-90">
                      {formData.description || 'Promotion description will appear here'}
                    </div>
                    <div className="text-xl font-bold mb-2">
                      {formData.discountType === 'percentage' && `${formData.discountValue}% OFF`}
                      {formData.discountType === 'fixed_amount' && `₹${formData.discountValue} OFF`}
                      {formData.discountType === 'free_shipping' && 'FREE SHIPPING'}
                    </div>
                    {formData.minCartValue > 0 && (
                      <div className="text-sm opacity-90">
                        On orders above ₹{formData.minCartValue}
                      </div>
                    )}
                    <div className="text-xs opacity-75 mt-2">
                      {formData.startDate && formData.endDate && (
                        <>
                          {new Date(formData.startDate).toLocaleDateString()} - {new Date(formData.endDate).toLocaleDateString()}
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex gap-3">
                  <Button type="submit" disabled={loading} className="flex-1">
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Creating...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Create Promotion
                      </>
                    )}
                  </Button>
                  <Button type="button" variant="outline" onClick={() => navigate('/admin/marketing')}>
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  )
}
