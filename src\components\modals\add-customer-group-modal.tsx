import React, { useState } from 'react'
import { toast } from 'react-hot-toast'

import { But<PERSON> } from '../ui/button'
import { Badge } from '../ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { useAutoClose } from '../../hooks/use-click-outside'

import {
  X,
  Users,
  Crown,
  Heart,
  Star,
  AlertTriangle,
  Target,
  Percent,
  DollarSign,
  Calendar,
  Activity
} from 'lucide-react'

interface CustomerGroup {
  id: string
  name: string
  description: string
  rules: {
    minSpend?: number
    orderCount?: number
    registrationDays?: number
    lastActivityDays?: number
  }
  discountRate?: number
  customerCount: number
  totalRevenue: number
  createdAt: string
  updatedAt: string
  status: 'active' | 'inactive'
  color: string
  icon: string
}

interface AddCustomerGroupModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (group: Omit<CustomerGroup, 'customerCount' | 'totalRevenue' | 'createdAt' | 'updatedAt'>) => void
}

export function AddCustomerGroupModal({ isOpen, onClose, onSave }: AddCustomerGroupModalProps) {
  const [formData, setFormData] = useState({
    id: '',
    name: '',
    description: '',
    rules: {
      minSpend: '',
      orderCount: '',
      registrationDays: '',
      lastActivityDays: ''
    },
    discountRate: '',
    status: 'active' as 'active' | 'inactive',
    color: 'blue',
    icon: 'users'
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  // Auto-close functionality for modal (escape key only, not click outside for modals)
  const modalRef = useAutoClose(() => {}, false) // Disabled click outside for modal

  // Handle escape key separately
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        handleClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      return () => document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen])

  const colorOptions = [
    { value: 'blue', label: 'Blue', class: 'bg-blue-500' },
    { value: 'purple', label: 'Purple', class: 'bg-purple-500' },
    { value: 'green', label: 'Green', class: 'bg-green-500' },
    { value: 'red', label: 'Red', class: 'bg-red-500' },
    { value: 'orange', label: 'Orange', class: 'bg-orange-500' },
    { value: 'yellow', label: 'Yellow', class: 'bg-yellow-500' },
    { value: 'pink', label: 'Pink', class: 'bg-pink-500' },
    { value: 'indigo', label: 'Indigo', class: 'bg-indigo-500' }
  ]

  const iconOptions = [
    { value: 'users', label: 'Users', icon: Users },
    { value: 'crown', label: 'Crown', icon: Crown },
    { value: 'heart', label: 'Heart', icon: Heart },
    { value: 'star', label: 'Star', icon: Star },
    { value: 'alert', label: 'Alert', icon: AlertTriangle },
    { value: 'target', label: 'Target', icon: Target }
  ]

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Group name is required'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required'
    }

    if (!formData.id.trim()) {
      newErrors.id = 'Group ID is required'
    } else if (!/^[a-z0-9-_]+$/.test(formData.id)) {
      newErrors.id = 'Group ID can only contain lowercase letters, numbers, hyphens, and underscores'
    }

    if (formData.discountRate && (isNaN(Number(formData.discountRate)) || Number(formData.discountRate) < 0 || Number(formData.discountRate) > 100)) {
      newErrors.discountRate = 'Discount rate must be between 0 and 100'
    }

    if (formData.rules.minSpend && (isNaN(Number(formData.rules.minSpend)) || Number(formData.rules.minSpend) < 0)) {
      newErrors.minSpend = 'Minimum spend must be a positive number'
    }

    if (formData.rules.orderCount && (isNaN(Number(formData.rules.orderCount)) || Number(formData.rules.orderCount) < 0)) {
      newErrors.orderCount = 'Order count must be a positive number'
    }

    if (formData.rules.registrationDays && (isNaN(Number(formData.rules.registrationDays)) || Number(formData.rules.registrationDays) < 0)) {
      newErrors.registrationDays = 'Registration days must be a positive number'
    }

    if (formData.rules.lastActivityDays && (isNaN(Number(formData.rules.lastActivityDays)) || Number(formData.rules.lastActivityDays) < 0)) {
      newErrors.lastActivityDays = 'Last activity days must be a positive number'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    const groupData = {
      id: formData.id,
      name: formData.name,
      description: formData.description,
      rules: {
        ...(formData.rules.minSpend && { minSpend: Number(formData.rules.minSpend) }),
        ...(formData.rules.orderCount && { orderCount: Number(formData.rules.orderCount) }),
        ...(formData.rules.registrationDays && { registrationDays: Number(formData.rules.registrationDays) }),
        ...(formData.rules.lastActivityDays && { lastActivityDays: Number(formData.rules.lastActivityDays) })
      },
      ...(formData.discountRate && { discountRate: Number(formData.discountRate) }),
      status: formData.status,
      color: formData.color,
      icon: formData.icon
    }

    onSave(groupData)
    handleClose()
  }

  const handleClose = () => {
    setFormData({
      id: '',
      name: '',
      description: '',
      rules: {
        minSpend: '',
        orderCount: '',
        registrationDays: '',
        lastActivityDays: ''
      },
      discountRate: '',
      status: 'active',
      color: 'blue',
      icon: 'users'
    })
    setErrors({})
    onClose()
  }

  const generateId = () => {
    const id = formData.name.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '')
    setFormData(prev => ({ ...prev, id }))
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 theme-modal-overlay">
      <div ref={modalRef} className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[85vh] overflow-y-auto theme-modal modal-optimized">
        <div className="modal-content-optimized">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Add Customer Group</h2>
            <Button variant="ghost" size="sm" onClick={handleClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Group Name *
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      onBlur={generateId}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="e.g., VIP Customers"
                    />
                    {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Group ID *
                    </label>
                    <input
                      type="text"
                      value={formData.id}
                      onChange={(e) => setFormData(prev => ({ ...prev, id: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="e.g., vip-customers"
                    />
                    {errors.id && <p className="text-red-500 text-xs mt-1">{errors.id}</p>}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Description *
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Describe this customer group..."
                  />
                  {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Status
                    </label>
                    <select
                      value={formData.status}
                      onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as 'active' | 'inactive' }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Color
                    </label>
                    <select
                      value={formData.color}
                      onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {colorOptions.map(color => (
                        <option key={color.value} value={color.value}>{color.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Icon
                    </label>
                    <select
                      value={formData.icon}
                      onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {iconOptions.map(icon => (
                        <option key={icon.value} value={icon.value}>{icon.label}</option>
                      ))}
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Group Rules */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Group Rules</CardTitle>
                <p className="text-sm text-gray-500">Define criteria for automatic group assignment</p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Minimum Spend ($)
                    </label>
                    <input
                      type="number"
                      value={formData.rules.minSpend}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        rules: { ...prev.rules, minSpend: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="e.g., 1000"
                      min="0"
                    />
                    {errors.minSpend && <p className="text-red-500 text-xs mt-1">{errors.minSpend}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Minimum Orders
                    </label>
                    <input
                      type="number"
                      value={formData.rules.orderCount}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        rules: { ...prev.rules, orderCount: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="e.g., 5"
                      min="0"
                    />
                    {errors.orderCount && <p className="text-red-500 text-xs mt-1">{errors.orderCount}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Registration Days
                    </label>
                    <input
                      type="number"
                      value={formData.rules.registrationDays}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        rules: { ...prev.rules, registrationDays: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="e.g., 30"
                      min="0"
                    />
                    {errors.registrationDays && <p className="text-red-500 text-xs mt-1">{errors.registrationDays}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Last Activity Days
                    </label>
                    <input
                      type="number"
                      value={formData.rules.lastActivityDays}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        rules: { ...prev.rules, lastActivityDays: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="e.g., 60"
                      min="0"
                    />
                    {errors.lastActivityDays && <p className="text-red-500 text-xs mt-1">{errors.lastActivityDays}</p>}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Benefits */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Group Benefits</CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Discount Rate (%)
                  </label>
                  <input
                    type="number"
                    value={formData.discountRate}
                    onChange={(e) => setFormData(prev => ({ ...prev, discountRate: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., 10"
                    min="0"
                    max="100"
                  />
                  {errors.discountRate && <p className="text-red-500 text-xs mt-1">{errors.discountRate}</p>}
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <div className="flex gap-3 pt-4">
              <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                Create Group
              </Button>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
