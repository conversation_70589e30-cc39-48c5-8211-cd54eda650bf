import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { InventoryService } from '../lib/inventory-service'
import { Button } from '../components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Input } from '../components/ui/input'
import { Label } from '../components/ui/label'
import { Textarea } from '../components/ui/textarea'
import { Select } from '../components/ui/select'
import { Checkbox } from '../components/ui/checkbox'
import { ArrowLeft, Save, Package, Upload, Download } from 'lucide-react'
import type { InventoryItem, BulkInventoryUpdate } from '../types/inventory'

export default function InventoryBulkAdjustPage() {
  const navigate = useNavigate()
  const { user } = useFirebaseAuthStore()

  // State
  const [items, setItems] = useState<InventoryItem[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [saving, setSaving] = useState(false)
  const [bulkAction, setBulkAction] = useState<'increase' | 'decrease' | 'set'>('increase')
  const [bulkQuantity, setBulkQuantity] = useState(0)
  const [bulkReason, setBulkReason] = useState('restock')
  const [bulkNote, setBulkNote] = useState('')

  // Initialize service
  const storeId = 'womanza-jewelry-store' // Use default store for demo
  const inventoryService = new InventoryService(storeId)

  // Load inventory items
  const loadItems = async () => {
    try {
      setLoading(true)
      const data = await inventoryService.getInventoryOverview()
      setItems(data.items)
    } catch (error) {
      console.error('Error loading inventory:', error)
      toast.error('Failed to load inventory data', {
        style: {
          background: '#EF4444',
          color: 'white',
        }
      })
    } finally {
      setLoading(false)
    }
  }

  // Load data on mount
  useEffect(() => {
    loadItems()
  }, [])

  // Handle item selection
  const handleItemSelect = (itemId: string, selected: boolean) => {
    if (selected) {
      setSelectedItems(prev => [...prev, itemId])
    } else {
      setSelectedItems(prev => prev.filter(id => id !== itemId))
    }
  }

  // Handle select all
  const handleSelectAll = () => {
    if (selectedItems.length === items.length) {
      setSelectedItems([])
    } else {
      setSelectedItems(items.map(item => item.id))
    }
  }

  // Handle bulk adjustment
  const handleBulkAdjust = async () => {
    if (selectedItems.length === 0) {
      toast.error('Please select items to adjust')
      return
    }

    if (!bulkQuantity || bulkQuantity <= 0) {
      toast.error('Please enter a valid quantity')
      return
    }

    try {
      setSaving(true)

      // Process each selected item
      for (const itemId of selectedItems) {
        const item = items.find(i => i.id === itemId)
        if (!item) continue

        const adjustment = {
          productId: item.productId,
          variantId: item.variantId,
          action: bulkAction,
          quantity: bulkQuantity,
          note: bulkNote,
          reason: bulkReason
        }

        await inventoryService.adjustInventory(adjustment, 'admin', 'Admin User')
      }

      toast.success(`Successfully adjusted ${selectedItems.length} items`, {
        style: {
          background: '#10B981',
          color: 'white',
        }
      })
      navigate('/admin/inventory')
    } catch (error) {
      console.error('Error performing bulk adjustment:', error)
      toast.error('Failed to perform bulk adjustment', {
        style: {
          background: '#EF4444',
          color: 'white',
        }
      })
    } finally {
      setSaving(false)
    }
  }

  // Export selected items
  const handleExport = () => {
    const selectedItemsData = items.filter(item => selectedItems.includes(item.id))
    
    const csvData = selectedItemsData.map(item => ({
      'Product Name': item.name,
      'SKU': item.sku,
      'Current Stock': item.currentStock,
      'Low Stock Threshold': item.lowStockThreshold,
      'Status': item.isOutOfStock ? 'Out of Stock' : item.currentStock <= item.lowStockThreshold ? 'Low Stock' : 'In Stock',
      'Value': item.currentStock * item.price,
      'Category': item.category || 'Uncategorized'
    }))

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `inventory-bulk-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }



  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading inventory items...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          onClick={() => navigate('/admin/inventory')}
          className="hover:bg-gray-100"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
            Bulk Inventory Adjustment
          </h1>
          <p className="text-gray-500 dark:text-gray-400">
            Select items and adjust inventory in bulk
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Items Selection */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Select Items ({selectedItems.length} selected)</CardTitle>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                >
                  {selectedItems.length === items.length ? 'Deselect All' : 'Select All'}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExport}
                  disabled={selectedItems.length === 0}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {items.length > 0 ? (
                items.map((item) => (
                  <div key={item.id} className="flex items-center gap-3 p-3 border rounded-lg">
                    <Checkbox
                      checked={selectedItems.includes(item.id)}
                      onCheckedChange={(checked) => handleItemSelect(item.id, checked as boolean)}
                    />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {item.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        SKU: {item.sku} • Stock: {item.currentStock}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">
                        {item.isOutOfStock ? (
                          <span className="text-red-600">Out of Stock</span>
                        ) : item.currentStock <= item.lowStockThreshold ? (
                          <span className="text-yellow-600">Low Stock</span>
                        ) : (
                          <span className="text-green-600">In Stock</span>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Package className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm">No inventory items found</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate('/admin/inventory')}
                    className="mt-2"
                  >
                    Go to Inventory
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Bulk Action Form */}
        <Card>
          <CardHeader>
            <CardTitle>Bulk Adjustment</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Action Type */}
            <div>
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Action Type
              </Label>
              <Select
                value={bulkAction}
                onValueChange={(value: 'increase' | 'decrease' | 'set') => setBulkAction(value)}
                options={[
                  { value: 'increase', label: '📈 Increase Stock' },
                  { value: 'decrease', label: '📉 Decrease Stock' },
                  { value: 'set', label: '🔄 Set Exact Amount' }
                ]}
                className="mt-1"
              />
            </div>

            {/* Quantity */}
            <div>
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {bulkAction === 'set' ? 'New Stock Amount' : 'Quantity'}
              </Label>
              <Input
                type="number"
                min="0"
                value={bulkQuantity}
                onChange={(e) => setBulkQuantity(parseInt(e.target.value) || 0)}
                className="mt-1"
                placeholder="Enter quantity"
              />
            </div>

            {/* Reason */}
            <div>
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Reason
              </Label>
              <Select
                value={bulkReason}
                onValueChange={(value: any) => setBulkReason(value)}
                options={[
                  { value: 'restock', label: 'Restock' },
                  { value: 'damage', label: 'Damage' },
                  { value: 'theft', label: 'Theft' },
                  { value: 'correction', label: 'Correction' },
                  { value: 'return', label: 'Return' },
                  { value: 'transfer', label: 'Transfer' },
                  { value: 'other', label: 'Other' }
                ]}
                className="mt-1"
              />
            </div>

            {/* Note */}
            <div>
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Note (Optional)
              </Label>
              <Textarea
                value={bulkNote}
                onChange={(e) => setBulkNote(e.target.value)}
                className="mt-1"
                placeholder="Add a note about this bulk adjustment..."
                rows={3}
              />
            </div>

            {/* Actions */}
            <div className="flex flex-col gap-2 pt-4 border-t">
              <Button
                onClick={handleBulkAdjust}
                disabled={saving || selectedItems.length === 0}
                className="w-full"
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Adjust {selectedItems.length} Items
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={() => navigate('/admin/inventory')}
                disabled={saving}
                className="w-full"
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
