import React, { useState, useRef, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'

// UI Components
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { Label } from '../components/ui/label'
import { Badge } from '../components/ui/badge'
import { Select } from '../components/ui/select'
import { Textarea } from '../components/ui/textarea'
import { Switch } from '../components/ui/switch'
import { Checkbox } from '../components/ui/checkbox'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { HierarchicalSelect } from '../components/ui/hierarchical-select'
import { FallbackImage } from '../components/ui/fallback-image'

// Icons
import {
  Save,
  CheckCircle,
  Upload,
  X,
  Plus,
  Package,
  DollarSign,
  Image as ImageIcon,
  Video,
  Tag,
  Trash2,
  GripVertical,
  Search,
  Star,
  Info,
  AlertTriangle,
  Globe,
  Link as LinkIcon,
  Eye,
  Calendar,
  Clock
} from 'lucide-react'

// Types
import { ProductFormData, ValidationErrors, UploadProgress, Category } from '../types/product'

// Services
import { StorageService } from '../lib/storage-service'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { dataService } from '../lib/data-service'
import { useCategoriesStore } from '../store/categories-store'

// Tab type definition
type TabType = 'basic' | 'media' | 'pricing' | 'variants' | 'seo' | 'related' | 'review'

export function AddProductPage() {
  const navigate = useNavigate()
  const { user, hasPermission } = useFirebaseAuthStore()
  const { categories, loading: categoriesLoading, fetchCategories, subscribeToCategories } = useCategoriesStore()
  const storageService = new StorageService()

  // State management
  const [activeTab, setActiveTab] = useState<TabType>('basic')
  const [saving, setSaving] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<{ [key: number]: number }>({})
  const [errors, setErrors] = useState<ValidationErrors>({})
  const [newTag, setNewTag] = useState('')

  // Variant state
  const [selectedSizes, setSelectedSizes] = useState<string[]>([])
  const [selectedColors, setSelectedColors] = useState<string[]>([])
  const [selectedMaterials, setSelectedMaterials] = useState<string[]>([])
  const [customSize, setCustomSize] = useState('')
  const [customColor, setCustomColor] = useState('')

  // Related products state
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedRelatedProducts, setSelectedRelatedProducts] = useState<string[]>([])

  // Variant settings state
  const [variantSettings, setVariantSettings] = useState({
    trackInventory: false,
    allowCustomerSelection: true,
    showVariantImages: false
  })

  // Related products settings state
  const [relatedSettings, setRelatedSettings] = useState({
    showOnProductPage: true,
    showInCart: false,
    maxProducts: 4,
    sectionTitle: 'You might also like'
  })

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null)
  const autoGeneratedRef = useRef({
    slug: false,
    sku: false,
    seoTitle: false
  })

  // Form data state
  const [form, setForm] = useState<ProductFormData>({
    // Basic Info
    name: '',
    slug: '',
    description: '',
    categoryId: '',
    tags: [],
    status: 'draft',

    // Images & Media
    images: [],
    videoUrl: '',

    // Pricing & Inventory
    price: {
      regular: 0,
      sale: 0
    },
    sku: '',
    stock: {
      quantity: 0,
      status: 'in_stock',
      allowBackorders: false
    },
    unit: 'pcs',

    // Variants
    variants: [],
    variantAttributes: {},

    // SEO
    seo: {
      title: '',
      description: '',
      canonicalUrl: ''
    },

    // Related Products
    relatedProducts: [],

    // Additional
    featured: false
  })

  // Utility functions
  const updateForm = (field: keyof ProductFormData, value: any) => {
    setForm(prev => ({ ...prev, [field]: value }))
    // Clear errors when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  // Variant helper functions
  const toggleSize = (size: string) => {
    setSelectedSizes(prev =>
      prev.includes(size)
        ? prev.filter(s => s !== size)
        : [...prev, size]
    )
  }

  const toggleColor = (color: string) => {
    setSelectedColors(prev =>
      prev.includes(color)
        ? prev.filter(c => c !== color)
        : [...prev, color]
    )
  }

  const toggleMaterial = (material: string) => {
    setSelectedMaterials(prev =>
      prev.includes(material)
        ? prev.filter(m => m !== material)
        : [...prev, material]
    )
  }

  const addCustomSize = () => {
    if (customSize.trim() && !selectedSizes.includes(customSize.trim())) {
      setSelectedSizes(prev => [...prev, customSize.trim()])
      setCustomSize('')
    }
  }

  const addCustomColor = () => {
    if (customColor.trim() && !selectedColors.includes(customColor.trim())) {
      setSelectedColors(prev => [...prev, customColor.trim()])
      setCustomColor('')
    }
  }

  // Related products helper functions
  const toggleRelatedProduct = (productId: string) => {
    setSelectedRelatedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    )
  }

  const generateSlug = (name: string): string => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')
  }

  const generateSKU = (name: string): string => {
    const prefix = name.substring(0, 3).toUpperCase()
    const timestamp = Date.now().toString().slice(-6)
    return `${prefix}-${timestamp}`
  }

  // Auto-generate slug, SKU, and SEO title when name changes
  useEffect(() => {
    if (form.name && form.name.trim()) {
      let hasUpdates = false
      const updates: Partial<ProductFormData> = {}

      if (!form.slug && !autoGeneratedRef.current.slug) {
        updates.slug = generateSlug(form.name)
        autoGeneratedRef.current.slug = true
        hasUpdates = true
      }

      if (!form.sku && !autoGeneratedRef.current.sku) {
        updates.sku = generateSKU(form.name)
        autoGeneratedRef.current.sku = true
        hasUpdates = true
      }

      if (!form.seo.title && !autoGeneratedRef.current.seoTitle) {
        updates.seo = { ...form.seo, title: `${form.name} - Womanza` }
        autoGeneratedRef.current.seoTitle = true
        hasUpdates = true
      }

      if (hasUpdates) {
        setForm(prev => ({ ...prev, ...updates }))
      }
    }
  }, [form.name]) // Fixed: Only depend on form.name to prevent infinite loop

  // Load categories on component mount and set up real-time subscription
  useEffect(() => {
    // Fetch categories initially
    fetchCategories()

    // Set up real-time subscription
    const unsubscribe = subscribeToCategories()

    // Cleanup subscription on unmount
    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
    }
  }, [])

  // Validation functions
  const validateForDraft = (): boolean => {
    const newErrors: ValidationErrors = {}
    if (!form.name.trim()) {
      newErrors.name = 'Product name is required'
    }
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const validateForPublishing = (): boolean => {
    const newErrors: ValidationErrors = {}

    // Required fields for publishing
    if (!form.name.trim()) {
      newErrors.name = 'Product name is required'
    } else if (form.name.length < 3) {
      newErrors.name = 'Product name must be at least 3 characters'
    }

    if (!form.categoryId) {
      newErrors.categoryId = 'Category is required'
    }

    if (form.price.regular <= 0) {
      newErrors.price = 'Regular price must be greater than 0'
    }

    if (!form.slug.trim()) {
      newErrors.slug = 'Product slug is required'
    } else if (!/^[a-z0-9-]+$/.test(form.slug)) {
      newErrors.slug = 'Slug can only contain lowercase letters, numbers, and hyphens'
    }

    if (!form.sku.trim()) {
      newErrors.sku = 'SKU is required for published products'
    }

    // Business rules
    if (form.price.sale && form.price.sale >= form.price.regular) {
      newErrors.salePrice = 'Sale price must be less than regular price'
    }

    if (form.stock.quantity < 0) {
      newErrors.stock = 'Stock quantity cannot be negative'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const canPublish = (): boolean => {
    return !!(
      form.name.trim() &&
      form.categoryId &&
      form.price.regular > 0 &&
      form.slug.trim()
    )
  }

  // Image upload handlers
  const handleFileSelect = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length === 0) return

    setUploading(true)
    const newProgress: { [key: number]: number } = {}

    try {
      const uploadPromises = files.map(async (file, index) => {
        const startIndex = form.images.length + index
        newProgress[startIndex] = 0
        setUploadProgress({ ...newProgress })

        const url = await storageService.uploadFile(
          file,
          'products',
          (progress) => {
            newProgress[startIndex] = progress.progress
            setUploadProgress({ ...newProgress })
          }
        )

        return url
      })

      const uploadedUrls = await Promise.all(uploadPromises)
      updateForm('images', [...form.images, ...uploadedUrls])
      toast.success(`${uploadedUrls.length} image(s) uploaded successfully`)
    } catch (error) {
      console.error('Upload error:', error)
      toast.error('Failed to upload images')
    } finally {
      setUploading(false)
      setUploadProgress({})
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      const input = fileInputRef.current
      if (input) {
        const dt = new DataTransfer()
        files.forEach(file => dt.items.add(file))
        input.files = dt.files
        handleFileChange({ target: input } as any)
      }
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  // Tag management
  const addTag = () => {
    if (newTag.trim() && !form.tags.includes(newTag.trim())) {
      updateForm('tags', [...form.tags, newTag.trim()])
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    updateForm('tags', form.tags.filter(tag => tag !== tagToRemove))
  }

  // Save functionality
  const handleSave = async (status: 'draft' | 'published') => {
    // Apply appropriate validation
    if (status === 'published' && !validateForPublishing()) {
      toast.error('Please fix the validation errors before publishing')
      return
    }

    if (status === 'draft' && !validateForDraft()) {
      toast.error('Product name is required to save as draft')
      return
    }

    setSaving(true)
    try {
      // Map form data to Firebase schema
      const productData = {
        name: form.name,
        slug: form.slug || generateSlug(form.name),
        description: form.description || null,
        categoryId: form.categoryId || null,
        tags: form.tags || [],
        images: form.images || [],
        videoUrl: form.videoUrl || null,
        price: {
          regular: form.price.regular || 0,
          sale: form.price.sale && form.price.sale > 0 ? form.price.sale : null
        },
        sku: form.sku || generateSKU(form.name),
        stock: {
          quantity: form.stock.quantity || 0,
          status: form.stock.status || 'in_stock',
          allowBackorders: form.stock.allowBackorders || false
        },
        unit: form.unit || 'pcs',
        variants: form.variants || [],
        variantAttributes: {
          sizes: selectedSizes,
          colors: selectedColors,
          materials: selectedMaterials,
          settings: variantSettings
        },
        seo: {
          title: form.seo.title || `${form.name} - Womanza`,
          description: form.seo.description || null,
          canonicalUrl: form.seo.canonicalUrl || null
        },
        relatedProducts: selectedRelatedProducts,
        relatedProductsSettings: relatedSettings,
        featured: form.featured || false,
        status: status,
        storeId: import.meta.env.VITE_STORE_ID || '4db760eb-679e-4783-a563-717c0b12dec2',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...(status === 'published' && { publishedAt: new Date().toISOString() })
      }

      // Save to data service
      console.log('Product data to save:', productData)

      // Use the data service to create the product
      const savedProduct = await dataService.createProduct(productData)
      console.log('Product saved successfully:', savedProduct)

      toast.success(`Product ${status === 'published' ? 'published' : 'saved as draft'} successfully!`)
      navigate('/admin/products')
    } catch (error) {
      console.error('Error saving product:', error)
      toast.error('Failed to save product')
    } finally {
      setSaving(false)
    }
  }

  // Tab configuration
  const tabs = [
    { id: 'basic' as TabType, label: 'Basic Info', icon: Package },
    { id: 'media' as TabType, label: 'Images & Media', icon: ImageIcon },
    { id: 'pricing' as TabType, label: 'Pricing & Inventory', icon: DollarSign },
    { id: 'variants' as TabType, label: 'Variants', icon: Tag },
    { id: 'seo' as TabType, label: 'SEO', icon: Globe },
    { id: 'related' as TabType, label: 'Related Products', icon: LinkIcon },
    { id: 'review' as TabType, label: 'Review & Publish', icon: CheckCircle }
  ]

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">Add New Product</h1>
          <p className="text-gray-500 dark:text-gray-400">Create a new product for your store catalog</p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={() => handleSave('draft')}
            disabled={saving}
          >
            <Save className="h-4 w-4 mr-2" />
            Save as Draft
          </Button>
          <Button
            onClick={() => handleSave('published')}
            disabled={saving || !canPublish()}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            {saving ? 'Publishing...' : 'Publish Product'}
          </Button>
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Progress</span>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {Object.values(tabs).findIndex(tab => tab.id === activeTab) + 1} of {tabs.length}
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((Object.values(tabs).findIndex(tab => tab.id === activeTab) + 1) / tabs.length) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Tabs Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => {
              const Icon = tab.icon
              const isActive = activeTab === tab.id
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    isActive
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors`}
                >
                  <Icon className="h-4 w-4" />
                  {tab.label}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {/* Basic Info Tab */}
          {activeTab === 'basic' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Left Column */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name" className="text-sm font-medium">Product Name *</Label>
                    <Input
                      id="name"
                      value={form.name}
                      onChange={(e) => updateForm('name', e.target.value)}
                      placeholder="Enter product name"
                      className={`mt-1 ${errors.name ? 'border-red-500' : ''}`}
                    />
                    {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
                  </div>

                  <div>
                    <Label htmlFor="slug" className="text-sm font-medium">Product Slug *</Label>
                    <Input
                      id="slug"
                      value={form.slug}
                      onChange={(e) => updateForm('slug', e.target.value)}
                      placeholder="product-slug"
                      className={`mt-1 ${errors.slug ? 'border-red-500' : ''}`}
                    />
                    {errors.slug && <p className="text-red-500 text-xs mt-1">{errors.slug}</p>}
                    <p className="text-xs text-gray-500 mt-1">URL-friendly version of the product name</p>
                  </div>

                  <div>
                    <Label htmlFor="category" className="text-sm font-medium">Category *</Label>
                    <HierarchicalSelect
                      options={categories.map(cat => ({
                        value: cat.id,
                        label: cat.name,
                        parentId: cat.parentId
                      }))}
                      value={form.categoryId}
                      onValueChange={(value) => updateForm('categoryId', value)}
                      placeholder={categoriesLoading ? "Loading categories..." : "Select a category"}
                      className={`mt-1 ${errors.categoryId ? 'border-red-500' : ''}`}
                      disabled={categoriesLoading}
                    />
                    {errors.categoryId && <p className="text-red-500 text-xs mt-1">{errors.categoryId}</p>}
                    <p className="text-xs text-gray-500 mt-1">Choose the most specific category for your product</p>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Tags</Label>
                    <div className="mt-1 space-y-2">
                      <div className="flex gap-2">
                        <Input
                          value={newTag}
                          onChange={(e) => setNewTag(e.target.value)}
                          placeholder="Add a tag"
                          onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                        />
                        <Button type="button" onClick={addTag} size="sm">
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      {form.tags.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {form.tags.map((tag, index) => (
                            <Badge key={index} variant="secondary" className="flex items-center gap-1">
                              {tag}
                              <button
                                type="button"
                                onClick={() => removeTag(tag)}
                                className="ml-1 hover:text-red-500"
                              >
                                <X className="h-3 w-3" />
                              </button>
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Right Column */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="description" className="text-sm font-medium">Description</Label>
                    <Textarea
                      id="description"
                      value={form.description}
                      onChange={(e) => updateForm('description', e.target.value)}
                      placeholder="Describe your product..."
                      className="mt-1"
                      rows={6}
                    />
                    <p className="text-xs text-gray-500 mt-1">Detailed product description for customers</p>
                  </div>

                  <div>
                    <Label htmlFor="status" className="text-sm font-medium">Product Status</Label>
                    <Select
                      value={form.status}
                      onValueChange={(value) => updateForm('status', value as 'draft' | 'published' | 'archived')}
                      placeholder="Select status"
                      options={[
                        { value: 'draft', label: 'Draft' },
                        { value: 'published', label: 'Published' },
                        { value: 'archived', label: 'Archived' }
                      ]}
                      className="mt-1"
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="featured"
                      checked={form.featured}
                      onCheckedChange={(checked) => updateForm('featured', checked)}
                    />
                    <Label htmlFor="featured" className="text-sm font-normal cursor-pointer">
                      Featured Product
                    </Label>
                  </div>
                  <p className="text-xs text-gray-500">Featured products appear prominently on your store</p>

                  <div className="bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Info className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      <h3 className="font-medium text-blue-900 dark:text-blue-100">Next Steps</h3>
                    </div>
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      Complete the basic information, then move to the next tabs to add images, pricing, and other details.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Images & Media Tab */}
          {activeTab === 'media' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left Column - Upload Section */}
              <div className="space-y-6">
                <div>
                  <Label className="text-lg font-medium">Upload Images</Label>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Add high-quality product images
                  </p>
                  <Badge variant="outline" className="text-xs mt-2">
                    {form.images.length}/10 images
                  </Badge>
                </div>

                <div
                  className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-200 ${
                    uploading
                      ? 'border-blue-400 bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20'
                      : 'border-gray-300 dark:border-gray-600 hover:border-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700 dark:hover:bg-opacity-50'
                  }`}
                  onClick={handleFileSelect}
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                >
                  {uploading ? (
                    <div className="space-y-3">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                      <p className="text-blue-600 font-medium text-sm">Uploading...</p>
                      {Object.keys(uploadProgress).length > 0 && (
                        <div className="space-y-1 max-w-xs mx-auto">
                          {Object.entries(uploadProgress).map(([index, progress]) => (
                            <div key={index} className="w-full bg-gray-200 rounded-full h-1.5">
                              <div
                                className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                                style={{ width: `${progress}%` }}
                              ></div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <Upload className="h-12 w-12 text-gray-400 mx-auto" />
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                          Upload Images
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mb-3">
                          Drag & drop or click to browse
                        </p>
                        <Button variant="outline" size="sm" disabled={form.images.length >= 10}>
                          <ImageIcon className="h-4 w-4 mr-2" />
                          {form.images.length >= 10 ? 'Max reached' : 'Choose Files'}
                        </Button>
                      </div>
                      <div className="text-xs text-gray-500 space-y-0.5">
                        <p>• 1200x1200px recommended</p>
                        <p>• JPG, PNG, WebP • Max 5MB</p>
                      </div>
                    </div>
                  )}
                </div>

                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleFileChange}
                  className="hidden"
                />

                {/* Video Section */}
                <div>
                  <Label htmlFor="videoUrl" className="text-lg font-medium">Product Video</Label>
                  <Badge variant="outline" className="text-xs ml-2">Optional</Badge>
                  <div className="mt-2">
                    <Input
                      id="videoUrl"
                      value={form.videoUrl}
                      onChange={(e) => updateForm('videoUrl', e.target.value)}
                      placeholder="https://youtube.com/watch?v=..."
                      className="mt-1"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      YouTube or Vimeo URL
                    </p>
                  </div>

                  {form.videoUrl && (
                    <div className="mt-3 bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                      <div className="aspect-video bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center">
                        <div className="text-center">
                          <Video className="h-6 w-6 text-gray-400 mx-auto mb-1" />
                          <p className="text-xs text-gray-500">Video Preview</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Right Column - Image Gallery */}
              <div className="space-y-6">
                {form.images.length > 0 ? (
                  <div>
                    <Label className="text-lg font-medium">Image Gallery</Label>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      Manage your product images
                    </p>

                    {/* Main Image */}
                    <div className="mt-4">
                      <Label className="text-sm font-medium mb-2 block">Main Product Image</Label>
                      <div className="relative bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden">
                        <div className="aspect-square max-w-sm mx-auto">
                          <FallbackImage
                            src={form.images[0]}
                            alt="Main product image"
                            className="w-full h-full object-cover"
                            fallbackClassName="w-full h-full rounded-lg"
                          />
                        </div>
                        <div className="absolute top-3 left-3">
                          <Badge className="bg-blue-600 hover:bg-blue-600">
                            <Star className="h-3 w-3 mr-1" />
                            Main
                          </Badge>
                        </div>
                        <div className="absolute top-3 right-3 flex gap-2">
                          <Button
                            size="sm"
                            variant="secondary"
                            className="bg-white bg-opacity-90 text-gray-900 hover:bg-white h-8 w-8 p-0"
                            title="Drag to reorder"
                          >
                            <GripVertical className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            className="bg-red-500 bg-opacity-90 hover:bg-red-600 h-8 w-8 p-0"
                            onClick={() => updateForm('images', form.images.filter((_, i) => i !== 0))}
                            title="Remove image"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* Additional Images */}
                    {form.images.length > 1 && (
                      <div className="mt-4">
                        <Label className="text-sm font-medium mb-2 block">Additional Images</Label>
                        <div className="grid grid-cols-3 gap-3">
                          {form.images.slice(1).map((image, index) => (
                            <div key={index + 1} className="relative group bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden">
                              <div className="aspect-square">
                                <FallbackImage
                                  src={image}
                                  alt={`Product image ${index + 2}`}
                                  className="w-full h-full object-cover"
                                  fallbackClassName="w-full h-full rounded-lg"
                                />
                              </div>

                              <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-all duration-200 flex items-center justify-center">
                                <div className="flex gap-1">
                                  <Button
                                    size="sm"
                                    variant="secondary"
                                    className="bg-white bg-opacity-90 text-gray-900 hover:bg-white h-7 w-7 p-0"
                                    title="Drag to reorder"
                                  >
                                    <GripVertical className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="destructive"
                                    className="bg-red-500 bg-opacity-90 hover:bg-red-600 h-7 w-7 p-0"
                                    onClick={() => updateForm('images', form.images.filter((_, i) => i !== index + 1))}
                                    title="Remove image"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>

                              <div className="absolute bottom-1 right-1 bg-black bg-opacity-70 text-white text-xs px-1.5 py-0.5 rounded">
                                {index + 2}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No Images Yet</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                      Upload images to see them here
                    </p>
                    <Button
                      variant="outline"
                      onClick={handleFileSelect}
                      className="border-blue-300 text-blue-700 hover:bg-blue-50"
                    >
                      <ImageIcon className="h-4 w-4 mr-2" />
                      Upload First Image
                    </Button>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Pricing & Inventory Tab */}
          {activeTab === 'pricing' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Left Column - Pricing */}
                <div className="space-y-4">
                  <div>
                    <Label className="text-lg font-medium mb-4 block">Pricing</Label>

                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="regularPrice" className="text-sm font-medium">Regular Price *</Label>
                        <div className="mt-1 relative">
                          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                          <Input
                            id="regularPrice"
                            type="number"
                            step="0.01"
                            min="0"
                            value={form.price.regular || ''}
                            onChange={(e) => updateForm('price', { ...form.price, regular: parseFloat(e.target.value) || 0 })}
                            placeholder="0.00"
                            className={`pl-8 ${errors.price ? 'border-red-500' : ''}`}
                          />
                        </div>
                        {errors.price && <p className="text-red-500 text-xs mt-1">{errors.price}</p>}
                      </div>

                      <div>
                        <Label htmlFor="salePrice" className="text-sm font-medium">Sale Price</Label>
                        <div className="mt-1 relative">
                          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                          <Input
                            id="salePrice"
                            type="number"
                            step="0.01"
                            min="0"
                            value={form.price.sale || ''}
                            onChange={(e) => updateForm('price', { ...form.price, sale: parseFloat(e.target.value) || 0 })}
                            placeholder="0.00"
                            className={`pl-8 ${errors.salePrice ? 'border-red-500' : ''}`}
                          />
                        </div>
                        {errors.salePrice && <p className="text-red-500 text-xs mt-1">{errors.salePrice}</p>}
                        <p className="text-xs text-gray-500 mt-1">Leave empty if not on sale</p>
                      </div>

                      {form.price.sale && form.price.sale > 0 && form.price.regular > 0 && (
                        <div className="bg-green-50 dark:bg-green-900 dark:bg-opacity-20 border border-green-200 dark:border-green-800 rounded-lg p-3">
                          <div className="flex items-center gap-2">
                            <Tag className="h-4 w-4 text-green-600 dark:text-green-400" />
                            <span className="text-sm font-medium text-green-800 dark:text-green-200">
                              {Math.round(((form.price.regular - form.price.sale) / form.price.regular) * 100)}% OFF
                            </span>
                          </div>
                          <p className="text-xs text-green-700 dark:text-green-300 mt-1">
                            Customers save ${(form.price.regular - form.price.sale).toFixed(2)}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="sku" className="text-sm font-medium">SKU (Stock Keeping Unit) *</Label>
                    <Input
                      id="sku"
                      value={form.sku}
                      onChange={(e) => updateForm('sku', e.target.value)}
                      placeholder="Enter SKU"
                      className={`mt-1 ${errors.sku ? 'border-red-500' : ''}`}
                    />
                    {errors.sku && <p className="text-red-500 text-xs mt-1">{errors.sku}</p>}
                    <p className="text-xs text-gray-500 mt-1">Unique identifier for inventory tracking</p>
                  </div>

                  <div>
                    <Label htmlFor="unit" className="text-sm font-medium">Unit</Label>
                    <Select
                      value={form.unit}
                      onValueChange={(value) => updateForm('unit', value as 'gm' | 'ml' | 'pcs')}
                      placeholder="Select unit"
                      options={[
                        { value: 'pcs', label: 'Pieces (pcs)' },
                        { value: 'gm', label: 'Grams (gm)' },
                        { value: 'ml', label: 'Milliliters (ml)' }
                      ]}
                      className="mt-1"
                    />
                  </div>
                </div>

                {/* Right Column - Inventory */}
                <div className="space-y-4">
                  <div>
                    <Label className="text-lg font-medium mb-4 block">Inventory</Label>

                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="quantity" className="text-sm font-medium">Stock Quantity</Label>
                        <Input
                          id="quantity"
                          type="number"
                          min="0"
                          value={form.stock.quantity || ''}
                          onChange={(e) => updateForm('stock', { ...form.stock, quantity: parseInt(e.target.value) || 0 })}
                          placeholder="0"
                          className={`mt-1 ${errors.stock ? 'border-red-500' : ''}`}
                        />
                        {errors.stock && <p className="text-red-500 text-xs mt-1">{errors.stock}</p>}
                      </div>

                      <div>
                        <Label htmlFor="stockStatus" className="text-sm font-medium">Stock Status</Label>
                        <Select
                          value={form.stock.status}
                          onValueChange={(value) => updateForm('stock', { ...form.stock, status: value as 'in_stock' | 'out_of_stock' })}
                          placeholder="Select status"
                          options={[
                            { value: 'in_stock', label: 'In Stock' },
                            { value: 'out_of_stock', label: 'Out of Stock' }
                          ]}
                          className="mt-1"
                        />
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="allowBackorders"
                          checked={form.stock.allowBackorders}
                          onCheckedChange={(checked) => updateForm('stock', { ...form.stock, allowBackorders: checked as boolean })}
                        />
                        <Label htmlFor="allowBackorders" className="text-sm font-normal cursor-pointer">
                          Allow Backorders
                        </Label>
                      </div>
                      <p className="text-xs text-gray-500">Allow customers to order when out of stock</p>

                      {/* Stock Status Indicator */}
                      <div className={`p-3 rounded-lg border ${
                        form.stock.status === 'in_stock'
                          ? 'bg-green-50 dark:bg-green-900 dark:bg-opacity-20 border-green-200 dark:border-green-800'
                          : 'bg-red-50 dark:bg-red-900 dark:bg-opacity-20 border-red-200 dark:border-red-800'
                      }`}>
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${
                            form.stock.status === 'in_stock' ? 'bg-green-500' : 'bg-red-500'
                          }`}></div>
                          <span className={`text-sm font-medium ${
                            form.stock.status === 'in_stock'
                              ? 'text-green-800 dark:text-green-200'
                              : 'text-red-800 dark:text-red-200'
                          }`}>
                            {form.stock.status === 'in_stock' ? 'In Stock' : 'Out of Stock'}
                          </span>
                        </div>
                        <p className={`text-xs mt-1 ${
                          form.stock.status === 'in_stock'
                            ? 'text-green-700 dark:text-green-300'
                            : 'text-red-700 dark:text-red-300'
                        }`}>
                          {form.stock.quantity} {form.unit} available
                          {form.stock.allowBackorders && form.stock.status === 'out_of_stock' && ' (Backorders allowed)'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Variants Tab */}
          {activeTab === 'variants' && (
            <div className="space-y-6">
              {/* Variant Attributes */}
              <Card>
                <CardHeader>
                  <CardTitle>Product Attributes</CardTitle>
                  <CardDescription>
                    Define attributes like size, color, or material that create product variations
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Size Options</Label>
                      <div className="mt-2 space-y-2">
                        <div className="flex flex-wrap gap-2">
                          {['XS', 'S', 'M', 'L', 'XL', 'XXL'].map((size) => (
                            <Button
                              key={size}
                              variant={selectedSizes.includes(size) ? "default" : "outline"}
                              size="sm"
                              className="h-8 px-3"
                              onClick={() => toggleSize(size)}
                            >
                              {size}
                            </Button>
                          ))}
                        </div>
                        <div className="flex gap-2">
                          <Input
                            placeholder="Add custom size"
                            className="text-sm"
                            value={customSize}
                            onChange={(e) => setCustomSize(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && addCustomSize()}
                          />
                          <Button
                            type="button"
                            size="sm"
                            onClick={addCustomSize}
                            disabled={!customSize.trim()}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                        {selectedSizes.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {selectedSizes.map((size) => (
                              <Badge
                                key={size}
                                variant="secondary"
                                className="text-xs cursor-pointer"
                                onClick={() => toggleSize(size)}
                              >
                                {size} ×
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Color Options</Label>
                      <div className="mt-2 space-y-2">
                        <div className="grid grid-cols-4 gap-2">
                          {[
                            { name: 'Black', color: '#000000' },
                            { name: 'White', color: '#FFFFFF' },
                            { name: 'Red', color: '#EF4444' },
                            { name: 'Blue', color: '#3B82F6' },
                            { name: 'Green', color: '#10B981' },
                            { name: 'Yellow', color: '#F59E0B' },
                            { name: 'Purple', color: '#8B5CF6' },
                            { name: 'Pink', color: '#EC4899' }
                          ].map((colorOption) => (
                            <div
                              key={colorOption.name}
                              className={`flex flex-col items-center p-2 border rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 ${
                                selectedColors.includes(colorOption.name)
                                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                                  : 'border-gray-200 dark:border-gray-700'
                              }`}
                              onClick={() => toggleColor(colorOption.name)}
                            >
                              <div
                                className="w-6 h-6 rounded-full border border-gray-300 dark:border-gray-600"
                                style={{ backgroundColor: colorOption.color }}
                              />
                              <span className="text-xs mt-1">{colorOption.name}</span>
                            </div>
                          ))}
                        </div>
                        <div className="flex gap-2">
                          <Input
                            placeholder="Add custom color"
                            className="text-sm"
                            value={customColor}
                            onChange={(e) => setCustomColor(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && addCustomColor()}
                          />
                          <Button
                            type="button"
                            size="sm"
                            onClick={addCustomColor}
                            disabled={!customColor.trim()}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                        {selectedColors.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {selectedColors.map((color) => (
                              <Badge
                                key={color}
                                variant="secondary"
                                className="text-xs cursor-pointer"
                                onClick={() => toggleColor(color)}
                              >
                                {color} ×
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Material Options</Label>
                    <div className="mt-2 space-y-2">
                      <div className="flex flex-wrap gap-2">
                        {['Cotton', 'Polyester', 'Silk', 'Wool', 'Linen', 'Denim'].map((material) => (
                          <Button
                            key={material}
                            variant={selectedMaterials.includes(material) ? "default" : "outline"}
                            size="sm"
                            className="h-8 px-3"
                            onClick={() => toggleMaterial(material)}
                          >
                            {material}
                          </Button>
                        ))}
                      </div>
                      {selectedMaterials.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {selectedMaterials.map((material) => (
                            <Badge
                              key={material}
                              variant="secondary"
                              className="text-xs cursor-pointer"
                              onClick={() => toggleMaterial(material)}
                            >
                              {material} ×
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>



              {/* Variant Settings */}
              <Card>
                <CardHeader>
                  <CardTitle>Variant Settings</CardTitle>
                  <CardDescription>
                    Configure how variants are displayed and managed
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Track inventory by variant</Label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Enable individual stock tracking for each variant
                      </p>
                    </div>
                    <Switch
                      checked={variantSettings.trackInventory}
                      onCheckedChange={(checked) =>
                        setVariantSettings(prev => ({ ...prev, trackInventory: checked }))
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Allow customers to select variants</Label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Show variant options on product page
                      </p>
                    </div>
                    <Switch
                      checked={variantSettings.allowCustomerSelection}
                      onCheckedChange={(checked) =>
                        setVariantSettings(prev => ({ ...prev, allowCustomerSelection: checked }))
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Show variant images</Label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Display different images for each variant
                      </p>
                    </div>
                    <Switch
                      checked={variantSettings.showVariantImages}
                      onCheckedChange={(checked) =>
                        setVariantSettings(prev => ({ ...prev, showVariantImages: checked }))
                      }
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* SEO Tab */}
          {activeTab === 'seo' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="seoTitle" className="text-sm font-medium">SEO Title</Label>
                    <Input
                      id="seoTitle"
                      value={form.seo.title}
                      onChange={(e) => updateForm('seo', { ...form.seo, title: e.target.value })}
                      placeholder="Product Name - Store Name"
                      className="mt-1"
                      maxLength={60}
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>Appears in search engine results</span>
                      <span>{form.seo.title.length}/60</span>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="seoDescription" className="text-sm font-medium">SEO Description</Label>
                    <Textarea
                      id="seoDescription"
                      value={form.seo.description}
                      onChange={(e) => updateForm('seo', { ...form.seo, description: e.target.value })}
                      placeholder="Brief description for search engines..."
                      className="mt-1"
                      rows={3}
                      maxLength={160}
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>Brief description for search results</span>
                      <span>{form.seo.description.length}/160</span>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="canonicalUrl" className="text-sm font-medium">Canonical URL</Label>
                    <Input
                      id="canonicalUrl"
                      value={form.seo.canonicalUrl}
                      onChange={(e) => updateForm('seo', { ...form.seo, canonicalUrl: e.target.value })}
                      placeholder="https://yourstore.com/products/product-slug"
                      className="mt-1"
                    />
                    <p className="text-xs text-gray-500 mt-1">Preferred URL for search engines</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-gray-50 dark:bg-gray-800 dark:bg-opacity-50 rounded-lg p-4">
                    <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-3">Search Preview</h3>
                    <div className="space-y-2">
                      <div className="text-blue-600 text-sm font-medium truncate">
                        {form.seo.title || `${form.name} - Womanza`}
                      </div>
                      <div className="text-green-600 text-xs truncate">
                        {form.seo.canonicalUrl || `https://womanza.com/products/${form.slug}`}
                      </div>
                      <div className="text-gray-600 text-sm">
                        {form.seo.description || form.description || 'No description available.'}
                      </div>
                    </div>
                  </div>

                  <div className="bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Globe className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      <h3 className="font-medium text-blue-900 dark:text-blue-100">SEO Tips</h3>
                    </div>
                    <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                      <li>• Include your main keyword in the title</li>
                      <li>• Keep titles under 60 characters</li>
                      <li>• Write compelling descriptions under 160 characters</li>
                      <li>• Use descriptive, keyword-rich URLs</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Related Products Tab */}
          {activeTab === 'related' && (
            <div className="space-y-6">
              {/* Search and Add Related Products */}
              <Card>
                <CardHeader>
                  <CardTitle>Add Related Products</CardTitle>
                  <CardDescription>
                    Search and select products to recommend to customers
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search products by name, SKU, or category..."
                      className="pl-10"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>

                  {/* Product Search Results */}
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg max-h-64 overflow-y-auto">
                    <div className="p-2 space-y-1">
                      {/* Sample search results */}
                      {[
                        { id: '1', name: 'Premium Cotton T-Shirt', price: 29.99, category: 'T-Shirts', image: '/placeholder-product.jpg' },
                        { id: '2', name: 'Denim Jacket', price: 89.99, category: 'Jackets', image: '/placeholder-product.jpg' },
                        { id: '3', name: 'Summer Dress', price: 59.99, category: 'Dresses', image: '/placeholder-product.jpg' },
                        { id: '4', name: 'Casual Sneakers', price: 79.99, category: 'Shoes', image: '/placeholder-product.jpg' }
                      ].map((product) => (
                        <div
                          key={product.id}
                          className="flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg cursor-pointer"
                        >
                          <div className="flex items-center gap-3">
                            <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                              <ImageIcon className="h-6 w-6 text-gray-400" />
                            </div>
                            <div>
                              <p className="font-medium text-sm">{product.name}</p>
                              <p className="text-xs text-gray-500">{product.category} • ${product.price}</p>
                            </div>
                          </div>
                          <Button
                            size="sm"
                            variant={selectedRelatedProducts.includes(product.id) ? "default" : "outline"}
                            onClick={() => toggleRelatedProduct(product.id)}
                          >
                            {selectedRelatedProducts.includes(product.id) ? (
                              <CheckCircle className="h-4 w-4" />
                            ) : (
                              <Plus className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>



              {/* Smart Recommendations */}
              <Card>
                <CardHeader>
                  <CardTitle>Smart Recommendations</CardTitle>
                  <CardDescription>
                    AI-suggested products based on category and attributes
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {[
                      { id: '1', name: 'Similar Style Dress', price: 49.99, category: 'Dresses', relevance: 95 },
                      { id: '2', name: 'Matching Accessories', price: 19.99, category: 'Accessories', relevance: 88 },
                      { id: '3', name: 'Complementary Shoes', price: 69.99, category: 'Shoes', relevance: 82 }
                    ].map((suggestion) => (
                      <div
                        key={suggestion.id}
                        className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                            <ImageIcon className="h-8 w-8 text-gray-400" />
                          </div>
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 text-yellow-400 fill-current" />
                            <span className="text-sm font-medium">{suggestion.relevance}%</span>
                          </div>
                        </div>
                        <h4 className="font-medium text-sm mb-1">{suggestion.name}</h4>
                        <p className="text-xs text-gray-500 mb-3">{suggestion.category} • ${suggestion.price}</p>
                        <Button
                          size="sm"
                          variant={selectedRelatedProducts.includes(suggestion.id) ? "default" : "outline"}
                          className="w-full"
                          onClick={() => toggleRelatedProduct(suggestion.id)}
                        >
                          {selectedRelatedProducts.includes(suggestion.id) ? (
                            <>
                              <CheckCircle className="h-4 w-4 mr-2" />
                              Added
                            </>
                          ) : (
                            <>
                              <Plus className="h-4 w-4 mr-2" />
                              Add as Related
                            </>
                          )}
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Related Products Settings */}
              <Card>
                <CardHeader>
                  <CardTitle>Display Settings</CardTitle>
                  <CardDescription>
                    Configure how related products are shown to customers
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Show related products on product page</Label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Display recommendations below product details
                      </p>
                    </div>
                    <Switch
                      checked={relatedSettings.showOnProductPage}
                      onCheckedChange={(checked) =>
                        setRelatedSettings(prev => ({ ...prev, showOnProductPage: checked }))
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Show in cart recommendations</Label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Suggest related products when item is added to cart
                      </p>
                    </div>
                    <Switch
                      checked={relatedSettings.showInCart}
                      onCheckedChange={(checked) =>
                        setRelatedSettings(prev => ({ ...prev, showInCart: checked }))
                      }
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="maxRelated" className="text-sm font-medium">Maximum products to show</Label>
                      <Input
                        id="maxRelated"
                        type="number"
                        min="1"
                        max="12"
                        value={relatedSettings.maxProducts}
                        onChange={(e) =>
                          setRelatedSettings(prev => ({
                            ...prev,
                            maxProducts: parseInt(e.target.value) || 4
                          }))
                        }
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="relatedTitle" className="text-sm font-medium">Section title</Label>
                      <Input
                        id="relatedTitle"
                        value={relatedSettings.sectionTitle}
                        onChange={(e) =>
                          setRelatedSettings(prev => ({
                            ...prev,
                            sectionTitle: e.target.value
                          }))
                        }
                        className="mt-1"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Review & Publish Tab */}
          {activeTab === 'review' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Left Column - Product Summary */}
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Product Summary</h3>

                    <div className="bg-gray-50 dark:bg-gray-800 dark:bg-opacity-50 rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Name:</span>
                        <span className="text-sm font-medium">{form.name || 'Not set'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Category:</span>
                        <span className="text-sm font-medium">
                          {categories?.find(c => c.id === form.categoryId)?.name || 'Not selected'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Price:</span>
                        <span className="text-sm font-medium">
                          ${form.price.regular}
                          {form.price.sale && form.price.sale > 0 && (
                            <span className="text-green-600 ml-1">(Sale: ${form.price.sale})</span>
                          )}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Stock:</span>
                        <span className="text-sm font-medium">{form.stock.quantity} {form.unit}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Images:</span>
                        <span className="text-sm font-medium">{form.images.length} uploaded</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Status:</span>
                        <Badge variant={form.status === 'published' ? 'default' : 'secondary'}>
                          {form.status}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* Validation Status */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Validation Status</h3>

                    <div className="space-y-2">
                      {[
                        { label: 'Product Name', valid: !!form.name.trim(), required: true },
                        { label: 'Category', valid: !!form.categoryId, required: true },
                        { label: 'Price', valid: form.price.regular > 0, required: true },
                        { label: 'SKU', valid: !!form.sku.trim(), required: true },
                        { label: 'Images', valid: form.images.length > 0, required: false },
                        { label: 'Description', valid: !!form.description.trim(), required: false },
                        { label: 'SEO Title', valid: !!form.seo.title.trim(), required: false }
                      ].map((item, index) => (
                        <div key={index} className="flex items-center justify-between p-2 rounded">
                          <span className="text-sm text-gray-700 dark:text-gray-300">
                            {item.label}
                            {item.required && <span className="text-red-500 ml-1">*</span>}
                          </span>
                          <div className="flex items-center gap-2">
                            {item.valid ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <AlertTriangle className={`h-4 w-4 ${item.required ? 'text-red-500' : 'text-yellow-500'}`} />
                            )}
                            <span className={`text-xs ${
                              item.valid
                                ? 'text-green-600 dark:text-green-400'
                                : item.required
                                  ? 'text-red-600 dark:text-red-400'
                                  : 'text-yellow-600 dark:text-yellow-400'
                            }`}>
                              {item.valid ? 'Complete' : item.required ? 'Required' : 'Optional'}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Right Column - Actions */}
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Publish Actions</h3>

                    <div className="space-y-4">
                      <Button
                        onClick={() => handleSave('draft')}
                        disabled={saving}
                        variant="outline"
                        className="w-full justify-start"
                      >
                        <Save className="h-4 w-4 mr-2" />
                        Save as Draft
                        <span className="ml-auto text-xs text-gray-500">Ctrl+S</span>
                      </Button>

                      <Button
                        onClick={() => handleSave('published')}
                        disabled={saving || !canPublish()}
                        className="w-full justify-start bg-blue-600 hover:bg-blue-700"
                      >
                        <CheckCircle className="h-4 w-4 mr-2" />
                        {saving ? 'Publishing...' : 'Publish Now'}
                        {!canPublish() && (
                          <span className="ml-auto text-xs">Fix required fields</span>
                        )}
                      </Button>

                      <div className="bg-yellow-50 dark:bg-yellow-900 dark:bg-opacity-20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <Clock className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                          <h4 className="font-medium text-yellow-900 dark:text-yellow-100">Schedule Publishing</h4>
                        </div>
                        <p className="text-sm text-yellow-800 dark:text-yellow-200 mb-3">
                          Coming soon: Schedule your product to go live at a specific date and time.
                        </p>
                        <Button variant="outline" disabled className="w-full">
                          <Calendar className="h-4 w-4 mr-2" />
                          Schedule for Later
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Preview */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Preview</h3>

                    <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div className="aspect-square bg-gray-100 dark:bg-gray-700 rounded-lg mb-3 flex items-center justify-center">
                        {form.images.length > 0 ? (
                          <FallbackImage
                            src={form.images[0]}
                            alt={form.name}
                            className="w-full h-full object-cover rounded-lg"
                            fallbackClassName="w-full h-full rounded-lg"
                          />
                        ) : (
                          <ImageIcon className="h-8 w-8 text-gray-400" />
                        )}
                      </div>
                      <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                        {form.name || 'Product Name'}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {form.description || 'No description'}
                      </p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {form.price.sale && form.price.sale > 0 ? (
                            <>
                              <span className="font-bold text-green-600">${form.price.sale}</span>
                              <span className="text-sm text-gray-500 line-through">${form.price.regular}</span>
                            </>
                          ) : (
                            <span className="font-bold">${form.price.regular || 0}</span>
                          )}
                        </div>
                        <Badge variant={form.stock.status === 'in_stock' ? 'default' : 'secondary'}>
                          {form.stock.status === 'in_stock' ? 'In Stock' : 'Out of Stock'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Navigation Footer */}
      <div className="flex justify-between items-center bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
        <Button
          variant="outline"
          onClick={() => {
            const currentIndex = tabs.findIndex(tab => tab.id === activeTab)
            if (currentIndex > 0) {
              setActiveTab(tabs[currentIndex - 1].id)
            }
          }}
          disabled={tabs.findIndex(tab => tab.id === activeTab) === 0}
        >
          Previous
        </Button>

        <div className="flex items-center gap-2">
          {tabs.map((tab, index) => (
            <div
              key={tab.id}
              className={`w-2 h-2 rounded-full ${
                index <= tabs.findIndex(t => t.id === activeTab)
                  ? 'bg-blue-600'
                  : 'bg-gray-300 dark:bg-gray-600'
              }`}
            />
          ))}
        </div>

        <Button
          onClick={() => {
            const currentIndex = tabs.findIndex(tab => tab.id === activeTab)
            if (currentIndex < tabs.length - 1) {
              setActiveTab(tabs[currentIndex + 1].id)
            }
          }}
          disabled={tabs.findIndex(tab => tab.id === activeTab) === tabs.length - 1}
        >
          Next
        </Button>
      </div>
    </div>
  )
}
