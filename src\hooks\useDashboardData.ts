import { useEffect } from 'react'
import { useAnalyticsStore } from '../store/analytics-store'
import { useFirebaseAuthStore } from '../store/firebase-auth'

export interface DashboardStats {
  totalRevenue: number
  totalOrders: number
  totalProducts: number
  totalCustomers: number
  revenueChange: number
  ordersChange: number
  productsChange: number
  customersChange: number
}

export interface SalesData {
  date: string
  revenue: number
  orders: number
}

export interface RecentOrder {
  id: string
  order_number: string
  customer_name: string
  customer_email: string
  total_amount: number
  status: string
  created_at: string
}

export interface TopProduct {
  id: string
  name: string
  sales_count: number
  revenue: number
  images?: any[]
}

export function useDashboardData() {
  const { user } = useFirebaseAuthStore()
  const {
    metrics,
    salesData,
    topProducts,
    topCustomers,
    recentOrders,
    loading,
    error,
    fetchDashboardMetrics,
    fetchSalesData,
    fetchTopProducts,
    fetchTopCustomers,
    fetchRecentOrders,
    clearError
  } = useAnalyticsStore()

  // Fetch data when user is available
  useEffect(() => {
    if (user) {
      fetchDashboardMetrics('30days')
      fetchSalesData('30days')
      fetchTopProducts('30days')
      fetchTopCustomers()
      fetchRecentOrders(10)
    }
  }, [user])

  // Transform analytics data to match dashboard interface
  const stats: DashboardStats = metrics ? {
    totalRevenue: metrics.totalRevenue,
    totalOrders: metrics.totalOrders,
    totalProducts: metrics.totalProducts,
    totalCustomers: metrics.totalCustomers,
    revenueChange: metrics.revenueGrowth,
    ordersChange: metrics.ordersGrowth,
    productsChange: 0, // Would need historical product data
    customersChange: metrics.customersGrowth
  } : {
    totalRevenue: 0,
    totalOrders: 0,
    totalProducts: 0,
    totalCustomers: 0,
    revenueChange: 0,
    ordersChange: 0,
    productsChange: 0,
    customersChange: 0
  }

  // Transform sales data
  const transformedSalesData: SalesData[] = salesData.map(item => ({
    date: item.date,
    revenue: item.revenue,
    orders: item.orders
  }))

  // Transform recent orders
  const transformedRecentOrders: RecentOrder[] = recentOrders.map(order => ({
    id: order.id,
    order_number: order.orderNumber,
    customer_name: order.customerName,
    customer_email: '', // Would need to fetch from customer data
    total_amount: order.total,
    status: order.status,
    created_at: order.createdAt
  }))

  // Transform top products
  const transformedTopProducts: TopProduct[] = topProducts.map(product => ({
    id: product.id,
    name: product.name,
    sales_count: product.orders,
    revenue: product.revenue,
    images: [] // Would need to fetch product images
  }))

  return {
    stats,
    salesData: transformedSalesData,
    recentOrders: transformedRecentOrders,
    topProducts: transformedTopProducts,
    topCustomers,
    loading,
    error,
    clearError,
    refetch: () => {
      if (user) {
        fetchDashboardMetrics('30days')
        fetchSalesData('30days')
        fetchTopProducts('30days')
        fetchTopCustomers()
        fetchRecentOrders(10)
      }
    }
  }
}
