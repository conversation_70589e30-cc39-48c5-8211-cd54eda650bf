import { useState, useEffect, useCallback } from 'react'
import { collection, query, orderBy, onSnapshot, where } from 'firebase/firestore'
import { db } from '../lib/firebase'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { InventoryService } from '../lib/inventory-service'
import type { InventoryItem, InventoryStats, LowStockAlert, InventoryLog } from '../types/inventory'

interface UseInventoryOptions {
  realtime?: boolean
  autoRefresh?: boolean
  refreshInterval?: number
}

interface UseInventoryReturn {
  items: InventoryItem[]
  stats: InventoryStats | null
  alerts: LowStockAlert[]
  loading: boolean
  refreshing: boolean
  error: string | null
  refreshData: () => Promise<void>
  clearError: () => void
}

export function useInventory(options: UseInventoryOptions = {}): UseInventoryReturn {
  const {
    realtime = false,
    autoRefresh = false,
    refreshInterval = 30000 // 30 seconds
  } = options

  const { user } = useFirebaseAuthStore()
  const [items, setItems] = useState<InventoryItem[]>([])
  const [stats, setStats] = useState<InventoryStats | null>(null)
  const [alerts, setAlerts] = useState<LowStockAlert[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Initialize service
  const storeId = user?.storeIds?.[0] || 'womanza-jewelry-store'
  const inventoryService = new InventoryService(storeId)

  // Load inventory data
  const loadInventoryData = useCallback(async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true)
      } else {
        setLoading(true)
      }
      setError(null)

      const data = await inventoryService.getInventoryOverview()
      setItems(data.items)
      setStats(data.stats)
      setAlerts(data.alerts)
    } catch (err) {
      console.error('Error loading inventory data:', err)
      setError(err instanceof Error ? err.message : 'Failed to load inventory data')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }, [inventoryService])

  // Refresh data function
  const refreshData = useCallback(async () => {
    await loadInventoryData(true)
  }, [loadInventoryData])

  // Clear error function
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // Real-time listener for products (with debouncing to prevent infinite loops)
  useEffect(() => {
    if (!user?.storeIds?.[0] || !realtime) return

    const storeId = user.storeIds[0]
    const productsRef = collection(db, 'stores', storeId, 'products')

    let timeoutId: NodeJS.Timeout

    const unsubscribe = onSnapshot(
      productsRef,
      (snapshot) => {
        console.log('📊 Real-time inventory update received')

        // Debounce the updates to prevent infinite loops
        clearTimeout(timeoutId)
        timeoutId = setTimeout(() => {
          loadInventoryData(true)
        }, 1000) // Wait 1 second before updating
      },
      (error) => {
        console.error('❌ Real-time inventory listener error:', error)
        setError('Real-time updates failed')
      }
    )

    return () => {
      unsubscribe()
      clearTimeout(timeoutId)
    }
  }, [user?.storeIds, realtime]) // Remove loadInventoryData from dependencies to prevent loops

  // Load data on mount only
  useEffect(() => {
    if (user?.storeIds?.[0]) {
      loadInventoryData()
    }
  }, [user?.storeIds?.[0]]) // Only depend on storeId

  // Auto-refresh functionality (only when not using real-time)
  useEffect(() => {
    if (!autoRefresh || !user?.storeIds?.[0] || realtime) return

    const interval = setInterval(() => {
      loadInventoryData(true)
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, user?.storeIds, loadInventoryData, realtime])

  return {
    items,
    stats,
    alerts,
    loading,
    refreshing,
    error,
    refreshData,
    clearError
  }
}

// Hook for inventory logs
interface UseInventoryLogsOptions {
  productId: string
  variantId?: string
  limit?: number
}

interface UseInventoryLogsReturn {
  logs: InventoryLog[]
  loading: boolean
  error: string | null
  refreshLogs: () => Promise<void>
}

export function useInventoryLogs(options: UseInventoryLogsOptions): UseInventoryLogsReturn {
  const { productId, variantId, limit = 50 } = options
  const { user } = useFirebaseAuthStore()
  
  const [logs, setLogs] = useState<InventoryLog[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Initialize service
  const storeId = user?.storeIds?.[0] || 'womanza-jewelry-store'
  const inventoryService = new InventoryService(storeId)

  // Load logs
  const loadLogs = useCallback(async () => {
    if (!productId) return

    try {
      setLoading(true)
      setError(null)
      
      const logsData = await inventoryService.getInventoryLogs(productId, variantId, limit)
      setLogs(logsData)
    } catch (err) {
      console.error('Error loading inventory logs:', err)
      setError(err instanceof Error ? err.message : 'Failed to load inventory logs')
    } finally {
      setLoading(false)
    }
  }, [productId, variantId, limit, inventoryService])

  // Refresh logs function
  const refreshLogs = useCallback(async () => {
    await loadLogs()
  }, [loadLogs])

  // Load logs on mount and when dependencies change
  useEffect(() => {
    if (productId && user?.storeIds?.[0]) {
      loadLogs()
    }
  }, [productId, variantId, user?.storeIds, loadLogs])

  return {
    logs,
    loading,
    error,
    refreshLogs
  }
}

// Hook for inventory adjustments
interface UseInventoryAdjustmentReturn {
  adjusting: boolean
  error: string | null
  adjustInventory: (
    productId: string,
    action: 'increase' | 'decrease' | 'set',
    quantity: number,
    options?: {
      variantId?: string
      note?: string
      reason?: string
    }
  ) => Promise<void>
  updateLowStockThreshold: (
    productId: string,
    threshold: number,
    variantId?: string
  ) => Promise<void>
  toggleInventoryTracking: (
    productId: string,
    trackInventory: boolean,
    variantId?: string
  ) => Promise<void>
  clearError: () => void
}

export function useInventoryAdjustment(): UseInventoryAdjustmentReturn {
  const { user } = useFirebaseAuthStore()
  const [adjusting, setAdjusting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Initialize service
  const storeId = user?.storeIds?.[0] || 'womanza-jewelry-store'
  const inventoryService = new InventoryService(storeId)

  // Adjust inventory function
  const adjustInventory = useCallback(async (
    productId: string,
    action: 'increase' | 'decrease' | 'set',
    quantity: number,
    options: {
      variantId?: string
      note?: string
      reason?: string
    } = {}
  ) => {
    if (!user?.uid || !user?.displayName) {
      throw new Error('User information not available')
    }

    try {
      setAdjusting(true)
      setError(null)

      const adjustment = {
        productId,
        variantId: options.variantId,
        action,
        quantity,
        note: options.note,
        reason: options.reason || 'adjustment'
      }

      await inventoryService.adjustInventory(adjustment, user.uid, user.displayName)
    } catch (err) {
      console.error('Error adjusting inventory:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to adjust inventory'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setAdjusting(false)
    }
  }, [inventoryService, user])

  // Update low stock threshold
  const updateLowStockThreshold = useCallback(async (
    productId: string,
    threshold: number,
    variantId?: string
  ) => {
    try {
      setAdjusting(true)
      setError(null)
      
      await inventoryService.updateLowStockThreshold(productId, threshold, variantId)
    } catch (err) {
      console.error('Error updating threshold:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to update threshold'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setAdjusting(false)
    }
  }, [inventoryService])

  // Toggle inventory tracking
  const toggleInventoryTracking = useCallback(async (
    productId: string,
    trackInventory: boolean,
    variantId?: string
  ) => {
    try {
      setAdjusting(true)
      setError(null)
      
      await inventoryService.toggleInventoryTracking(productId, trackInventory, variantId)
    } catch (err) {
      console.error('Error toggling tracking:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to toggle tracking'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setAdjusting(false)
    }
  }, [inventoryService])

  // Clear error function
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  return {
    adjusting,
    error,
    adjustInventory,
    updateLowStockThreshold,
    toggleInventoryTracking,
    clearError
  }
}
