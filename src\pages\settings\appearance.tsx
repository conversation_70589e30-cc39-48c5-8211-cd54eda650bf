import { useState, useEffect } from 'react'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Textarea } from '../../components/ui/textarea'
import { Switch } from '../../components/ui/switch'
import { Button } from '../../components/ui/button'
import { toast } from 'react-hot-toast'
import { Save, Loader2, Upload, Trash2, ImageIcon } from 'lucide-react'
import { useRealtimeSettings } from '../../hooks/use-realtime-settings'
import { useStoreManagement } from '../../store/store-management'
import { useFirebaseAuthStore } from '../../store/firebase-auth'

export function AppearanceSettingsPage() {
  const { hasPermission } = useFirebaseAuthStore()
  const { currentStore } = useStoreManagement()
  const {
    settings,
    loading,
    updateBranding,
    updateSEO,
    initializeSettings
  } = useRealtimeSettings(currentStore?.id || 'default')

  const [brandingData, setBrandingData] = useState({})
  const [seoData, setSeoData] = useState({})
  const [isSaving, setIsSaving] = useState(false)

  const canEdit = hasPermission(['super_admin', 'admin'])

  // Initialize form data when settings load
  useEffect(() => {
    if (settings?.branding) {
      setBrandingData(settings.branding)
    }
    if (settings?.seo) {
      setSeoData(settings.seo)
    }
  }, [settings?.branding, settings?.seo])

  // Initialize settings if they don't exist
  useEffect(() => {
    if (!loading && (!settings?.branding || !settings?.seo) && currentStore?.id) {
      console.log('🔧 Initializing appearance settings for store:', currentStore.id)
      initializeSettings()
    }
  }, [loading, settings?.branding, settings?.seo, currentStore?.id, initializeSettings])

  // Handle field changes
  const handleBrandingChange = (field: string, value: any) => {
    setBrandingData(prev => ({ ...prev, [field]: value }))
  }

  const handleSeoChange = (field: string, value: any) => {
    setSeoData(prev => ({ ...prev, [field]: value }))
  }

  // Handle save
  const handleSave = async () => {
    if (!canEdit) return

    setIsSaving(true)
    try {
      await Promise.all([
        updateBranding(brandingData),
        updateSEO(seoData)
      ])
      
      toast.success('Appearance settings saved successfully!')
    } catch (error) {
      console.error('Error saving appearance settings:', error)
      toast.error('Failed to save settings. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Appearance Settings</h1>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            Customize your store's visual identity, theme, and SEO
          </p>
        </div>
        <Button
          onClick={handleSave}
          disabled={isSaving || !canEdit}
          className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
        >
          {isSaving ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          {isSaving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>

      {/* Branding Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Branding & Visual Identity</h3>
        
        {/* Logo Upload */}
        <div className="mb-6">
          <Label className="text-sm font-medium">Store Logo</Label>
          <div className="mt-2 flex items-center gap-4">
            <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-600">
              {brandingData.logoUrl ? (
                <img 
                  src={brandingData.logoUrl} 
                  alt="Store Logo" 
                  className="w-full h-full object-cover rounded-lg"
                />
              ) : (
                <ImageIcon className="h-6 w-6 text-gray-400" />
              )}
            </div>
            <div className="flex-1">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                Upload your store logo (recommended: 200x200px, PNG or JPG)
              </p>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // TODO: Implement logo upload
                    console.log('Logo upload feature coming soon')
                  }}
                  disabled={!canEdit}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Logo
                </Button>
                {brandingData.logoUrl && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleBrandingChange('logoUrl', '')}
                    disabled={!canEdit}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Remove
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Color Scheme */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <Label htmlFor="primaryColor">Primary Color</Label>
            <div className="mt-2 flex items-center gap-3">
              <Input
                id="primaryColor"
                type="color"
                value={brandingData.primaryColor || '#3B82F6'}
                onChange={(e) => handleBrandingChange('primaryColor', e.target.value)}
                disabled={!canEdit}
                className="w-16 h-10 p-1 border rounded"
              />
              <Input
                value={brandingData.primaryColor || '#3B82F6'}
                onChange={(e) => handleBrandingChange('primaryColor', e.target.value)}
                disabled={!canEdit}
                placeholder="#3B82F6"
                className="flex-1"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="secondaryColor">Secondary Color</Label>
            <div className="mt-2 flex items-center gap-3">
              <Input
                id="secondaryColor"
                type="color"
                value={brandingData.secondaryColor || '#6B7280'}
                onChange={(e) => handleBrandingChange('secondaryColor', e.target.value)}
                disabled={!canEdit}
                className="w-16 h-10 p-1 border rounded"
              />
              <Input
                value={brandingData.secondaryColor || '#6B7280'}
                onChange={(e) => handleBrandingChange('secondaryColor', e.target.value)}
                disabled={!canEdit}
                placeholder="#6B7280"
                className="flex-1"
              />
            </div>
          </div>
        </div>

        {/* Theme Settings */}
        <div>
          <Label className="text-sm font-medium">Theme Settings</Label>
          <div className="mt-3 space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="darkMode" className="text-sm font-medium">Dark Mode Support</Label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Enable dark mode for your storefront
                </p>
              </div>
              <Switch
                id="darkMode"
                checked={brandingData.darkModeEnabled || false}
                onCheckedChange={(checked) => handleBrandingChange('darkModeEnabled', checked)}
                disabled={!canEdit}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="customCSS" className="text-sm font-medium">Custom CSS</Label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Allow custom CSS modifications
                </p>
              </div>
              <Switch
                id="customCSS"
                checked={brandingData.customCSSEnabled || false}
                onCheckedChange={(checked) => handleBrandingChange('customCSSEnabled', checked)}
                disabled={!canEdit}
              />
            </div>
          </div>
        </div>

        {/* Custom CSS Editor */}
        {brandingData.customCSSEnabled && (
          <div className="mt-6">
            <Label htmlFor="customCSSCode">Custom CSS Code</Label>
            <Textarea
              id="customCSSCode"
              value={brandingData.customCSS || ''}
              onChange={(e) => handleBrandingChange('customCSS', e.target.value)}
              disabled={!canEdit}
              placeholder="/* Add your custom CSS here */"
              rows={8}
              className="font-mono text-sm"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Custom CSS will be applied to your storefront. Use with caution.
            </p>
          </div>
        )}
      </div>

      {/* SEO Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">SEO & Analytics</h3>
        
        <div className="space-y-6">
          <div>
            <Label htmlFor="metaTitle">Meta Title</Label>
            <Input
              id="metaTitle"
              value={seoData.metaTitle || ''}
              onChange={(e) => handleSeoChange('metaTitle', e.target.value)}
              disabled={!canEdit}
              placeholder="Your Store - Best Products Online"
            />
            <p className="text-xs text-gray-500 mt-1">Recommended: 50-60 characters</p>
          </div>

          <div>
            <Label htmlFor="metaDescription">Meta Description</Label>
            <Textarea
              id="metaDescription"
              value={seoData.metaDescription || ''}
              onChange={(e) => handleSeoChange('metaDescription', e.target.value)}
              disabled={!canEdit}
              placeholder="Discover amazing products at great prices. Fast shipping and excellent customer service."
              rows={3}
            />
            <p className="text-xs text-gray-500 mt-1">Recommended: 150-160 characters</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="googleAnalyticsId">Google Analytics ID</Label>
              <Input
                id="googleAnalyticsId"
                value={seoData.googleAnalyticsId || ''}
                onChange={(e) => handleSeoChange('googleAnalyticsId', e.target.value)}
                disabled={!canEdit}
                placeholder="G-XXXXXXXXXX"
              />
            </div>

            <div>
              <Label htmlFor="facebookPixelId">Facebook Pixel ID</Label>
              <Input
                id="facebookPixelId"
                value={seoData.facebookPixelId || ''}
                onChange={(e) => handleSeoChange('facebookPixelId', e.target.value)}
                disabled={!canEdit}
                placeholder="123456789012345"
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="sitemapEnabled" className="text-sm font-medium">Enable Sitemap</Label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Automatically generate XML sitemap for search engines
              </p>
            </div>
            <Switch
              id="sitemapEnabled"
              checked={seoData.sitemapEnabled || false}
              onCheckedChange={(checked) => handleSeoChange('sitemapEnabled', checked)}
              disabled={!canEdit}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
