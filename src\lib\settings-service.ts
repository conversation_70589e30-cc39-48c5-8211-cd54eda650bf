import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  onSnapshot,
  serverTimestamp,
  Unsubscribe,
  collection,
  getDocs,
  query,
  where
} from 'firebase/firestore'
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage'
import { db, storage, isFirebaseConfigured } from './firebase'
import { useStoreManagement } from '../store/store-management'

// ==================== NEW CATEGORIZED SETTINGS INTERFACES ====================

// 1. Branding & Appearance Settings
export interface BrandingSettings {
  logoUrl?: string
  primaryColor: string
  accentColor: string
  layout: 'boxed' | 'full-width'
  typography: string
  homepageBannerUrl?: string
  faviconUrl?: string
  theme: 'light' | 'dark' | 'auto'
  customCss?: string
  updatedAt?: string
  createdAt?: string
}

// 2. General Store Info
export interface GeneralSettings {
  storeName: string
  storeSlug: string
  currency: string
  locale: string
  timeZone: string
  description?: string
  status: 'active' | 'inactive' | 'maintenance'
  updatedAt?: string
  createdAt?: string
}

// 3. SEO & Analytics
export interface SEOSettings {
  metaTitle: string
  metaDescription: string
  googleAnalyticsId?: string
  facebookPixelId?: string
  sitemapUrl?: string
  googleTagManagerId?: string
  bingWebmasterId?: string
  metaKeywords?: string[]
  updatedAt?: string
  createdAt?: string
}

// 4. Auth & Access
export interface AuthSettings {
  allowPublicSignup: boolean
  sessionTimeoutMinutes: number
  enforceStrongPassword: boolean
  enable2FA: boolean
  passwordRequirements: {
    minLength: number
    requireUppercase: boolean
    requireNumbers: boolean
    requireSymbols: boolean
  }
  loginAttemptsLimit: number
  accountLockoutDuration: number
  updatedAt?: string
  createdAt?: string
}

// 5. Email & Notifications
export interface EmailSettings {
  senderEmail: string
  replyToEmail?: string
  orderConfirmationTemplate?: string
  enableAdminAlerts: boolean
  smtpSettings?: {
    host: string
    port: number
    secure: boolean
    username: string
    password: string
  }
  emailNotifications: {
    newOrders: boolean
    lowStock: boolean
    customerMessages: boolean
    paymentUpdates: boolean
  }
  updatedAt?: string
  createdAt?: string
}

// 6. Payment Gateway
export interface PaymentSettings {
  activeGateways: string[]
  stripePublicKey?: string
  stripeSecretKey?: string
  paypalClientId?: string
  paypalClientSecret?: string
  currency: string
  codEnabled: boolean
  updatedAt?: string
  createdAt?: string
}

// 7. Shipping Settings
export interface ShippingSettings {
  enableShipping: boolean
  zones: ShippingZone[]
  methods: ShippingMethod[]
  updatedAt?: string
  createdAt?: string
}

export interface ShippingZone {
  id: string
  name: string
  countries: string[]
  states?: string[]
  cities?: string[]
}

export interface ShippingMethod {
  id: string
  name: string
  type: 'flat_rate' | 'free_shipping' | 'local_pickup'
  cost: number
  minOrderValue?: number
  estimatedDays?: string
}

// 8. Tax Settings
export interface TaxSettings {
  taxInclusive: boolean
  defaultTaxRate: number
  regionalOverrides: TaxOverride[]
  updatedAt?: string
  createdAt?: string
}

export interface TaxOverride {
  region: string
  taxRate: number
  type: 'country' | 'state' | 'city'
}

// 9. Compliance
export interface ComplianceSettings {
  privacyPolicy: string
  termsOfService: string
  cookieConsentText: string
  gdprCompliance: boolean
  cookieConsentEnabled: boolean
  returnPolicy?: string
  updatedAt?: string
  createdAt?: string
}

// 10. Storefront Behavior
export interface BehaviorSettings {
  enableWishlist: boolean
  enableProductReviews: boolean
  relatedProductStrategy: 'tags' | 'manual' | 'random'
  showLowStockWarnings: boolean
  enableQuickView: boolean
  enableCompareProducts: boolean
  updatedAt?: string
  createdAt?: string
}

// 11. Admin Panel Preferences
export interface PanelPreferences {
  defaultTheme: 'light' | 'dark'
  defaultLanguage: string
  itemsPerPage: number
  visibleDashboardCards: string[]
  sidebarCollapsed: boolean
  updatedAt?: string
  createdAt?: string
}

// Combined settings interface for backward compatibility
export interface CombinedStoreSettings {
  branding: BrandingSettings
  general: GeneralSettings
  seo: SEOSettings
  auth: AuthSettings
  emailConfig: EmailSettings
  payment: PaymentSettings
  shipping: ShippingSettings
  tax: TaxSettings
  compliance: ComplianceSettings
  behavior: BehaviorSettings
  panelPreferences: PanelPreferences
}

// ==================== LEGACY INTERFACE (for backward compatibility) ====================
export interface StoreSettings {
  id: string
  name: string
  description: string
  currency: string
  timezone: string
  language: string
  status: 'active' | 'inactive' | 'maintenance'

  // Store Information
  store_address: string
  store_phone: string
  store_email: string
  store_website: string
  business_type: string
  country: string

  // Owner Information
  owner_name?: string
  owner_email?: string
  owner_phone?: string

  // Appearance & Branding
  primary_color: string
  secondary_color: string
  logo_url?: string
  favicon_url?: string
  theme?: 'light' | 'dark' | 'auto'
  custom_css?: string

  // Business Settings
  tax_rate: number
  shipping_enabled: boolean
  inventory_tracking: boolean
  low_stock_threshold: number
  multi_variant_products: boolean
  reviews_enabled: boolean
  wishlist_enabled: boolean
  gift_cards_enabled: boolean
  subscriptions_enabled: boolean

  // Notification Settings
  email_notifications: {
    new_orders: boolean
    low_stock: boolean
    customer_messages: boolean
    payment_updates: boolean
    order_confirmations: boolean
    shipping_updates: boolean
    marketing_emails: boolean
  }
  push_notifications: {
    enabled: boolean
    new_orders: boolean
    inventory_alerts: boolean
    customer_messages: boolean
    payment_updates: boolean
  }
  sms_notifications: {
    enabled: boolean
    order_confirmations: boolean
    shipping_updates: boolean
    payment_alerts: boolean
  }

  // Security Settings
  two_factor_enabled: boolean
  session_timeout: number
  password_requirements: {
    min_length: number
    require_uppercase: boolean
    require_numbers: boolean
    require_symbols: boolean
  }
  login_attempts_limit: number
  account_lockout_duration: number

  // SEO & Analytics
  meta_title: string
  meta_description: string
  meta_keywords: string[]
  google_analytics_id: string
  facebook_pixel_id: string
  google_tag_manager_id: string
  bing_webmaster_id: string

  // Social Media
  social_media: {
    facebook?: string
    instagram?: string
    twitter?: string
    linkedin?: string
    youtube?: string
    tiktok?: string
    pinterest?: string
  }

  // Payment Settings
  payment_methods: {
    stripe_enabled: boolean
    stripe_public_key?: string
    paypal_enabled: boolean
    paypal_client_id?: string
    cod_enabled: boolean
    bank_transfer_enabled: boolean
    crypto_enabled: boolean
  }

  // Shipping Settings
  shipping_settings: {
    free_shipping_threshold: number
    default_shipping_rate: number
    express_shipping_rate: number
    international_shipping_enabled: boolean
    shipping_zones: Array<{
      name: string
      countries: string[]
      rate: number
      free_threshold?: number
    }>
  }

  // Email Settings
  email_settings: {
    smtp_host?: string
    smtp_port?: number
    smtp_username?: string
    smtp_password?: string
    from_email?: string
    from_name?: string
  }

  // Advanced Settings
  maintenance_mode?: boolean
  debug_mode?: boolean
  api_rate_limit?: number
  cache_enabled?: boolean
  cdn_enabled?: boolean
  backup_frequency?: 'daily' | 'weekly' | 'monthly'
  data_retention_days?: number

  // Legal & Compliance
  privacy_policy_url?: string
  terms_of_service_url?: string
  return_policy_url?: string
  gdpr_compliance_enabled?: boolean
  cookie_consent_enabled?: boolean

  // Localization
  supported_languages: string[]
  default_language: string
  rtl_support: boolean
  date_format: string
  time_format: '12h' | '24h'
  number_format: string

  // Performance
  image_optimization: boolean
  lazy_loading: boolean
  minify_css: boolean
  minify_js: boolean

  // Timestamps
  created_at?: string
  updated_at?: string
}

class SettingsService {
  private unsubscribers: Map<string, Unsubscribe> = new Map()

  /**
   * Get store settings document reference
   * First try the subcollection approach, then fall back to main store document
   */
  private getSettingsDocRef(storeId: string) {
    // Try subcollection first: stores/{storeId}/settings/general
    return doc(db, 'stores', storeId, 'settings', 'general')
  }

  /**
   * Get main store document reference (fallback)
   */
  private getStoreDocRef(storeId: string) {
    return doc(db, 'stores', storeId)
  }

  /**
   * Get default settings for a new store
   */
  getDefaultSettings(storeId: string, storeName?: string): StoreSettings {
    return {
      id: storeId,
      name: storeName || 'My Store',
      description: 'Your online store powered by Womanza',
      currency: 'PKR',
      timezone: 'Asia/Karachi',
      language: 'en',
      status: 'active',

      // Store Information
      store_address: '',
      store_phone: '',
      store_email: '',
      store_website: '',
      business_type: 'retail',
      country: 'PK',

      // Owner Information
      owner_name: '',
      owner_email: '',
      owner_phone: '',

      // Appearance & Branding
      primary_color: '#3B82F6',
      secondary_color: '#8B5CF6',
      theme: 'light',
      custom_css: '',

      // Business Settings
      tax_rate: 0,
      shipping_enabled: true,
      inventory_tracking: true,
      low_stock_threshold: 10,
      multi_variant_products: true,
      reviews_enabled: true,
      wishlist_enabled: true,
      gift_cards_enabled: false,
      subscriptions_enabled: false,

      // Notification Settings
      email_notifications: {
        new_orders: true,
        low_stock: true,
        customer_messages: true,
        payment_updates: true,
        order_confirmations: true,
        shipping_updates: true,
        marketing_emails: false
      },
      push_notifications: {
        enabled: false,
        new_orders: true,
        inventory_alerts: true,
        customer_messages: true,
        payment_updates: true
      },
      sms_notifications: {
        enabled: false,
        order_confirmations: false,
        shipping_updates: false,
        payment_alerts: false
      },

      // Security Settings
      two_factor_enabled: false,
      session_timeout: 30,
      password_requirements: {
        min_length: 8,
        require_uppercase: true,
        require_numbers: true,
        require_symbols: false
      },
      login_attempts_limit: 5,
      account_lockout_duration: 30,

      // SEO & Analytics
      meta_title: `${storeName || 'My Store'} - Premium Products`,
      meta_description: 'Discover premium products at our online store',
      meta_keywords: ['ecommerce', 'online store', 'shopping'],
      google_analytics_id: '',
      facebook_pixel_id: '',
      google_tag_manager_id: '',
      bing_webmaster_id: '',

      // Social Media
      social_media: {
        facebook: '',
        instagram: '',
        twitter: '',
        linkedin: '',
        youtube: '',
        tiktok: '',
        pinterest: ''
      },

      // Payment Settings
      payment_methods: {
        stripe_enabled: false,
        stripe_public_key: '',
        paypal_enabled: false,
        paypal_client_id: '',
        cod_enabled: true,
        bank_transfer_enabled: false,
        crypto_enabled: false
      },

      // Shipping Settings
      shipping_settings: {
        free_shipping_threshold: 100,
        default_shipping_rate: 10,
        express_shipping_rate: 25,
        international_shipping_enabled: false,
        shipping_zones: [
          {
            name: 'Local',
            countries: ['PK'],
            rate: 10,
            free_threshold: 100
          }
        ]
      },

      // Email Settings
      email_settings: {
        smtp_host: '',
        smtp_port: 587,
        smtp_username: '',
        smtp_password: '',
        from_email: '',
        from_name: storeName || 'My Store'
      },

      // Advanced Settings
      maintenance_mode: false,
      debug_mode: false,
      api_rate_limit: 100,
      cache_enabled: true,
      cdn_enabled: false,
      backup_frequency: 'weekly',
      data_retention_days: 365,

      // Legal & Compliance
      privacy_policy_url: '',
      terms_of_service_url: '',
      return_policy_url: '',
      gdpr_compliance_enabled: false,
      cookie_consent_enabled: false,

      // Localization
      supported_languages: ['en', 'ur'],
      default_language: 'en',
      rtl_support: false,
      date_format: 'DD/MM/YYYY',
      time_format: '12h',
      number_format: '1,234.56',

      // Performance
      image_optimization: true,
      lazy_loading: true,
      minify_css: true,
      minify_js: true
    }
  }

  /**
   * Get store settings - try subcollection first, then main store document
   */
  async getSettings(storeId: string): Promise<StoreSettings> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      console.log('🔍 Fetching settings for store:', storeId)

      // First try the settings subcollection
      const settingsDoc = await getDoc(this.getSettingsDocRef(storeId))

      if (settingsDoc.exists()) {
        console.log('✅ Found settings in subcollection')
        const data = settingsDoc.data()
        return {
          ...data,
          id: storeId,
          created_at: data.created_at?.toDate?.()?.toISOString() || data.created_at,
          updated_at: data.updated_at?.toDate?.()?.toISOString() || data.updated_at
        } as StoreSettings
      }

      // Fallback: try to get settings from main store document
      console.log('⚠️ Settings subcollection not found, trying main store document')
      const storeDoc = await getDoc(this.getStoreDocRef(storeId))

      if (storeDoc.exists()) {
        console.log('✅ Found store document, extracting settings')
        const storeData = storeDoc.data()

        // Map store document fields to settings format
        const mappedSettings: StoreSettings = {
          id: storeId,
          name: storeData.name || 'Unnamed Store',
          description: storeData.description || '',
          currency: storeData.currency || 'PKR',
          timezone: storeData.timezone || 'Asia/Karachi',
          language: storeData.language || 'en',
          status: storeData.status || 'active',

          // Appearance
          primary_color: storeData.primaryColor || '#3B82F6',
          secondary_color: storeData.secondaryColor || '#8B5CF6',
          logo_url: storeData.logoUrl || '',
          theme: 'light',

          // Store Information
          store_address: storeData.businessAddress || '',
          store_phone: storeData.contactPhone || '',
          store_email: storeData.contactEmail || storeData.ownerEmail || '',
          store_website: storeData.website || '',

          // Business Settings
          tax_rate: storeData.taxRate || 0,
          shipping_enabled: true,
          inventory_tracking: true,
          low_stock_threshold: 10,

          // Default notification settings
          email_notifications: {
            new_orders: true,
            low_stock: true,
            customer_messages: true,
            payment_updates: true
          },
          push_notifications: {
            enabled: false,
            new_orders: true,
            inventory_alerts: true
          },

          // Default security settings
          two_factor_enabled: false,
          session_timeout: 30,
          password_requirements: {
            min_length: 8,
            require_uppercase: true,
            require_numbers: true,
            require_symbols: false
          },

          // SEO Settings
          meta_title: `${storeData.name || 'Store'} - Premium Products`,
          meta_description: storeData.description || 'Discover premium products at our store',
          google_analytics_id: '',
          facebook_pixel_id: '',

          // Payment Settings
          payment_methods: {
            stripe_enabled: false,
            paypal_enabled: false,
            cod_enabled: true
          },

          // Timestamps
          created_at: storeData.createdAt?.toDate?.()?.toISOString() || storeData.createdAt,
          updated_at: storeData.updatedAt?.toDate?.()?.toISOString() || storeData.updatedAt
        }

        // Initialize proper settings document for future use
        await this.initializeSettings(storeId, storeData.name)

        return mappedSettings
      }

      // If no store document exists, return default settings
      console.log('⚠️ No store document found, returning defaults')
      return this.getDefaultSettings(storeId)

    } catch (error) {
      console.error('Error fetching settings:', error)
      throw new Error('Failed to fetch store settings')
    }
  }

  /**
   * Validate settings data
   */
  private validateSettings(settings: Partial<StoreSettings>, isPartialUpdate = true): string[] {
    const errors: string[] = []

    // For partial updates (during typing), be more lenient
    if (isPartialUpdate) {
      // Only validate if the field is being explicitly set and has a meaningful value
      if (settings.name !== undefined && settings.name !== null && settings.name.trim().length > 0 && settings.name.trim().length < 2) {
        // Don't throw error during typing, just log warning
        console.warn('Store name should be at least 2 characters long')
        return [] // Return empty errors for partial updates
      }
    } else {
      // For full validation (e.g., form submission), be strict
      if (settings.name !== undefined && settings.name !== null && settings.name.trim().length < 2) {
        errors.push('Store name must be at least 2 characters long')
      }
    }

    // Only validate required fields if they're explicitly being set to empty/null
    if (settings.currency !== undefined && settings.currency === '') {
      if (!isPartialUpdate) errors.push('Currency is required')
    }

    if (settings.timezone !== undefined && settings.timezone === '') {
      if (!isPartialUpdate) errors.push('Timezone is required')
    }

    // Validate email format (only for non-partial updates or complete emails)
    if (settings.store_email !== undefined && settings.store_email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(settings.store_email)) {
        if (!isPartialUpdate) {
          errors.push('Store email must be a valid email address')
        } else {
          console.warn('Email format incomplete during typing')
        }
      }
    }

    // Validate phone format (basic validation) - only for non-partial updates
    if (settings.store_phone !== undefined && settings.store_phone) {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
      if (!phoneRegex.test(settings.store_phone.replace(/[\s\-\(\)]/g, ''))) {
        if (!isPartialUpdate) {
          errors.push('Store phone must be a valid phone number')
        } else {
          console.warn('Phone format incomplete during typing')
        }
      }
    }

    // Validate website URL - only for non-partial updates
    if (settings.store_website !== undefined && settings.store_website) {
      try {
        new URL(settings.store_website)
      } catch {
        if (!isPartialUpdate) {
          errors.push('Store website must be a valid URL')
        } else {
          console.warn('Website URL incomplete during typing')
        }
      }
    }

    // Validate tax rate - only for non-partial updates
    if (settings.tax_rate !== undefined && (settings.tax_rate < 0 || settings.tax_rate > 100)) {
      if (!isPartialUpdate) {
        errors.push('Tax rate must be between 0 and 100')
      }
    }

    // Validate low stock threshold - only for non-partial updates
    if (settings.low_stock_threshold !== undefined && settings.low_stock_threshold < 0) {
      if (!isPartialUpdate) {
        errors.push('Low stock threshold must be a positive number')
      }
    }

    // Validate session timeout - only for non-partial updates
    if (settings.session_timeout !== undefined && (settings.session_timeout < 5 || settings.session_timeout > 1440)) {
      if (!isPartialUpdate) {
        errors.push('Session timeout must be between 5 and 1440 minutes')
      }
    }

    // Validate password requirements - only for non-partial updates
    if (settings.password_requirements?.min_length !== undefined &&
        (settings.password_requirements.min_length < 6 || settings.password_requirements.min_length > 50)) {
      if (!isPartialUpdate) {
        errors.push('Password minimum length must be between 6 and 50 characters')
      }
    }

    // Validate colors (hex format) - only for non-partial updates or complete colors
    if (settings.primary_color !== undefined && settings.primary_color) {
      const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
      if (!colorRegex.test(settings.primary_color)) {
        if (!isPartialUpdate) {
          errors.push('Primary color must be a valid hex color')
        } else {
          console.warn('Color format incomplete during typing')
        }
      }
    }

    if (settings.secondary_color !== undefined && settings.secondary_color) {
      const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
      if (!colorRegex.test(settings.secondary_color)) {
        errors.push('Secondary color must be a valid hex color')
      }
    }

    return errors
  }

  /**
   * Validate settings with strict validation (for form submission)
   */
  public validateSettingsStrict(settings: Partial<StoreSettings>): string[] {
    return this.validateSettings(settings, false)
  }

  /**
   * Save settings with strict validation (for explicit save operations)
   */
  async saveSettings(storeId: string, settings: Partial<StoreSettings>): Promise<void> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    console.log('💾 Saving settings with strict validation for store:', storeId)

    // Use strict validation for explicit save operations
    const validationErrors = this.validateSettings(settings, false)
    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.join(', ')}`)
    }

    // Use the regular updateSettings method but with pre-validated data
    return this.updateSettings(storeId, settings)
  }

  /**
   * Update store settings with validation - updates both settings subcollection and main store document
   */
  async updateSettings(storeId: string, updates: Partial<StoreSettings>): Promise<void> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    console.log('💾 Updating settings for store:', storeId, updates)

    // Validate settings (use lenient validation for partial updates)
    const validationErrors = this.validateSettings(updates, true)
    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.join(', ')}`)
    }

    try {
      // Remove id and timestamps from updates to avoid conflicts
      const { id, created_at, updated_at, ...updateData } = updates

      // Sanitize data before saving
      const sanitizedData = this.sanitizeSettingsData(updateData)

      console.log('📝 Sanitized data to save:', sanitizedData)

      // Update settings subcollection (use setDoc with merge to create if not exists)
      const settingsRef = this.getSettingsDocRef(storeId)
      await setDoc(settingsRef, {
        ...sanitizedData,
        updated_at: serverTimestamp()
      }, { merge: true })

      console.log('✅ Settings subcollection updated')

      // Also update relevant fields in the main store document for consistency
      const storeRef = this.getStoreDocRef(storeId)
      const storeUpdates: any = {
        updatedAt: serverTimestamp()
      }

      // Map settings fields to store document fields
      if (sanitizedData.name) storeUpdates.name = sanitizedData.name
      if (sanitizedData.description) storeUpdates.description = sanitizedData.description
      if (sanitizedData.currency) storeUpdates.currency = sanitizedData.currency
      if (sanitizedData.timezone) storeUpdates.timezone = sanitizedData.timezone
      if (sanitizedData.language) storeUpdates.language = sanitizedData.language
      if (sanitizedData.status) storeUpdates.status = sanitizedData.status
      if (sanitizedData.primary_color) storeUpdates.primaryColor = sanitizedData.primary_color
      if (sanitizedData.secondary_color) storeUpdates.secondaryColor = sanitizedData.secondary_color
      if (sanitizedData.logo_url) storeUpdates.logoUrl = sanitizedData.logo_url
      if (sanitizedData.store_address) storeUpdates.businessAddress = sanitizedData.store_address
      if (sanitizedData.store_phone) storeUpdates.contactPhone = sanitizedData.store_phone
      if (sanitizedData.store_email) storeUpdates.contactEmail = sanitizedData.store_email
      if (sanitizedData.store_website) storeUpdates.website = sanitizedData.store_website
      if (sanitizedData.tax_rate !== undefined) storeUpdates.taxRate = sanitizedData.tax_rate

      await updateDoc(storeRef, storeUpdates)
      console.log('✅ Main store document updated with:', storeUpdates)

      console.log('✅ Settings updated successfully for store:', storeId)
    } catch (error) {
      console.error('❌ Error updating settings:', error)
      if (error instanceof Error && error.message.includes('Validation failed')) {
        throw error
      }
      throw new Error(`Failed to update store settings: ${error.message}`)
    }
  }

  /**
   * Sanitize settings data before saving
   */
  private sanitizeSettingsData(data: any): any {
    const sanitized = { ...data }

    // Trim string values
    Object.keys(sanitized).forEach(key => {
      if (typeof sanitized[key] === 'string') {
        sanitized[key] = sanitized[key].trim()
      }
    })

    // Ensure numeric values are properly typed
    if (sanitized.tax_rate !== undefined) {
      sanitized.tax_rate = Number(sanitized.tax_rate)
    }
    if (sanitized.low_stock_threshold !== undefined) {
      sanitized.low_stock_threshold = Number(sanitized.low_stock_threshold)
    }
    if (sanitized.session_timeout !== undefined) {
      sanitized.session_timeout = Number(sanitized.session_timeout)
    }

    // Ensure boolean values are properly typed
    if (sanitized.shipping_enabled !== undefined) {
      sanitized.shipping_enabled = Boolean(sanitized.shipping_enabled)
    }
    if (sanitized.inventory_tracking !== undefined) {
      sanitized.inventory_tracking = Boolean(sanitized.inventory_tracking)
    }
    if (sanitized.two_factor_enabled !== undefined) {
      sanitized.two_factor_enabled = Boolean(sanitized.two_factor_enabled)
    }

    return sanitized
  }

  /**
   * Initialize settings for a new store
   */
  async initializeSettings(storeId: string, storeName?: string): Promise<void> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      console.log('🔧 Initializing settings for store:', storeId)

      const settingsRef = this.getSettingsDocRef(storeId)
      const settingsDoc = await getDoc(settingsRef)

      if (!settingsDoc.exists()) {
        console.log('📝 Creating new settings document')

        const defaultSettings = this.getDefaultSettings(storeId, storeName)
        const { id, ...settingsData } = defaultSettings

        await setDoc(settingsRef, {
          ...settingsData,
          created_at: serverTimestamp(),
          updated_at: serverTimestamp()
        })

        console.log('✅ Settings initialized for store:', storeId)
      } else {
        console.log('ℹ️ Settings document already exists for store:', storeId)
      }
    } catch (error) {
      console.error('❌ Error initializing settings:', error)
      throw new Error('Failed to initialize store settings')
    }
  }

  /**
   * Subscribe to real-time settings updates
   */
  subscribeToSettings(
    storeId: string,
    callback: (settings: StoreSettings) => void,
    onError?: (error: Error) => void
  ): Unsubscribe {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    console.log('🔔 Setting up real-time subscription for store:', storeId)

    // First try settings subcollection
    const settingsRef = this.getSettingsDocRef(storeId)

    const unsubscribe = onSnapshot(
      settingsRef,
      async (doc) => {
        console.log('📡 Settings subscription triggered for:', storeId)

        if (doc.exists()) {
          console.log('✅ Settings document exists in subcollection')
          const data = doc.data()
          const settings: StoreSettings = {
            ...data,
            id: storeId,
            created_at: data.created_at?.toDate?.()?.toISOString() || data.created_at,
            updated_at: data.updated_at?.toDate?.()?.toISOString() || data.updated_at
          } as StoreSettings
          callback(settings)
        } else {
          console.log('⚠️ Settings document not found in subcollection, checking main store document')

          // Fallback: check main store document
          try {
            const storeDoc = await getDoc(this.getStoreDocRef(storeId))
            if (storeDoc.exists()) {
              console.log('✅ Found main store document, mapping to settings')
              const storeData = storeDoc.data()

              // Map store document to settings format
              const mappedSettings: StoreSettings = {
                id: storeId,
                name: storeData.name || 'Unnamed Store',
                description: storeData.description || '',
                currency: storeData.currency || 'PKR',
                timezone: storeData.timezone || 'Asia/Karachi',
                language: storeData.language || 'en',
                status: storeData.status || 'active',

                // Appearance
                primary_color: storeData.primaryColor || '#3B82F6',
                secondary_color: storeData.secondaryColor || '#8B5CF6',
                logo_url: storeData.logoUrl || '',
                theme: 'light',

                // Store Information
                store_address: storeData.businessAddress || '',
                store_phone: storeData.contactPhone || '',
                store_email: storeData.contactEmail || storeData.ownerEmail || '',
                store_website: storeData.website || '',

                // Business Settings
                tax_rate: storeData.taxRate || 0,
                shipping_enabled: true,
                inventory_tracking: true,
                low_stock_threshold: 10,

                // Default notification settings
                email_notifications: {
                  new_orders: true,
                  low_stock: true,
                  customer_messages: true,
                  payment_updates: true
                },
                push_notifications: {
                  enabled: false,
                  new_orders: true,
                  inventory_alerts: true
                },

                // Default security settings
                two_factor_enabled: false,
                session_timeout: 30,
                password_requirements: {
                  min_length: 8,
                  require_uppercase: true,
                  require_numbers: true,
                  require_symbols: false
                },

                // SEO Settings
                meta_title: `${storeData.name || 'Store'} - Premium Products`,
                meta_description: storeData.description || 'Discover premium products at our store',
                google_analytics_id: '',
                facebook_pixel_id: '',

                // Payment Settings
                payment_methods: {
                  stripe_enabled: false,
                  paypal_enabled: false,
                  cod_enabled: true
                },

                // Timestamps
                created_at: storeData.createdAt?.toDate?.()?.toISOString() || storeData.createdAt,
                updated_at: storeData.updatedAt?.toDate?.()?.toISOString() || storeData.updatedAt
              }

              callback(mappedSettings)

              // Initialize proper settings document for future use
              try {
                await this.initializeSettings(storeId, storeData.name)
                console.log('✅ Settings document initialized from store data')
              } catch (initError) {
                console.warn('⚠️ Could not initialize settings document:', initError)
              }
            } else {
              console.log('⚠️ No store document found, using defaults')
              callback(this.getDefaultSettings(storeId))
            }
          } catch (fallbackError) {
            console.error('❌ Error in fallback store document fetch:', fallbackError)
            callback(this.getDefaultSettings(storeId))
          }
        }
      },
      (error) => {
        console.error('❌ Error in settings subscription:', error)
        if (onError) {
          onError(new Error('Failed to subscribe to settings updates'))
        }
      }
    )

    // Store unsubscriber for cleanup
    this.unsubscribers.set(storeId, unsubscribe)

    return unsubscribe
  }

  /**
   * Unsubscribe from settings updates
   */
  unsubscribeFromSettings(storeId: string): void {
    const unsubscribe = this.unsubscribers.get(storeId)
    if (unsubscribe) {
      unsubscribe()
      this.unsubscribers.delete(storeId)
    }
  }

  /**
   * Create settings audit log entry
   */
  async logSettingsChange(
    storeId: string,
    changes: Partial<StoreSettings>,
    userId: string,
    userEmail: string
  ): Promise<void> {
    if (!isFirebaseConfigured) return

    try {
      const auditRef = doc(db, 'stores', storeId, 'settings_audit', Date.now().toString())

      await setDoc(auditRef, {
        changes,
        changed_by: userId,
        changed_by_email: userEmail,
        timestamp: serverTimestamp(),
        change_type: 'settings_update'
      })

      console.log('✅ Settings change logged for store:', storeId)
    } catch (error) {
      console.error('Error logging settings change:', error)
      // Don't throw error for audit logging failures
    }
  }

  /**
   * Get settings change history
   */
  async getSettingsHistory(storeId: string, limit: number = 50) {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      const { collection, query, orderBy, limitToLast, getDocs } = await import('firebase/firestore')

      const auditRef = collection(db, 'stores', storeId, 'settings_audit')
      const q = query(auditRef, orderBy('timestamp', 'desc'), limitToLast(limit))

      const snapshot = await getDocs(q)
      const history = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate?.()?.toISOString() || doc.data().timestamp
      }))

      return history
    } catch (error) {
      console.error('Error fetching settings history:', error)
      throw new Error('Failed to fetch settings history')
    }
  }

  /**
   * Backup current settings
   */
  async backupSettings(storeId: string): Promise<string> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      const settings = await this.getSettings(storeId)
      const backupRef = doc(db, 'stores', storeId, 'settings_backups', Date.now().toString())

      await setDoc(backupRef, {
        settings,
        created_at: serverTimestamp(),
        backup_type: 'manual'
      })

      console.log('✅ Settings backed up for store:', storeId)
      return backupRef.id
    } catch (error) {
      console.error('Error backing up settings:', error)
      throw new Error('Failed to backup settings')
    }
  }

  /**
   * Restore settings from backup
   */
  async restoreSettings(storeId: string, backupId: string): Promise<void> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      const backupRef = doc(db, 'stores', storeId, 'settings_backups', backupId)
      const backupDoc = await getDoc(backupRef)

      if (!backupDoc.exists()) {
        throw new Error('Backup not found')
      }

      const backupData = backupDoc.data()
      const { settings } = backupData

      if (!settings) {
        throw new Error('Invalid backup data')
      }

      // Remove timestamps from backup data
      const { created_at, updated_at, ...settingsToRestore } = settings

      await this.updateSettings(storeId, settingsToRestore)

      console.log('✅ Settings restored from backup for store:', storeId)
    } catch (error) {
      console.error('Error restoring settings:', error)
      throw new Error('Failed to restore settings from backup')
    }
  }

  // ==================== NEW CATEGORIZED SETTINGS METHODS ====================

  /**
   * Get settings document reference for a specific category
   */
  private getCategoryDocRef(storeId: string, category: string) {
    return doc(db, 'stores', storeId, 'settings', category)
  }

  /**
   * Get branding settings
   */
  async getBrandingSettings(storeId: string): Promise<BrandingSettings> {
    try {
      const docRef = this.getCategoryDocRef(storeId, 'branding')
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        return docSnap.data() as BrandingSettings
      }

      // Return default branding settings
      return this.getDefaultBrandingSettings()
    } catch (error) {
      console.error('Error getting branding settings:', error)
      return this.getDefaultBrandingSettings()
    }
  }

  /**
   * Update branding settings
   */
  async updateBrandingSettings(storeId: string, settings: Partial<BrandingSettings>): Promise<void> {
    try {
      const docRef = this.getCategoryDocRef(storeId, 'branding')
      await setDoc(docRef, {
        ...settings,
        updatedAt: serverTimestamp()
      }, { merge: true })
    } catch (error) {
      console.error('Error updating branding settings:', error)
      throw error
    }
  }

  /**
   * Subscribe to branding settings changes
   */
  subscribeToBrandingSettings(storeId: string, callback: (settings: BrandingSettings) => void): Unsubscribe {
    const docRef = this.getCategoryDocRef(storeId, 'branding')
    return onSnapshot(docRef, (doc) => {
      if (doc.exists()) {
        callback(doc.data() as BrandingSettings)
      } else {
        callback(this.getDefaultBrandingSettings())
      }
    })
  }

  /**
   * Get general settings
   */
  async getGeneralSettings(storeId: string): Promise<GeneralSettings> {
    try {
      const docRef = this.getCategoryDocRef(storeId, 'general')
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        return docSnap.data() as GeneralSettings
      }

      return this.getDefaultGeneralSettings(storeId)
    } catch (error) {
      console.error('Error getting general settings:', error)
      return this.getDefaultGeneralSettings(storeId)
    }
  }

  /**
   * Update general settings
   */
  async updateGeneralSettings(storeId: string, settings: Partial<GeneralSettings>): Promise<void> {
    try {
      const docRef = this.getCategoryDocRef(storeId, 'general')
      await setDoc(docRef, {
        ...settings,
        updatedAt: serverTimestamp()
      }, { merge: true })
    } catch (error) {
      console.error('Error updating general settings:', error)
      throw error
    }
  }

  /**
   * Subscribe to general settings changes
   */
  subscribeToGeneralSettings(storeId: string, callback: (settings: GeneralSettings) => void): Unsubscribe {
    const docRef = this.getCategoryDocRef(storeId, 'general')
    return onSnapshot(docRef, (doc) => {
      if (doc.exists()) {
        callback(doc.data() as GeneralSettings)
      } else {
        callback(this.getDefaultGeneralSettings(storeId))
      }
    })
  }

  /**
   * Get all categorized settings
   */
  async getAllCategorizedSettings(storeId: string): Promise<CombinedStoreSettings> {
    try {
      const [branding, general, seo, auth, emailConfig, payment, shipping, tax, compliance, behavior, panelPreferences] = await Promise.all([
        this.getBrandingSettings(storeId),
        this.getGeneralSettings(storeId),
        this.getSEOSettings(storeId),
        this.getAuthSettings(storeId),
        this.getEmailSettings(storeId),
        this.getPaymentSettings(storeId),
        this.getShippingSettings(storeId),
        this.getTaxSettings(storeId),
        this.getComplianceSettings(storeId),
        this.getBehaviorSettings(storeId),
        this.getPanelPreferences(storeId)
      ])

      return {
        branding,
        general,
        seo,
        auth,
        emailConfig,
        payment,
        shipping,
        tax,
        compliance,
        behavior,
        panelPreferences
      }
    } catch (error) {
      console.error('Error getting all categorized settings:', error)
      throw error
    }
  }

  /**
   * Subscribe to all categorized settings changes
   */
  subscribeToAllCategorizedSettings(storeId: string, callback: (settings: CombinedStoreSettings) => void): Unsubscribe {
    const unsubscribers: Unsubscribe[] = []
    let settingsCache: Partial<CombinedStoreSettings> = {}

    const updateCallback = (category: keyof CombinedStoreSettings, data: any) => {
      settingsCache[category] = data

      // Check if all categories are loaded
      const requiredCategories: (keyof CombinedStoreSettings)[] = [
        'branding', 'general', 'seo', 'auth', 'emailConfig',
        'payment', 'shipping', 'tax', 'compliance', 'behavior', 'panelPreferences'
      ]

      const allLoaded = requiredCategories.every(cat => settingsCache[cat] !== undefined)

      if (allLoaded) {
        callback(settingsCache as CombinedStoreSettings)
      }
    }

    // Subscribe to each category
    unsubscribers.push(this.subscribeToBrandingSettings(storeId, (data) => updateCallback('branding', data)))
    unsubscribers.push(this.subscribeToGeneralSettings(storeId, (data) => updateCallback('general', data)))
    // Add more subscriptions for other categories...

    // Return cleanup function
    return () => {
      unsubscribers.forEach(unsub => unsub())
    }
  }

  // ==================== REMAINING CATEGORY METHODS ====================

  /**
   * Get SEO settings
   */
  async getSEOSettings(storeId: string): Promise<SEOSettings> {
    try {
      const docRef = this.getCategoryDocRef(storeId, 'seo')
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        return docSnap.data() as SEOSettings
      }

      return this.getDefaultSEOSettings()
    } catch (error) {
      console.error('Error getting SEO settings:', error)
      return this.getDefaultSEOSettings()
    }
  }

  /**
   * Get auth settings
   */
  async getAuthSettings(storeId: string): Promise<AuthSettings> {
    try {
      const docRef = this.getCategoryDocRef(storeId, 'auth')
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        return docSnap.data() as AuthSettings
      }

      return this.getDefaultAuthSettings()
    } catch (error) {
      console.error('Error getting auth settings:', error)
      return this.getDefaultAuthSettings()
    }
  }

  /**
   * Get email settings
   */
  async getEmailSettings(storeId: string): Promise<EmailSettings> {
    try {
      const docRef = this.getCategoryDocRef(storeId, 'email-config')
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        return docSnap.data() as EmailSettings
      }

      return this.getDefaultEmailSettings()
    } catch (error) {
      console.error('Error getting email settings:', error)
      return this.getDefaultEmailSettings()
    }
  }

  /**
   * Get payment settings
   */
  async getPaymentSettings(storeId: string): Promise<PaymentSettings> {
    try {
      const docRef = this.getCategoryDocRef(storeId, 'payment')
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        return docSnap.data() as PaymentSettings
      }

      return this.getDefaultPaymentSettings()
    } catch (error) {
      console.error('Error getting payment settings:', error)
      return this.getDefaultPaymentSettings()
    }
  }

  /**
   * Get shipping settings
   */
  async getShippingSettings(storeId: string): Promise<ShippingSettings> {
    try {
      const docRef = this.getCategoryDocRef(storeId, 'shipping')
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        return docSnap.data() as ShippingSettings
      }

      return this.getDefaultShippingSettings()
    } catch (error) {
      console.error('Error getting shipping settings:', error)
      return this.getDefaultShippingSettings()
    }
  }

  /**
   * Get tax settings
   */
  async getTaxSettings(storeId: string): Promise<TaxSettings> {
    try {
      const docRef = this.getCategoryDocRef(storeId, 'tax')
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        return docSnap.data() as TaxSettings
      }

      return this.getDefaultTaxSettings()
    } catch (error) {
      console.error('Error getting tax settings:', error)
      return this.getDefaultTaxSettings()
    }
  }

  /**
   * Get compliance settings
   */
  async getComplianceSettings(storeId: string): Promise<ComplianceSettings> {
    try {
      const docRef = this.getCategoryDocRef(storeId, 'compliance')
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        return docSnap.data() as ComplianceSettings
      }

      return this.getDefaultComplianceSettings()
    } catch (error) {
      console.error('Error getting compliance settings:', error)
      return this.getDefaultComplianceSettings()
    }
  }

  /**
   * Get behavior settings
   */
  async getBehaviorSettings(storeId: string): Promise<BehaviorSettings> {
    try {
      const docRef = this.getCategoryDocRef(storeId, 'behavior')
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        return docSnap.data() as BehaviorSettings
      }

      return this.getDefaultBehaviorSettings()
    } catch (error) {
      console.error('Error getting behavior settings:', error)
      return this.getDefaultBehaviorSettings()
    }
  }

  /**
   * Get panel preferences
   */
  async getPanelPreferences(storeId: string): Promise<PanelPreferences> {
    try {
      const docRef = this.getCategoryDocRef(storeId, 'panel-preferences')
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        return docSnap.data() as PanelPreferences
      }

      return this.getDefaultPanelPreferences()
    } catch (error) {
      console.error('Error getting panel preferences:', error)
      return this.getDefaultPanelPreferences()
    }
  }

  /**
   * Cleanup all subscriptions
   */
  cleanup(): void {
    this.unsubscribers.forEach((unsubscribe) => unsubscribe())
    this.unsubscribers.clear()
  }
  // ==================== DEFAULT SETTINGS METHODS ====================

  /**
   * Get default branding settings
   */
  private getDefaultBrandingSettings(): BrandingSettings {
    return {
      primaryColor: '#3B82F6',
      accentColor: '#8B5CF6',
      layout: 'full-width',
      typography: 'Inter',
      theme: 'light',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  /**
   * Get default general settings
   */
  private getDefaultGeneralSettings(storeId: string): GeneralSettings {
    return {
      storeName: 'My Store',
      storeSlug: storeId,
      currency: 'PKR',
      locale: 'en-PK',
      timeZone: 'Asia/Karachi',
      description: 'Your online store powered by Womanza',
      status: 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  /**
   * Get default SEO settings
   */
  private getDefaultSEOSettings(): SEOSettings {
    return {
      metaTitle: 'My Store - Premium Products',
      metaDescription: 'Discover premium products at our online store',
      metaKeywords: ['ecommerce', 'online store', 'shopping'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  /**
   * Get default auth settings
   */
  private getDefaultAuthSettings(): AuthSettings {
    return {
      allowPublicSignup: true,
      sessionTimeoutMinutes: 30,
      enforceStrongPassword: true,
      enable2FA: false,
      passwordRequirements: {
        minLength: 8,
        requireUppercase: true,
        requireNumbers: true,
        requireSymbols: false
      },
      loginAttemptsLimit: 5,
      accountLockoutDuration: 30,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  /**
   * Get default email settings
   */
  private getDefaultEmailSettings(): EmailSettings {
    return {
      senderEmail: '<EMAIL>',
      enableAdminAlerts: true,
      emailNotifications: {
        newOrders: true,
        lowStock: true,
        customerMessages: true,
        paymentUpdates: true
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  /**
   * Get default payment settings
   */
  private getDefaultPaymentSettings(): PaymentSettings {
    return {
      activeGateways: ['cod'],
      currency: 'PKR',
      codEnabled: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  /**
   * Get default shipping settings
   */
  private getDefaultShippingSettings(): ShippingSettings {
    return {
      enableShipping: true,
      zones: [],
      methods: [
        {
          id: 'flat_rate',
          name: 'Flat Rate',
          type: 'flat_rate',
          cost: 200,
          estimatedDays: '3-5 business days'
        }
      ],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  /**
   * Get default tax settings
   */
  private getDefaultTaxSettings(): TaxSettings {
    return {
      taxInclusive: false,
      defaultTaxRate: 0,
      regionalOverrides: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  /**
   * Get default compliance settings
   */
  private getDefaultComplianceSettings(): ComplianceSettings {
    return {
      privacyPolicy: '',
      termsOfService: '',
      cookieConsentText: 'We use cookies to improve your experience on our website.',
      gdprCompliance: false,
      cookieConsentEnabled: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  /**
   * Get default behavior settings
   */
  private getDefaultBehaviorSettings(): BehaviorSettings {
    return {
      enableWishlist: true,
      enableProductReviews: true,
      relatedProductStrategy: 'tags',
      showLowStockWarnings: true,
      enableQuickView: true,
      enableCompareProducts: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  /**
   * Get default panel preferences
   */
  private getDefaultPanelPreferences(): PanelPreferences {
    return {
      defaultTheme: 'light',
      defaultLanguage: 'en',
      itemsPerPage: 25,
      visibleDashboardCards: ['orders', 'revenue', 'customers', 'products'],
      sidebarCollapsed: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  /**
   * Upload file to Firebase Storage
   */
  async uploadFile(storeId: string, file: File, path: string): Promise<string> {
    try {
      const storageRef = ref(storage, `stores/${storeId}/${path}`)
      const snapshot = await uploadBytes(storageRef, file)
      const downloadURL = await getDownloadURL(snapshot.ref)
      return downloadURL
    } catch (error) {
      console.error('Error uploading file:', error)
      throw error
    }
  }

  /**
   * Delete file from Firebase Storage
   */
  async deleteFile(storeId: string, path: string): Promise<void> {
    try {
      const storageRef = ref(storage, `stores/${storeId}/${path}`)
      await deleteObject(storageRef)
    } catch (error) {
      console.error('Error deleting file:', error)
      throw error
    }
  }
}

export const settingsService = new SettingsService()
