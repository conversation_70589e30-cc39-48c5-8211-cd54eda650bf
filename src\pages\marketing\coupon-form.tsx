import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from '../../components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Select } from '../../components/ui/select'
import { Switch } from '../../components/ui/switch'
import { MarketingService, type Coupon } from '../../lib/marketing-service'
import { toast } from 'react-hot-toast'
import { ArrowLeft, Save, Tag } from 'lucide-react'

export default function CouponFormPage() {
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)
  const marketingService = new MarketingService('womanza-jewelry-store')

  const [formData, setFormData] = useState({
    code: '',
    discountType: 'percentage' as 'percentage' | 'flat' | 'free_shipping',
    discountValue: 0,
    usageLimit: 100,
    minCartValue: 0,
    customerGroup: '',
    applicableProducts: [] as string[],
    applicableCategories: [] as string[],
    active: true,
    expiresAt: '',
    assignedTo: null as string | null
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.code.trim()) {
      toast.error('Coupon code is required')
      return
    }

    if (formData.discountType !== 'free_shipping' && formData.discountValue <= 0) {
      toast.error('Discount value must be greater than 0')
      return
    }

    if (!formData.expiresAt) {
      toast.error('Expiration date is required')
      return
    }

    try {
      setLoading(true)
      
      const couponData = {
        ...formData,
        code: formData.code.toUpperCase(),
        createdBy: 'admin' // TODO: Get from auth context
      }

      await marketingService.createCoupon(couponData)
      
      toast.success('Coupon created successfully!', {
        style: {
          background: '#10B981',
          color: 'white',
        }
      })
      
      navigate('/admin/marketing')
    } catch (error) {
      console.error('Error creating coupon:', error)
      toast.error('Failed to create coupon', {
        style: {
          background: '#EF4444',
          color: 'white',
        }
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => navigate('/admin/marketing')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Marketing
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Create Coupon</h1>
            <p className="text-gray-600">Create a new discount coupon for your customers</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Tag className="h-5 w-5 mr-2" />
                  Coupon Details
                </CardTitle>
                <CardDescription>
                  Configure the basic details of your coupon
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="code">Coupon Code *</Label>
                    <Input
                      id="code"
                      value={formData.code}
                      onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                      placeholder="e.g., SAVE20"
                      className="font-mono"
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Use uppercase letters and numbers only
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="discountType">Discount Type *</Label>
                    <Select
                      value={formData.discountType}
                      onValueChange={(value: 'percentage' | 'flat' | 'free_shipping') =>
                        setFormData({ ...formData, discountType: value, discountValue: value === 'free_shipping' ? 0 : formData.discountValue })
                      }
                      options={[
                        { value: 'percentage', label: 'Percentage Off' },
                        { value: 'flat', label: 'Fixed Amount Off' },
                        { value: 'free_shipping', label: 'Free Shipping' }
                      ]}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {formData.discountType !== 'free_shipping' && (
                    <div>
                      <Label htmlFor="discountValue">
                        Discount Value * {formData.discountType === 'percentage' ? '(%)' : '(₹)'}
                      </Label>
                      <Input
                        id="discountValue"
                        type="number"
                        value={formData.discountValue}
                        onChange={(e) => setFormData({ ...formData, discountValue: Number(e.target.value) })}
                        placeholder={formData.discountType === 'percentage' ? '20' : '1000'}
                        min="0"
                        max={formData.discountType === 'percentage' ? '100' : undefined}
                        required
                      />
                    </div>
                  )}

                  <div>
                    <Label htmlFor="usageLimit">Usage Limit</Label>
                    <Input
                      id="usageLimit"
                      type="number"
                      value={formData.usageLimit}
                      onChange={(e) => setFormData({ ...formData, usageLimit: Number(e.target.value) })}
                      placeholder="100"
                      min="1"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Maximum number of times this coupon can be used
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="minCartValue">Minimum Cart Value (₹)</Label>
                    <Input
                      id="minCartValue"
                      type="number"
                      value={formData.minCartValue}
                      onChange={(e) => setFormData({ ...formData, minCartValue: Number(e.target.value) })}
                      placeholder="0"
                      min="0"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Minimum order amount required to use this coupon
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="expiresAt">Expiration Date *</Label>
                    <Input
                      id="expiresAt"
                      type="datetime-local"
                      value={formData.expiresAt}
                      onChange={(e) => setFormData({ ...formData, expiresAt: e.target.value })}
                      required
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="active"
                    checked={formData.active}
                    onCheckedChange={(checked) => setFormData({ ...formData, active: checked })}
                  />
                  <Label htmlFor="active">Active</Label>
                  <p className="text-sm text-gray-500">
                    Inactive coupons cannot be used by customers
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gradient-to-r from-accent-500 to-accent-600 text-white p-4 rounded-lg">
                  <div className="text-center">
                    <div className="text-2xl font-bold font-mono mb-2">
                      {formData.code || 'COUPON CODE'}
                    </div>
                    <div className="text-lg mb-2">
                      {formData.discountType === 'percentage' && `${formData.discountValue}% OFF`}
                      {formData.discountType === 'flat' && `₹${formData.discountValue} OFF`}
                      {formData.discountType === 'free_shipping' && 'FREE SHIPPING'}
                    </div>
                    {formData.minCartValue > 0 && (
                      <div className="text-sm opacity-90">
                        On orders above ₹{formData.minCartValue}
                      </div>
                    )}
                    <div className="text-xs opacity-75 mt-2">
                      Valid until {formData.expiresAt ? new Date(formData.expiresAt).toLocaleDateString() : 'expiry date'}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex gap-3">
                  <Button type="submit" disabled={loading} className="flex-1">
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Creating...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Create Coupon
                      </>
                    )}
                  </Button>
                  <Button type="button" variant="outline" onClick={() => navigate('/admin/marketing')}>
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  )
}
