import React, { useState, useEffect } from 'react'
import { Input } from './input'
import { Label } from './label'
import { Badge } from './badge'
import { 
  formatPhoneNumber, 
  validatePhoneNumber, 
  getPhoneInputPlaceholder,
  getCountryInfo,
  getCountryByCurrency 
} from '../../lib/country-utils'
import { Phone, Check, AlertCircle } from 'lucide-react'

interface PhoneInputProps {
  value: string
  onChange: (value: string) => void
  countryCode?: string
  currency?: string
  label?: string
  placeholder?: string
  disabled?: boolean
  required?: boolean
  className?: string
}

export function PhoneInput({
  value,
  onChange,
  countryCode,
  currency,
  label = 'Phone Number',
  placeholder,
  disabled = false,
  required = false,
  className = ''
}: PhoneInputProps) {
  const [inputValue, setInputValue] = useState(value || '')
  const [isValid, setIsValid] = useState(false)
  const [country, setCountry] = useState<any>(null)

  // Determine country from countryCode or currency
  useEffect(() => {
    let detectedCountry = null
    
    if (countryCode) {
      detectedCountry = getCountryInfo(countryCode)
    } else if (currency) {
      detectedCountry = getCountryByCurrency(currency)
    }
    
    setCountry(detectedCountry)
  }, [countryCode, currency])

  // Validate phone number when input or country changes
  useEffect(() => {
    if (inputValue && country) {
      const valid = validatePhoneNumber(inputValue, country.code)
      setIsValid(valid)
    } else {
      setIsValid(false)
    }
  }, [inputValue, country])

  // Update input value when prop value changes
  useEffect(() => {
    setInputValue(value || '')
  }, [value])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value
    setInputValue(rawValue)
    
    // Format the phone number if country is detected
    if (country && rawValue) {
      const formatted = formatPhoneNumber(rawValue, country.code)
      onChange(formatted)
    } else {
      onChange(rawValue)
    }
  }

  const handleBlur = () => {
    // Format on blur for better UX
    if (country && inputValue) {
      const formatted = formatPhoneNumber(inputValue, country.code)
      setInputValue(formatted)
      onChange(formatted)
    }
  }

  const getPlaceholder = () => {
    if (placeholder) return placeholder
    if (country) return getPhoneInputPlaceholder(country.code)
    return 'Enter phone number'
  }

  const getValidationMessage = () => {
    if (!inputValue) return null
    if (!country) return 'Country not detected'
    if (isValid) return `Valid ${country.name} phone number`
    return `Invalid ${country.name} phone number format`
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <Label htmlFor="phone-input" className="flex items-center gap-2">
          <Phone className="h-4 w-4" />
          {label}
          {required && <span className="text-red-500">*</span>}
        </Label>
      )}
      
      <div className="relative">
        <Input
          id="phone-input"
          type="tel"
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleBlur}
          placeholder={getPlaceholder()}
          disabled={disabled}
          className={`pr-10 ${
            inputValue && country
              ? isValid
                ? 'border-green-500 focus:border-green-500'
                : 'border-red-500 focus:border-red-500'
              : ''
          }`}
        />
        
        {/* Validation Icon */}
        {inputValue && country && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {isValid ? (
              <Check className="h-4 w-4 text-green-500" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-500" />
            )}
          </div>
        )}
      </div>

      {/* Country and Format Info */}
      <div className="flex items-center justify-between text-xs">
        <div className="flex items-center gap-2">
          {country && (
            <>
              <Badge variant="outline" className="text-xs">
                {country.name} ({country.phoneCode})
              </Badge>
              <span className="text-gray-500">
                Format: {country.phoneFormat}
              </span>
            </>
          )}
        </div>
        
        {/* Validation Message */}
        {inputValue && (
          <div className={`flex items-center gap-1 ${
            isValid ? 'text-green-600' : 'text-red-600'
          }`}>
            {isValid ? (
              <Check className="h-3 w-3" />
            ) : (
              <AlertCircle className="h-3 w-3" />
            )}
            <span>{getValidationMessage()}</span>
          </div>
        )}
      </div>

      {/* Example */}
      {!inputValue && country && (
        <div className="text-xs text-gray-500">
          Example: {country.phoneExample}
        </div>
      )}
    </div>
  )
}

// Currency-aware phone input that automatically detects country from currency
interface CurrencyPhoneInputProps extends Omit<PhoneInputProps, 'countryCode'> {
  currency: string
}

export function CurrencyPhoneInput({ currency, ...props }: CurrencyPhoneInputProps) {
  return <PhoneInput {...props} currency={currency} />
}

// Country selector with phone input
interface CountryPhoneInputProps extends PhoneInputProps {
  onCountryChange?: (countryCode: string) => void
  availableCountries?: string[]
}

export function CountryPhoneInput({
  onCountryChange,
  availableCountries = ['PK', 'US', 'GB', 'IN', 'CA', 'AU', 'AE', 'SA'],
  countryCode,
  ...props
}: CountryPhoneInputProps) {
  const [selectedCountry, setSelectedCountry] = useState(countryCode || 'PK')

  const handleCountryChange = (newCountryCode: string) => {
    setSelectedCountry(newCountryCode)
    if (onCountryChange) {
      onCountryChange(newCountryCode)
    }
  }

  return (
    <div className="space-y-3">
      {/* Country Selector */}
      <div>
        <Label htmlFor="country-select">Country</Label>
        <select
          id="country-select"
          value={selectedCountry}
          onChange={(e) => handleCountryChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 mt-1"
        >
          {availableCountries.map((code) => {
            const country = getCountryInfo(code)
            return country ? (
              <option key={code} value={code}>
                {country.name} ({country.phoneCode})
              </option>
            ) : null
          })}
        </select>
      </div>

      {/* Phone Input */}
      <PhoneInput {...props} countryCode={selectedCountry} />
    </div>
  )
}
