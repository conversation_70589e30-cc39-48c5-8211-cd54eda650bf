import { useState, useEffect, useCallback, useRef } from 'react'
import { CustomersService, type CustomerStats } from '../lib/customers-service'
import { useStoreManagement } from '../store/store-management'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { toast } from 'react-hot-toast'
import type { Customer } from '../lib/firebase'

interface UseCustomersOptions {
  limit?: number
  orderBy?: string
  orderDirection?: 'asc' | 'desc'
  filters?: {
    status?: string
    group?: string
    search?: string
  }
  realtime?: boolean
}

interface UseCustomersReturn {
  customers: Customer[]
  stats: CustomerStats | null
  loading: boolean
  error: string | null
  refreshing: boolean
  
  // Actions
  refreshCustomers: () => Promise<void>
  createCustomer: (customerData: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>) => Promise<string>
  updateCustomer: (customerId: string, updates: Partial<Customer>) => Promise<void>
  deleteCustomer: (customerId: string) => Promise<void>
  getCustomer: (customerId: string) => Promise<Customer | null>
  
  // Pagination
  loadMore: () => Promise<void>
  hasMore: boolean
  
  // Filters
  setFilters: (filters: UseCustomersOptions['filters']) => void
  setSearch: (search: string) => void
}

export function useCustomers(options: UseCustomersOptions = {}): UseCustomersReturn {
  const { currentStore } = useStoreManagement()
  const { user } = useFirebaseAuthStore()

  const [customers, setCustomers] = useState<Customer[]>([])
  const [stats, setStats] = useState<CustomerStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [filters, setFiltersState] = useState(options.filters || {})

  const unsubscribeRef = useRef<(() => void) | null>(null)
  const serviceRef = useRef<CustomersService | null>(null)
  const lastDocRef = useRef<any>(null)
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Memoize options to prevent unnecessary re-renders
  const optionsRef = useRef(options)
  optionsRef.current = options

  // Get store ID
  const storeId = currentStore?.id || user?.primaryStoreId || user?.storeId || 'womanza-jewelry-store'

  // Initialize service
  useEffect(() => {
    if (storeId) {
      serviceRef.current = new CustomersService(storeId)
    }
  }, [storeId])

  // Load customers with debouncing
  const loadCustomers = useCallback(async (append: boolean = false) => {
    if (!serviceRef.current) return

    // Clear any existing timeout
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current)
    }

    // Debounce the loading to prevent excessive calls
    return new Promise<void>((resolve) => {
      loadingTimeoutRef.current = setTimeout(async () => {
        try {
          if (!append) {
            setLoading(true)
            setError(null)
          }

      const currentOptions = optionsRef.current
      const loadOptions = {
        ...currentOptions,
        filters: { ...currentOptions.filters, ...filters },
        startAfter: append ? lastDocRef.current : undefined
      }

      const newCustomers = await serviceRef.current.getCustomers(loadOptions)
      
      if (append) {
        setCustomers(prev => [...prev, ...newCustomers])
      } else {
        setCustomers(newCustomers)
      }

      // Update pagination
      setHasMore(newCustomers.length === (currentOptions.limit || 50))
      if (newCustomers.length > 0) {
        lastDocRef.current = newCustomers[newCustomers.length - 1]
      }

          console.log('✅ Customers loaded:', newCustomers.length)
          resolve()
        } catch (error) {
          console.error('❌ Error loading customers:', error)
          setError(error instanceof Error ? error.message : 'Failed to load customers')
          toast.error('Failed to load customers')
          resolve()
        } finally {
          setLoading(false)
        }
      }, 100) // 100ms debounce
    })
  }, [filters, storeId])

  // Load customer statistics
  const loadStats = useCallback(async () => {
    if (!serviceRef.current) return

    try {
      const customerStats = await serviceRef.current.getCustomerStats()
      setStats(customerStats)
      console.log('✅ Customer statistics loaded')
    } catch (error) {
      console.error('❌ Error loading customer statistics:', error)
    }
  }, [storeId])

  // Setup real-time subscription
  useEffect(() => {
    const currentOptions = optionsRef.current
    if (!serviceRef.current || !currentOptions.realtime) return

    console.log('🔔 Setting up real-time customers subscription')

    const unsubscribe = serviceRef.current.subscribeToCustomers(
      (realtimeCustomers) => {
        setCustomers(realtimeCustomers)
        setLoading(false)
        console.log('🔄 Real-time customers update received')
      },
      {
        limit: currentOptions.limit,
        orderBy: currentOptions.orderBy,
        orderDirection: currentOptions.orderDirection
      }
    )

    unsubscribeRef.current = unsubscribe

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
        unsubscribeRef.current = null
      }
    }
  }, [storeId])

  // Initial load
  useEffect(() => {
    if (storeId && serviceRef.current) {
      const currentOptions = optionsRef.current
      if (currentOptions.realtime) {
        // Real-time subscription will handle loading
        setLoading(false)
      } else {
        loadCustomers()
      }
      loadStats()
    }
  }, [storeId])

  // Refresh customers
  const refreshCustomers = useCallback(async () => {
    setRefreshing(true)
    lastDocRef.current = null
    await Promise.all([loadCustomers(), loadStats()])
    setRefreshing(false)
  }, [loadCustomers, loadStats])

  // Create customer
  const createCustomer = useCallback(async (customerData: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
    if (!serviceRef.current) {
      throw new Error('Service not initialized')
    }

    try {
      const customerId = await serviceRef.current.createCustomer(customerData)
      toast.success('Customer created successfully!')
      
      // Refresh data if not using real-time
      if (!optionsRef.current.realtime) {
        await refreshCustomers()
      }
      
      return customerId
    } catch (error) {
      console.error('❌ Error creating customer:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to create customer'
      toast.error(errorMessage)
      throw error
    }
  }, [refreshCustomers, options.realtime])

  // Update customer
  const updateCustomer = useCallback(async (customerId: string, updates: Partial<Customer>): Promise<void> => {
    if (!serviceRef.current) {
      throw new Error('Service not initialized')
    }

    try {
      await serviceRef.current.updateCustomer(customerId, updates)
      toast.success('Customer updated successfully!')
      
      // Refresh data if not using real-time
      if (!optionsRef.current.realtime) {
        await refreshCustomers()
      }
    } catch (error) {
      console.error('❌ Error updating customer:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to update customer'
      toast.error(errorMessage)
      throw error
    }
  }, [refreshCustomers])

  // Delete customer
  const deleteCustomer = useCallback(async (customerId: string): Promise<void> => {
    if (!serviceRef.current) {
      throw new Error('Service not initialized')
    }

    try {
      await serviceRef.current.deleteCustomer(customerId)
      toast.success('Customer deleted successfully!')
      
      // Refresh data if not using real-time
      if (!optionsRef.current.realtime) {
        await refreshCustomers()
      }
    } catch (error) {
      console.error('❌ Error deleting customer:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete customer'
      toast.error(errorMessage)
      throw error
    }
  }, [refreshCustomers])

  // Get single customer
  const getCustomer = useCallback(async (customerId: string): Promise<Customer | null> => {
    if (!serviceRef.current) {
      throw new Error('Service not initialized')
    }

    try {
      return await serviceRef.current.getCustomer(customerId)
    } catch (error) {
      console.error('❌ Error fetching customer:', error)
      throw error
    }
  }, [])

  // Load more customers (pagination)
  const loadMore = useCallback(async () => {
    if (!hasMore || loading) return
    await loadCustomers(true)
  }, [hasMore, loading, loadCustomers])

  // Set filters
  const setFilters = useCallback((newFilters: UseCustomersOptions['filters']) => {
    setFiltersState(newFilters || {})
    lastDocRef.current = null
    
    // Reload customers with new filters if not using real-time
    if (!optionsRef.current.realtime && serviceRef.current) {
      loadCustomers()
    }
  }, [loadCustomers])

  // Set search
  const setSearch = useCallback((search: string) => {
    setFilters({ ...filters, search })
  }, [filters, setFilters])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
      }
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current)
      }
    }
  }, [])

  return {
    customers,
    stats,
    loading,
    error,
    refreshing,
    refreshCustomers,
    createCustomer,
    updateCustomer,
    deleteCustomer,
    getCustomer,
    loadMore,
    hasMore,
    setFilters,
    setSearch
  }
}
