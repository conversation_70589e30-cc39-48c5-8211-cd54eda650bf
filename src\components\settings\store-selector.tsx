import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { useStoreManagement } from '../../store/store-management'
import { useFirebaseAuthStore } from '../../store/firebase-auth'
import { useMultipleStoreSettings } from '../../hooks/use-store-settings'
import {
  Store,
  Settings,
  Users,
  Package,
  TrendingUp,
  Search,
  ExternalLink,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react'

interface StoreSelectorProps {
  currentStoreId: string
  onStoreSelect: (storeId: string) => void
}

export function StoreSelector({ currentStoreId, onStoreSelect }: StoreSelectorProps) {
  const { user } = useFirebaseAuthStore()
  const { stores, switchStore, currentStore } = useStoreManagement()
  const [searchTerm, setSearchTerm] = useState('')

  // Get settings for all stores
  const storeIds = stores.map(store => store.id)
  const { settingsMap, loading } = useMultipleStoreSettings(storeIds)

  // Filter stores based on search
  const filteredStores = stores.filter(store =>
    store.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    store.id.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStoreStatus = (storeId: string) => {
    const settings = settingsMap[storeId]
    if (!settings) return 'unknown'
    return settings.status
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'inactive':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4" />
      case 'inactive':
        return <AlertCircle className="h-4 w-4" />
      case 'maintenance':
        return <Clock className="h-4 w-4" />
      default:
        return <AlertCircle className="h-4 w-4" />
    }
  }

  // Only show store selector for super admins
  if (user?.role !== 'super_admin') {
    return null
  }

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Store className="h-5 w-5" />
          Store Management
        </CardTitle>
        <CardDescription>
          Select a store to manage its settings. You have access to {stores.length} stores.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Search */}
        <div className="mb-4">
          <Label htmlFor="store-search">Search Stores</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="store-search"
              placeholder="Search by store name or ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Store Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredStores.map((store) => {
            const isSelected = store.id === currentStoreId
            const status = getStoreStatus(store.id)
            const settings = settingsMap[store.id]

            return (
              <div
                key={store.id}
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  isSelected
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-950'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
                onClick={() => onStoreSelect(store.id)}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">
                      {store.name}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      ID: {store.id}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    {isSelected && (
                      <Badge variant="default" className="text-xs">
                        Current
                      </Badge>
                    )}
                    <Badge className={`text-xs ${getStatusColor(status)}`}>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(status)}
                        {status}
                      </div>
                    </Badge>
                  </div>
                </div>

                {settings && (
                  <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      <span>Currency: {settings.currency}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      <span>Language: {settings.language}</span>
                    </div>
                    {settings.updated_at && (
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        <span>Updated: {new Date(settings.updated_at).toLocaleDateString()}</span>
                      </div>
                    )}
                  </div>
                )}

                {loading && !settings && (
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Loading settings...
                  </div>
                )}

                <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                  <Button
                    variant={isSelected ? "default" : "outline"}
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      onStoreSelect(store.id)
                    }}
                  >
                    <Settings className="h-4 w-4 mr-1" />
                    {isSelected ? 'Managing' : 'Manage'}
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      switchStore(store.id)
                    }}
                  >
                    <ExternalLink className="h-4 w-4 mr-1" />
                    Switch
                  </Button>
                </div>
              </div>
            )
          })}
        </div>

        {filteredStores.length === 0 && (
          <div className="text-center py-8">
            <Store className="h-12 w-12 text-gray-400 mx-auto mb-3" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              No stores found
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              Try adjusting your search terms
            </p>
          </div>
        )}

        {/* Current Store Info */}
        {currentStore && (
          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              <h4 className="font-semibold text-blue-900 dark:text-blue-100">
                Currently Managing: {currentStore.name}
              </h4>
            </div>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              All settings changes will be applied to this store. You can switch to manage other stores using the buttons above.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
