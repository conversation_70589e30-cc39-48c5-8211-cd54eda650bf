import { create } from 'zustand'
import {
  signInWithEmailAndPassword,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  User as FirebaseUser
} from 'firebase/auth'
import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  collection,
  getDocs,
  serverTimestamp
} from 'firebase/firestore'
import { auth, db, isFirebaseConfigured, type AdminUser } from '../lib/firebase'
import { useStoreManagement } from './store-management'
import { activityLogger } from '../lib/activity-logger'
import { appAdminService } from '../lib/app-admin-service'

// Store ID for the current store
const STORE_ID = (import.meta as any).env?.VITE_STORE_ID || 'Womanza'

interface FirebaseAuthState {
  user: AdminUser | null
  loading: boolean
  error: string | null
  signIn: (email: string, password: string) => Promise<{ error?: string }>
  signOut: () => Promise<void>
  clearError: () => void
  checkAuth: () => Promise<void | (() => void)>
  hasPermission: (roles: string[]) => boolean
}

export const useFirebaseAuthStore = create<FirebaseAuthState>((set, get) => ({
  user: null,
  loading: true,
  error: null,

  clearError: () => set({ error: null }),

  hasPermission: (roles: string[]) => {
    const { user } = get()
    if (!user || !user.active) return false
    return roles.includes(user.role)
  },

  signIn: async (email: string, password: string) => {
    console.log('🚀 FIREBASE AUTH STORE - signIn called with:', email)
    set({ error: null, loading: true })

    try {
      console.log('🔐 Starting sign in process for:', email)

      if (!isFirebaseConfigured) {
        throw new Error('Firebase is not configured. Please check your environment variables.')
      }

      // Check if this is an app admin login first (case-insensitive)
      const isAppAdmin = email.toLowerCase() === '<EMAIL>'
      console.log('🔍 Is app admin?', isAppAdmin, 'Email:', email, 'Lowercase:', email.toLowerCase())

      if (isAppAdmin) {
        console.log('🔐 *** APP ADMIN LOGIN DETECTED *** - using app admin authentication...')
        console.log('🔐 *** THIS SHOULD BYPASS REGULAR USER PROFILE CHECK ***')

        // Verify app admin credentials
        if (password !== 'Womanza766@@') {
          console.log('❌ Invalid app admin password provided')
          throw new Error('Invalid app admin credentials')
        }

        console.log('🔑 App admin credentials verified, signing in with Firebase Auth...')

        // Sign in with Firebase Auth
        let userCredential, firebaseUser
        try {
          userCredential = await signInWithEmailAndPassword(auth, email, password)
          firebaseUser = userCredential.user
          console.log('✅ Firebase Auth successful for app admin, UID:', firebaseUser.uid)
        } catch (authError) {
          console.error('❌ Firebase Auth failed for app admin:', authError)
          throw new Error(`App admin Firebase Auth failed: ${authError.message}`)
        }

        // Create app admin user object
        const appAdminUser: AdminUser = {
          uid: firebaseUser.uid,
          id: firebaseUser.uid,
          email: firebaseUser.email!,
          name: 'Womanza App Administrator',
          fullName: 'Womanza App Administrator',
          role: 'app_admin',
          active: true,
          storeIds: [], // App admin has access to all stores
          primaryStoreId: null,
          createdBy: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          lastLoginAt: new Date().toISOString(),
          avatarUrl: null,
          profile: {
            firstName: 'Womanza',
            lastName: 'Administrator',
            phone: '',
            avatar: ''
          }
        }

        console.log('✅ App admin authenticated successfully')
        set({ user: appAdminUser, loading: false, error: null })
        return {}
      }

      // Regular user authentication
      console.log('👤 Regular user login, checking user profile...')

      // Sign in with Firebase Auth
      const userCredential = await signInWithEmailAndPassword(auth, email, password)
      const firebaseUser = userCredential.user

      // Get user data from Firestore
      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid))
      let userData: AdminUser

      if (!userDoc.exists()) {
        console.log('⚠️  User profile not found in Firestore, checking for recovery options...')

        // Check if this is a known super admin that needs profile recovery
        const knownSuperAdmins = ['<EMAIL>'] // Add other known super admins here

        if (firebaseUser.email && knownSuperAdmins.includes(firebaseUser.email.toLowerCase())) {
          console.log('🔄 Attempting to recover super admin profile...')

          // Try to find the pending request to get user details
          const pendingSnapshot = await getDocs(collection(db, 'pending_store_requests'))
          let recoveryData = null

          for (const requestDoc of pendingSnapshot.docs) {
            const requestData = requestDoc.data()
            if (firebaseUser.email && requestData?.owner?.email?.toLowerCase() === firebaseUser.email.toLowerCase()) {
              recoveryData = requestData
              break
            }
          }

          if (recoveryData) {
            console.log('📋 Found pending request data for profile recovery')

            // Create a basic user profile for recovery
            const recoveredUserData: AdminUser = {
              uid: firebaseUser.uid,
              id: firebaseUser.uid,
              email: firebaseUser.email || '',
              name: recoveryData?.owner?.name || 'Unknown User',
              fullName: recoveryData?.owner?.name || 'Unknown User',
              phone: recoveryData?.owner?.phone || null,
              role: 'super_admin' as const,
              primaryStoreId: recoveryData?.store?.slug || null,
              storeIds: [recoveryData?.store?.slug || ''].filter(Boolean),
              active: true,
              createdBy: 'system_recovery',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              lastLoginAt: null,
              avatarUrl: null,
              profile: {
                firstName: recoveryData?.owner?.name?.split(' ')[0] || recoveryData?.owner?.name || 'Unknown',
                lastName: recoveryData?.owner?.name?.split(' ').slice(1).join(' ') || '',
                phone: recoveryData?.owner?.phone || '',
                avatar: ''
              },
              permissions: {
                canManageUsers: true,
                canManageProducts: true,
                canManageOrders: true,
                canViewAnalytics: true,
                canManageSettings: true,
                canManageCategories: true
              }
            }

            // Create the recovered user profile
            await setDoc(doc(db, 'users', firebaseUser.uid), {
              ...recoveredUserData,
              createdAt: serverTimestamp(),
              updatedAt: serverTimestamp()
            })

            console.log('✅ User profile recovered successfully')

            // Use the recovered data
            userData = recoveredUserData

            // Continue with the rest of the authentication process
            console.log('🏪 Validating store access for recovered user:', userData.email)

          } else {
            throw new Error('User profile not found and cannot be recovered. Please contact your administrator.')
          }
        } else {
          throw new Error('User profile not found. Please contact your administrator.')
        }
      } else {
        userData = userDoc.data() as AdminUser
      }

      // Enhanced store access validation
      console.log('🏪 Validating store access for user:', userData.email)
      console.log('   User role:', userData.role)
      console.log('   User storeIds:', userData.storeIds)
      console.log('   User primaryStoreId:', userData.primaryStoreId)

      // Ensure user document exists in /users collection for Firestore rules
      try {
        const userDocRef = doc(db, 'users', firebaseUser.uid)
        const userDocSnapshot = await getDoc(userDocRef)

        if (!userDocSnapshot.exists()) {
          console.log('🔧 Creating missing user document for Firestore rules...')
          await setDoc(userDocRef, {
            uid: firebaseUser.uid,
            email: userData.email,
            role: userData.role,
            storeIds: userData.storeIds || [],
            primaryStoreId: userData.primaryStoreId || userData.storeId,
            active: userData.active,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          })
          console.log('✅ User document created for Firestore rules')
        } else {
          // Update existing user document to ensure it has latest data
          await setDoc(userDocRef, {
            uid: firebaseUser.uid,
            email: userData.email,
            role: userData.role,
            storeIds: userData.storeIds || [],
            primaryStoreId: userData.primaryStoreId || userData.storeId,
            active: userData.active,
            updatedAt: serverTimestamp()
          }, { merge: true })
          console.log('✅ User document updated for Firestore rules')
        }
      } catch (error) {
        console.error('❌ Failed to create/update user document:', error)
      }

      if (userData.role === 'app_admin') {
        console.log('✅ App admin access granted')

        // Initialize app admin document after successful authentication
        try {
          await appAdminService.initializeAppAdmin()
        } catch (error) {
          console.warn('Failed to initialize app admin document:', error)
        }
      } else {
        // For store users, validate store access
        const userStoreIds = userData.storeIds || []
        const primaryStoreId = userData.primaryStoreId || userData.storeId || null

        if (!primaryStoreId && userStoreIds.length === 0) {
          throw new Error('No store access configured. Please contact your administrator.')
        }

        // Check if user has access to any store
        let hasStoreAccess = false
        let accessibleStoreId = null

        if (primaryStoreId) {
          // Check primary store access
          console.log('🔍 Checking primary store access for:', primaryStoreId)
          const storeDoc = await getDoc(doc(db, 'stores', primaryStoreId))
          console.log('   Store exists:', storeDoc.exists())

          if (storeDoc.exists()) {
            const storeData = storeDoc.data()
            console.log('   Store status:', storeData.status)
            console.log('   Store name:', storeData.name)

            if (storeData.status === 'active') {
              // Super admins have automatic access to their primary store
              if (userData.role === 'super_admin') {
                hasStoreAccess = true
                accessibleStoreId = primaryStoreId
                console.log('✅ Super admin access granted to primary store:', primaryStoreId)
              } else {
                // Verify regular users exist in store-specific users collection
                const storeUserDoc = await getDoc(doc(db, 'stores', primaryStoreId, 'users', firebaseUser.uid))
                if (storeUserDoc.exists() && storeUserDoc.data().active) {
                  hasStoreAccess = true
                  accessibleStoreId = primaryStoreId
                  console.log('✅ Primary store access validated:', primaryStoreId)
                }
              }
            } else {
              console.log('⚠️  Store is not active, status:', storeData.status)
            }
          } else {
            console.log('⚠️  Primary store document does not exist:', primaryStoreId)
          }
        }

        // If no primary store access, check other stores
        if (!hasStoreAccess && userStoreIds.length > 0) {
          console.log('🔍 Checking fallback stores:', userStoreIds)
          for (const storeId of userStoreIds) {
            console.log('   Checking store:', storeId)
            const storeDoc = await getDoc(doc(db, 'stores', storeId))
            console.log('   Store exists:', storeDoc.exists())

            if (storeDoc.exists()) {
              const storeData = storeDoc.data()
              console.log('   Store status:', storeData.status)

              if (storeData.status === 'active') {
                // Super admins have automatic access to their assigned stores
                if (userData.role === 'super_admin') {
                  hasStoreAccess = true
                  accessibleStoreId = storeId
                  console.log('✅ Super admin access granted to store:', storeId)
                  break
                } else {
                  // Verify regular users exist in store-specific users collection
                  const storeUserDoc = await getDoc(doc(db, 'stores', storeId, 'users', firebaseUser.uid))
                  if (storeUserDoc.exists() && storeUserDoc.data().active) {
                    hasStoreAccess = true
                    accessibleStoreId = storeId
                    console.log('✅ Store access validated:', storeId)
                    break
                  }
                }
              }
            }
          }
        }

        if (!hasStoreAccess) {
          console.log('❌ Store access validation failed:')
          console.log('   User role:', userData.role)
          console.log('   Primary store ID:', primaryStoreId)
          console.log('   User store IDs:', userStoreIds)
          console.log('   Has store access:', hasStoreAccess)
          console.log('   Accessible store ID:', accessibleStoreId)
          throw new Error('Access denied. You do not have permission to access any active store.')
        }

        // Ensure super admin has a user document in the store subcollection
        if (accessibleStoreId && userData.role === 'super_admin') {
          const storeUserDoc = await getDoc(doc(db, 'stores', accessibleStoreId, 'users', firebaseUser.uid))
          if (!storeUserDoc.exists()) {
            console.log('🔧 Creating missing store user document for super admin...')
            try {
              await setDoc(doc(db, 'stores', accessibleStoreId, 'users', firebaseUser.uid), {
                uid: firebaseUser.uid,
                email: userData.email,
                role: 'super_admin',
                active: true,
                permissions: {
                  canManageUsers: true,
                  canManageProducts: true,
                  canManageOrders: true,
                  canViewAnalytics: true,
                  canManageSettings: true
                },
                profile: userData.profile,
                createdAt: serverTimestamp(),
                updatedAt: serverTimestamp()
              })
              console.log('✅ Store user document created for super admin')
            } catch (error) {
              console.warn('⚠️  Could not create store user document:', error.message)
            }
          }
        }

        // Add current store info to user data and set it in store management
        if (accessibleStoreId) {
          const storeDoc = await getDoc(doc(db, 'stores', accessibleStoreId))
          if (storeDoc.exists()) {
            const storeData = storeDoc.data()
            userData.currentStore = {
              id: accessibleStoreId,
              name: storeData.name,
              slug: storeData.slug
            }

            // Set the current store in store management
            const storeManagement = useStoreManagement.getState()
            storeManagement.setCurrentStore({
              id: accessibleStoreId,
              ...storeData
            } as any)
            console.log('✅ Current store set in store management:', accessibleStoreId)
          }
        }
      }

      // Verify user is active
      if (!userData.active) {
        throw new Error('Your account has been deactivated. Please contact your administrator.')
      }

      // Update last login time
      await updateDoc(doc(db, 'users', firebaseUser.uid), {
        lastLoginAt: serverTimestamp()
      })

      // Set user in store
      const adminUser: AdminUser = {
        ...userData,
        id: firebaseUser.uid,
        lastLoginAt: new Date().toISOString()
      }

      // Log successful login activity
      try {
        const storeId = adminUser.currentStore?.id || adminUser.primaryStoreId || adminUser.storeId || STORE_ID
        await activityLogger.logLogin(
          adminUser.uid || adminUser.id,
          adminUser.fullName || adminUser.name || adminUser.email,
          adminUser.role,
          storeId
        )
      } catch (error) {
        console.warn('Failed to log login activity:', error)
      }

      set({ user: adminUser, loading: false, error: null })
      return {}

    } catch (error: any) {
      console.error('Sign in error:', error)
      let errorMessage = 'An unexpected error occurred. Please try again.'

      if (error.code === 'auth/invalid-credential') {
        errorMessage = 'Invalid email or password. Please check your credentials.'
      } else if (error.code === 'auth/user-not-found') {
        errorMessage = 'No account found with this email address.'
      } else if (error.code === 'auth/wrong-password') {
        errorMessage = 'Incorrect password. Please try again.'
      } else if (error.code === 'auth/too-many-requests') {
        errorMessage = 'Too many failed attempts. Please try again later.'
      } else if (error.code === 'auth/user-disabled') {
        errorMessage = 'This account has been disabled. Please contact support.'
      } else if (error.message) {
        errorMessage = error.message
      }

      // Log failed login attempt
      try {
        await activityLogger.logLoginFailed(email, STORE_ID)
      } catch (logError) {
        console.warn('Failed to log failed login activity:', logError)
      }

      set({ error: errorMessage, loading: false })
      return { error: errorMessage }
    }
  },

  signOut: async () => {
    try {
      if (isFirebaseConfigured) {
        await firebaseSignOut(auth)
      }
      set({ user: null, loading: false, error: null })
    } catch (error: any) {
      console.error('Sign out error:', error)
      // Even if sign out fails, clear the local state
      set({ user: null, loading: false, error: null })
    }
  },

  checkAuth: async () => {
    try {
      if (!isFirebaseConfigured) {
        set({ user: null, loading: false })
        return
      }

      // Set up auth state listener
      const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
        if (firebaseUser) {
          try {
            // Get user data from Firestore
            const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid))

            if (userDoc.exists()) {
              const userData = userDoc.data() as AdminUser

              // Verify user is active
              if (userData.active) {
                const adminUser: AdminUser = {
                  ...userData,
                  id: firebaseUser.uid
                }
                set({ user: adminUser, loading: false })

                // Load current store (individual store mode)
                const storeManagement = useStoreManagement.getState()
                if (storeManagement.fetchCurrentStore) {
                  await storeManagement.fetchCurrentStore()
                }
              } else {
                // User is inactive
                await firebaseSignOut(auth)
                set({ user: null, loading: false })
              }
            } else {
              // User document doesn't exist
              await firebaseSignOut(auth)
              set({ user: null, loading: false })
            }
          } catch (error) {
            console.error('Error fetching user data:', error)
            set({ user: null, loading: false })
          }
        } else {
          set({ user: null, loading: false })
        }
      })

      // Store the unsubscribe function for cleanup
      return unsubscribe
    } catch (error) {
      console.error('Auth check error:', error)
      set({ user: null, loading: false })
    }
  }
}))

// Initialize auth check on store creation
useFirebaseAuthStore.getState().checkAuth()
