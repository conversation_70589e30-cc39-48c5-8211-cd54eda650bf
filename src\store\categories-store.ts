import { create } from 'zustand'
import { 
  collection, 
  doc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  orderBy, 
  onSnapshot,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore'
import { db } from '../lib/firebase'
import { useStoreManagement } from './store-management'

export interface Category {
  id: string
  name: string
  description: string
  slug: string
  status: 'active' | 'inactive'
  sortOrder: number
  parentId?: string
  storeId: string
  createdAt: Timestamp | any
  updatedAt: Timestamp | any
}

interface CategoriesState {
  categories: Category[]
  loading: boolean
  error: string | null
  
  // Actions
  fetchCategories: () => Promise<void>
  addCategory: (categoryData: Omit<Category, 'id' | 'createdAt' | 'updatedAt' | 'storeId'>) => Promise<string>
  updateCategory: (categoryId: string, updates: Partial<Category>) => Promise<void>
  deleteCategory: (categoryId: string) => Promise<void>
  subscribeToCategories: () => () => void
  clearError: () => void
}

export const useCategoriesStore = create<CategoriesState>((set, get) => ({
  categories: [],
  loading: false,
  error: null,

  clearError: () => set({ error: null }),

  fetchCategories: async () => {
    const { currentStoreId } = useStoreManagement.getState()
    
    if (!currentStoreId) {
      set({ categories: [], loading: false, error: 'No store selected' })
      return
    }

    try {
      set({ loading: true, error: null })
      
      const categoriesRef = collection(db, 'stores', currentStoreId, 'categories')
      const q = query(categoriesRef, orderBy('sortOrder', 'asc'))
      const snapshot = await getDocs(q)
      
      const categories = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Category[]
      
      set({ categories, loading: false })
      console.log('🏷️ Fetched categories:', categories.length)
    } catch (error: any) {
      console.error('Error fetching categories:', error)
      set({ error: error.message, loading: false })
    }
  },

  addCategory: async (categoryData) => {
    const { currentStoreId } = useStoreManagement.getState()
    
    if (!currentStoreId) {
      throw new Error('No store selected')
    }

    try {
      set({ loading: true, error: null })
      
      const categoriesRef = collection(db, 'stores', currentStoreId, 'categories')
      const docRef = await addDoc(categoriesRef, {
        ...categoryData,
        storeId: currentStoreId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      })
      
      console.log('✅ Category added successfully:', docRef.id)
      set({ loading: false })
      return docRef.id
    } catch (error: any) {
      console.error('❌ Error adding category:', error)
      set({ error: error.message, loading: false })
      throw error
    }
  },

  updateCategory: async (categoryId: string, updates) => {
    const { currentStoreId } = useStoreManagement.getState()
    
    if (!currentStoreId) {
      throw new Error('No store selected')
    }

    try {
      set({ loading: true, error: null })
      
      const categoryRef = doc(db, 'stores', currentStoreId, 'categories', categoryId)
      await updateDoc(categoryRef, {
        ...updates,
        updatedAt: serverTimestamp()
      })
      
      // Update local state
      const { categories } = get()
      const updatedCategories = categories.map(category => 
        category.id === categoryId 
          ? { ...category, ...updates, updatedAt: new Date() }
          : category
      )
      
      set({ categories: updatedCategories, loading: false })
      console.log('✅ Category updated successfully:', categoryId)
    } catch (error: any) {
      console.error('❌ Error updating category:', error)
      set({ error: error.message, loading: false })
      throw error
    }
  },

  deleteCategory: async (categoryId: string) => {
    const { currentStoreId } = useStoreManagement.getState()
    
    if (!currentStoreId) {
      throw new Error('No store selected')
    }

    try {
      set({ loading: true, error: null })
      
      const categoryRef = doc(db, 'stores', currentStoreId, 'categories', categoryId)
      await deleteDoc(categoryRef)
      
      // Update local state
      const { categories } = get()
      const filteredCategories = categories.filter(category => category.id !== categoryId)
      
      set({ categories: filteredCategories, loading: false })
      console.log('✅ Category deleted successfully:', categoryId)
    } catch (error: any) {
      console.error('❌ Error deleting category:', error)
      set({ error: error.message, loading: false })
      throw error
    }
  },

  subscribeToCategories: () => {
    const { currentStoreId } = useStoreManagement.getState()
    
    if (!currentStoreId) {
      console.warn('No store selected for categories subscription')
      return () => {}
    }

    const categoriesRef = collection(db, 'stores', currentStoreId, 'categories')
    const q = query(categoriesRef, orderBy('sortOrder', 'asc'))
    
    const unsubscribe = onSnapshot(q, 
      (snapshot) => {
        const categories = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Category[]
        
        set({ categories, loading: false, error: null })
        console.log('🔄 Categories updated in real-time:', categories.length)
      },
      (error) => {
        console.error('❌ Error in categories subscription:', error)
        set({ error: error.message, loading: false })
      }
    )
    
    return unsubscribe
  }
}))

// Auto-subscribe when store is initialized
let currentUnsubscribe: (() => void) | null = null

export const initializeCategoriesStore = (storeId: string) => {
  // Cleanup previous subscription
  if (currentUnsubscribe) {
    currentUnsubscribe()
  }
  
  // Start new subscription
  const store = useCategoriesStore.getState()
  currentUnsubscribe = store.subscribeToCategories()
  
  console.log('🚀 Categories store initialized for:', storeId)
  return currentUnsubscribe
}
