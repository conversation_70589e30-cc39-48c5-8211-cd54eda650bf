import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Alert, AlertDescription } from '../components/ui/alert'
import { Trash2, <PERSON><PERSON><PERSON><PERSON>gle, Loader2 } from 'lucide-react'
import { 
  collection, 
  getDocs, 
  writeBatch, 
  doc
} from 'firebase/firestore'
import { db } from '../lib/firebase'
import { toast } from 'react-hot-toast'

export function QuickCleanupPage() {
  const [loading, setLoading] = useState(false)
  const [confirmed, setConfirmed] = useState(false)

  const deleteCollection = async (collectionName: string) => {
    console.log(`Deleting collection: ${collectionName}`)
    
    try {
      const snapshot = await getDocs(collection(db, collectionName))
      
      if (snapshot.empty) {
        console.log(`No documents in ${collectionName}`)
        return 0
      }

      const batch = writeBatch(db)
      let count = 0
      
      snapshot.docs.forEach((document) => {
        batch.delete(document.ref)
        count++
      })
      
      await batch.commit()
      console.log(`Deleted ${count} documents from ${collectionName}`)
      return count
    } catch (error: any) {
      console.error(`Error deleting ${collectionName}:`, error)
      toast.error(`Failed to delete ${collectionName}: ${error.message}`)
      return 0
    }
  }

  const deleteSubcollections = async (parentCollection: string, subcollections: string[]) => {
    console.log(`Deleting subcollections in ${parentCollection}`)
    
    try {
      const parentSnapshot = await getDocs(collection(db, parentCollection))
      let totalDeleted = 0
      
      for (const parentDoc of parentSnapshot.docs) {
        for (const subcollectionName of subcollections) {
          const subcollectionRef = collection(db, parentCollection, parentDoc.id, subcollectionName)
          const subcollectionSnapshot = await getDocs(subcollectionRef)
          
          if (!subcollectionSnapshot.empty) {
            const batch = writeBatch(db)
            let count = 0
            
            subcollectionSnapshot.docs.forEach((subDoc) => {
              batch.delete(subDoc.ref)
              count++
            })
            
            await batch.commit()
            totalDeleted += count
            console.log(`Deleted ${count} documents from ${parentCollection}/${parentDoc.id}/${subcollectionName}`)
          }
        }
      }
      
      return totalDeleted
    } catch (error: any) {
      console.error(`Error deleting subcollections in ${parentCollection}:`, error)
      toast.error(`Failed to delete subcollections: ${error.message}`)
      return 0
    }
  }

  const performCleanup = async () => {
    if (!confirmed) {
      toast.error('Please confirm the cleanup by checking the checkbox')
      return
    }

    setLoading(true)
    let totalDeleted = 0

    try {
      toast.loading('Starting cleanup...', { id: 'cleanup' })

      // Delete store subcollections first
      const subcollectionCount = await deleteSubcollections('stores', ['products', 'orders', 'categories', 'users'])
      totalDeleted += subcollectionCount

      // Delete main collections
      const collections = ['stores', 'users', 'customers', 'store-requests', 'pending_store_requests', 'app_admins']
      
      for (const collectionName of collections) {
        const count = await deleteCollection(collectionName)
        totalDeleted += count
      }

      toast.success(`Cleanup completed! Deleted ${totalDeleted} documents`, { id: 'cleanup' })
      setConfirmed(false)
      
    } catch (error: any) {
      console.error('Cleanup failed:', error)
      toast.error(`Cleanup failed: ${error.message}`, { id: 'cleanup' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6 p-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Quick Firebase Cleanup</h1>
        <p className="text-gray-600 mt-2">
          Clean all Firebase data to start fresh with store registration
        </p>
      </div>

      <Alert className="border-red-200 bg-red-50">
        <AlertTriangle className="h-4 w-4 text-red-600" />
        <AlertDescription className="text-red-800">
          <strong>Warning:</strong> This will permanently delete ALL data from Firebase including:
          stores, users, customers, products, orders, and store requests. This action cannot be undone.
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-red-600" />
            Complete Data Cleanup
          </CardTitle>
          <CardDescription>
            This will delete all collections and documents from your Firebase project.
            After cleanup, you can register a new store via the registration form.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">What will be deleted:</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• All stores and their products, orders, categories</li>
              <li>• All user accounts and customers</li>
              <li>• All store registration requests</li>
              <li>• All app admin accounts</li>
            </ul>
          </div>

          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              id="confirm-cleanup"
              checked={confirmed}
              onChange={(e) => setConfirmed(e.target.checked)}
              className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
            />
            <label htmlFor="confirm-cleanup" className="text-sm text-gray-700">
              I understand this will permanently delete all Firebase data and cannot be undone
            </label>
          </div>

          <div className="flex gap-3">
            <Button
              onClick={performCleanup}
              disabled={loading || !confirmed}
              variant="destructive"
              className="flex items-center gap-2"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="h-4 w-4" />
              )}
              {loading ? 'Cleaning...' : 'Clean All Data'}
            </Button>
            
            <Button
              onClick={() => window.location.href = '/register'}
              variant="outline"
              disabled={loading}
            >
              Go to Registration
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="pt-6">
          <h3 className="font-medium text-blue-900 mb-2">After Cleanup:</h3>
          <ol className="text-sm text-blue-800 space-y-1">
            <li>1. Visit the registration form at <code>/register</code></li>
            <li>2. Fill out your store information</li>
            <li>3. Submit the registration request</li>
            <li>4. Login as app admin to approve the request</li>
            <li>5. Start using your new store admin panel</li>
          </ol>
        </CardContent>
      </Card>
    </div>
  )
}
