import { useState, useEffect } from 'react'
import { collection, getDocs, doc, updateDoc, setDoc, serverTimestamp } from 'firebase/firestore'
import { db } from '../lib/firebase'
import { useFirebaseAuthStore } from '../store/firebase-auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Badge } from '../components/ui/badge'
import { Textarea } from '../components/ui/textarea'
import { Label } from '../components/ui/label'
import { 
  Shield, 
  Store, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react'

interface PendingRequest {
  id: string
  store: {
    id: string
    name: string
    slug: string
    currency: string
    country: string
    description: string
  }
  owner: {
    uid: string
    name: string
    email: string
    phone: string
  }
  status: 'pending' | 'approved' | 'rejected'
  createdAt: any
  rejectionReason?: string
}

export function AppAdminPage() {
  const { user } = useFirebaseAuthStore()
  const [pendingRequests, setPendingRequests] = useState<PendingRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [processingId, setProcessingId] = useState<string | null>(null)

  useEffect(() => {
    if (user?.role === 'app_admin') {
      fetchPendingRequests()
    }
  }, [user])

  const fetchPendingRequests = async () => {
    try {
      setLoading(true)
      const snapshot = await getDocs(collection(db, 'pending_store_requests'))
      const requests = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as PendingRequest[]
      
      // Sort by creation date, newest first
      requests.sort((a, b) => {
        const dateA = a.createdAt?.toDate?.() || new Date(a.createdAt)
        const dateB = b.createdAt?.toDate?.() || new Date(b.createdAt)
        return dateB.getTime() - dateA.getTime()
      })
      
      setPendingRequests(requests)
    } catch (err: any) {
      setError(`Failed to fetch requests: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  const approveRequest = async (request: PendingRequest) => {
    setProcessingId(request.id)
    try {
      // Create the store
      await setDoc(doc(db, 'stores', request.store.id), {
        name: request.store.name,
        slug: request.store.slug,
        currency: request.store.currency,
        country: request.store.country,
        description: request.store.description,
        ownerId: request.owner.uid,
        status: 'active',
        domain: null,
        contactEmail: request.owner.email,
        contactPhone: request.owner.phone,
        logoUrl: null,
        primaryColor: '#3B82F6',
        secondaryColor: '#1E40AF',
        taxRate: 0,
        settings: {
          timezone: 'UTC',
          language: 'en',
          theme: 'light'
        },
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      })

      // Update user to activate and assign store
      await updateDoc(doc(db, 'users', request.owner.uid), {
        storeIds: [request.store.id],
        activeStoreId: request.store.id,
        active: true,
        updatedAt: serverTimestamp()
      })

      // Update request status
      await updateDoc(doc(db, 'pending_store_requests', request.id), {
        status: 'approved',
        approvedAt: serverTimestamp(),
        approvedBy: user?.uid
      })

      // Refresh the list
      await fetchPendingRequests()
      
    } catch (err: any) {
      setError(`Failed to approve request: ${err.message}`)
    } finally {
      setProcessingId(null)
    }
  }

  const rejectRequest = async (request: PendingRequest, reason: string) => {
    setProcessingId(request.id)
    try {
      // Update request status
      await updateDoc(doc(db, 'pending_store_requests', request.id), {
        status: 'rejected',
        rejectionReason: reason,
        rejectedAt: serverTimestamp(),
        rejectedBy: user?.uid
      })

      // Deactivate user
      await updateDoc(doc(db, 'users', request.owner.uid), {
        active: false,
        rejectionReason: reason,
        updatedAt: serverTimestamp()
      })

      // Refresh the list
      await fetchPendingRequests()
      
    } catch (err: any) {
      setError(`Failed to reject request: ${err.message}`)
    } finally {
      setProcessingId(null)
    }
  }

  if (user?.role !== 'app_admin') {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <Card>
          <CardHeader className="text-center">
            <Shield className="h-12 w-12 text-red-400 mx-auto mb-4" />
            <CardTitle className="text-red-900">Access Denied</CardTitle>
            <CardDescription>
              You don't have permission to access this page.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">App Admin Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Manage store registration requests and system settings
          </p>
        </div>
        <Button onClick={fetchPendingRequests} variant="outline">
          Refresh
        </Button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-800">{error}</p>
        </div>
      )}

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Requests</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {pendingRequests.filter(r => r.status === 'pending').length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {pendingRequests.filter(r => r.status === 'approved').length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejected</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {pendingRequests.filter(r => r.status === 'rejected').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Pending Requests */}
      <Card>
        <CardHeader>
          <CardTitle>Store Registration Requests</CardTitle>
          <CardDescription>
            Review and approve new store registrations
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">Loading requests...</p>
            </div>
          ) : pendingRequests.length === 0 ? (
            <div className="text-center py-8">
              <Store className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No store requests found</p>
            </div>
          ) : (
            <div className="space-y-6">
              {pendingRequests.map((request) => (
                <RequestCard
                  key={request.id}
                  request={request}
                  onApprove={() => approveRequest(request)}
                  onReject={(reason) => rejectRequest(request, reason)}
                  processing={processingId === request.id}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

function RequestCard({ 
  request, 
  onApprove, 
  onReject, 
  processing 
}: { 
  request: PendingRequest
  onApprove: () => void
  onReject: (reason: string) => void
  processing: boolean
}) {
  const [showRejectForm, setShowRejectForm] = useState(false)
  const [rejectionReason, setRejectionReason] = useState('')

  const handleReject = () => {
    if (rejectionReason.trim()) {
      onReject(rejectionReason)
      setShowRejectForm(false)
      setRejectionReason('')
    }
  }

  const getStatusBadge = () => {
    switch (request.status) {
      case 'pending':
        return <Badge variant="secondary"><Clock className="h-3 w-3 mr-1" />Pending</Badge>
      case 'approved':
        return <Badge variant="default"><CheckCircle className="h-3 w-3 mr-1" />Approved</Badge>
      case 'rejected':
        return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />Rejected</Badge>
      default:
        return null
    }
  }

  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <Store className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <CardTitle className="text-lg">{request.store.name}</CardTitle>
              <CardDescription>
                Requested on {new Date(request.createdAt?.toDate?.() || request.createdAt).toLocaleDateString()}
              </CardDescription>
            </div>
          </div>
          {getStatusBadge()}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Store Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium flex items-center gap-2">
              <Store className="h-4 w-4" />
              Store Details
            </h4>
            <div className="text-sm space-y-1">
              <p><span className="font-medium">Slug:</span> {request.store.slug}</p>
              <p><span className="font-medium">Currency:</span> {request.store.currency}</p>
              <p><span className="font-medium">Country:</span> {request.store.country}</p>
              {request.store.description && (
                <p><span className="font-medium">Description:</span> {request.store.description}</p>
              )}
            </div>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium flex items-center gap-2">
              <User className="h-4 w-4" />
              Owner Details
            </h4>
            <div className="text-sm space-y-1">
              <p><span className="font-medium">Name:</span> {request.owner.name}</p>
              <p><span className="font-medium">Email:</span> {request.owner.email}</p>
              {request.owner.phone && (
                <p><span className="font-medium">Phone:</span> {request.owner.phone}</p>
              )}
            </div>
          </div>
        </div>

        {/* Rejection Reason */}
        {request.status === 'rejected' && request.rejectionReason && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-800">
              <span className="font-medium">Rejection Reason:</span> {request.rejectionReason}
            </p>
          </div>
        )}

        {/* Actions */}
        {request.status === 'pending' && (
          <div className="flex gap-3 pt-4 border-t">
            {!showRejectForm ? (
              <>
                <Button 
                  onClick={onApprove}
                  disabled={processing}
                  className="flex-1"
                >
                  {processing ? 'Approving...' : 'Approve Store'}
                </Button>
                <Button 
                  onClick={() => setShowRejectForm(true)}
                  variant="outline"
                  disabled={processing}
                  className="flex-1"
                >
                  Reject
                </Button>
              </>
            ) : (
              <div className="w-full space-y-3">
                <div>
                  <Label htmlFor="rejectionReason">Rejection Reason</Label>
                  <Textarea
                    id="rejectionReason"
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    placeholder="Please provide a reason for rejection..."
                    rows={3}
                  />
                </div>
                <div className="flex gap-2">
                  <Button 
                    onClick={handleReject}
                    variant="destructive"
                    disabled={!rejectionReason.trim() || processing}
                    className="flex-1"
                  >
                    {processing ? 'Rejecting...' : 'Confirm Rejection'}
                  </Button>
                  <Button 
                    onClick={() => {
                      setShowRejectForm(false)
                      setRejectionReason('')
                    }}
                    variant="outline"
                    disabled={processing}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
