import { 
  collection, 
  query, 
  where, 
  onSnapshot, 
  getDocs,
  doc,
  getDoc
} from 'firebase/firestore'
import { db } from './firebase'

export interface Permission {
  id: string
  name: string
  description: string
  category: string
}

export interface Role {
  id: string
  name: string
  displayName: string
  description: string
  permissions: string[]
  userCount: number
  isSystem: boolean
}

// System permissions definition
export const SYSTEM_PERMISSIONS: Permission[] = [
  // Dashboard
  { id: 'dashboard.view', name: 'View Dashboard', description: 'Access to main dashboard', category: 'Dashboard' },
  
  // Products
  { id: 'products.view', name: 'View Products', description: 'View product listings', category: 'Products' },
  { id: 'products.create', name: 'Create Products', description: 'Add new products', category: 'Products' },
  { id: 'products.edit', name: 'Edit Products', description: 'Modify existing products', category: 'Products' },
  { id: 'products.delete', name: 'Delete Products', description: 'Remove products', category: 'Products' },
  { id: 'products.publish', name: 'Publish Products', description: 'Control product visibility', category: 'Products' },
  
  // Categories
  { id: 'categories.view', name: 'View Categories', description: 'View category listings', category: 'Categories' },
  { id: 'categories.create', name: 'Create Categories', description: 'Add new categories', category: 'Categories' },
  { id: 'categories.edit', name: 'Edit Categories', description: 'Modify categories', category: 'Categories' },
  { id: 'categories.delete', name: 'Delete Categories', description: 'Remove categories', category: 'Categories' },
  
  // Orders
  { id: 'orders.view', name: 'View Orders', description: 'View order listings', category: 'Orders' },
  { id: 'orders.edit', name: 'Edit Orders', description: 'Modify order details', category: 'Orders' },
  { id: 'orders.fulfill', name: 'Fulfill Orders', description: 'Process and fulfill orders', category: 'Orders' },
  { id: 'orders.cancel', name: 'Cancel Orders', description: 'Cancel orders', category: 'Orders' },
  
  // Customers
  { id: 'customers.view', name: 'View Customers', description: 'View customer data', category: 'Customers' },
  { id: 'customers.edit', name: 'Edit Customers', description: 'Modify customer information', category: 'Customers' },
  { id: 'customers.segment', name: 'Customer Segmentation', description: 'Create customer groups', category: 'Customers' },
  
  // Marketing
  { id: 'marketing.view', name: 'View Marketing', description: 'View marketing campaigns', category: 'Marketing' },
  { id: 'marketing.create', name: 'Create Campaigns', description: 'Create marketing campaigns', category: 'Marketing' },
  { id: 'marketing.edit', name: 'Edit Campaigns', description: 'Modify campaigns', category: 'Marketing' },
  
  // Analytics
  { id: 'analytics.view', name: 'View Analytics', description: 'Access analytics and reports', category: 'Analytics' },
  { id: 'analytics.export', name: 'Export Reports', description: 'Export analytics data', category: 'Analytics' },
  
  // Settings
  { id: 'settings.store', name: 'Store Settings', description: 'Modify store configuration', category: 'Settings' },
  { id: 'settings.users', name: 'User Management', description: 'Manage staff and permissions', category: 'Settings' },
  
  // File Storage
  { id: 'files.upload', name: 'Upload Files', description: 'Upload images and documents', category: 'Files' },
  { id: 'files.manage', name: 'Manage Files', description: 'Organize and delete files', category: 'Files' }
]

// System roles definition
export const SYSTEM_ROLES: Omit<Role, 'userCount'>[] = [
  {
    id: 'super_admin',
    name: 'super_admin',
    displayName: 'Super Admin',
    description: 'Store owner with full access to all features',
    permissions: SYSTEM_PERMISSIONS.map(p => p.id),
    isSystem: true
  },
  {
    id: 'admin',
    name: 'admin',
    displayName: 'Admin',
    description: 'Can manage products, orders, marketing, customers',
    permissions: [
      'dashboard.view',
      'products.view', 'products.create', 'products.edit', 'products.delete', 'products.publish',
      'categories.view', 'categories.create', 'categories.edit', 'categories.delete',
      'orders.view', 'orders.edit', 'orders.fulfill', 'orders.cancel',
      'customers.view', 'customers.edit', 'customers.segment',
      'marketing.view', 'marketing.create', 'marketing.edit',
      'analytics.view', 'analytics.export',
      'files.upload', 'files.manage'
    ],
    isSystem: true
  },
  {
    id: 'editor',
    name: 'editor',
    displayName: 'Editor',
    description: 'Can view and edit products, categories, orders, customers',
    permissions: [
      'dashboard.view',
      'products.view', 'products.edit',
      'categories.view', 'categories.edit',
      'orders.view', 'orders.edit',
      'customers.view', 'customers.edit',
      'files.upload'
    ],
    isSystem: true
  },
  {
    id: 'viewer',
    name: 'viewer',
    displayName: 'Viewer',
    description: 'Read-only access to all store data',
    permissions: [
      'dashboard.view',
      'products.view',
      'categories.view',
      'orders.view',
      'customers.view'
    ],
    isSystem: true
  }
]

class RolesPermissionsService {
  private storeId: string

  constructor() {
    this.storeId = import.meta.env.VITE_STORE_ID || 'womanza'
  }

  // Get all permissions (system-defined)
  getPermissions(): Permission[] {
    return SYSTEM_PERMISSIONS
  }

  // Get user count for each role from Firestore
  async getUserCountsByRole(storeId: string): Promise<Record<string, number>> {
    try {
      const counts: Record<string, number> = {}
      
      // Initialize counts
      SYSTEM_ROLES.forEach(role => {
        counts[role.name] = 0
      })

      // Get users from store subcollection
      const usersQuery = query(collection(db, 'stores', storeId, 'users'))
      const usersSnapshot = await getDocs(usersQuery)
      
      usersSnapshot.forEach((doc) => {
        const userData = doc.data()
        const userRole = userData.role
        if (counts.hasOwnProperty(userRole)) {
          counts[userRole]++
        }
      })

      return counts
    } catch (error) {
      console.error('Error getting user counts by role:', error)
      return {}
    }
  }

  // Subscribe to real-time role data with user counts
  subscribeToRoles(
    storeId: string,
    callback: (roles: Role[]) => void
  ): () => void {
    try {
      // Subscribe to users collection to get real-time user counts
      const usersQuery = query(collection(db, 'stores', storeId, 'users'))
      
      return onSnapshot(usersQuery, async (snapshot) => {
        // Count users by role
        const userCounts: Record<string, number> = {}
        
        // Initialize counts
        SYSTEM_ROLES.forEach(role => {
          userCounts[role.name] = 0
        })

        // Count users
        snapshot.forEach((doc) => {
          const userData = doc.data()
          const userRole = userData.role
          if (userCounts.hasOwnProperty(userRole)) {
            userCounts[userRole]++
          }
        })

        // Create roles with user counts
        const rolesWithCounts: Role[] = SYSTEM_ROLES.map(role => ({
          ...role,
          userCount: userCounts[role.name] || 0
        }))

        callback(rolesWithCounts)
      }, (error) => {
        console.error('Error fetching roles data:', error)
        // Return system roles with zero counts on error
        const rolesWithZeroCounts: Role[] = SYSTEM_ROLES.map(role => ({
          ...role,
          userCount: 0
        }))
        callback(rolesWithZeroCounts)
      })
    } catch (error) {
      console.error('Error setting up roles subscription:', error)
      return () => {}
    }
  }

  // Get role by name
  getRoleByName(roleName: string): Role | null {
    const systemRole = SYSTEM_ROLES.find(role => role.name === roleName)
    if (systemRole) {
      return {
        ...systemRole,
        userCount: 0 // Will be updated by subscription
      }
    }
    return null
  }

  // Check if user has permission
  hasPermission(userRole: string, permissionId: string): boolean {
    const role = SYSTEM_ROLES.find(r => r.name === userRole)
    return role ? role.permissions.includes(permissionId) : false
  }

  // Get permissions for role
  getPermissionsForRole(roleName: string): Permission[] {
    const role = SYSTEM_ROLES.find(r => r.name === roleName)
    if (!role) return []

    return SYSTEM_PERMISSIONS.filter(permission => 
      role.permissions.includes(permission.id)
    )
  }

  // Get permissions grouped by category
  getPermissionsByCategory(): Record<string, Permission[]> {
    return SYSTEM_PERMISSIONS.reduce((acc, permission) => {
      if (!acc[permission.category]) {
        acc[permission.category] = []
      }
      acc[permission.category].push(permission)
      return acc
    }, {} as Record<string, Permission[]>)
  }
}

// Export singleton instance
export const rolesPermissionsService = new RolesPermissionsService()
