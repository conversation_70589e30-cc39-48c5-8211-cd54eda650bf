import { 
  collection, 
  addDoc, 
  query, 
  orderBy, 
  limit, 
  onSnapshot, 
  where,
  serverTimestamp,
  Timestamp 
} from 'firebase/firestore'
import { db } from './firebase'

export interface ActivityLog {
  id: string
  timestamp: string
  userId: string
  userName: string
  userRole: string
  action: string
  resource: string
  resourceId?: string
  details: string
  ipAddress: string
  userAgent: string
  status: 'success' | 'error' | 'warning'
  storeId: string
  metadata?: Record<string, any>
}

export interface CreateActivityLogData {
  userId: string
  userName: string
  userRole: string
  action: string
  resource: string
  resourceId?: string
  details: string
  status: 'success' | 'error' | 'warning'
  storeId: string
  metadata?: Record<string, any>
}

class ActivityLoggerService {
  private storeId: string

  constructor() {
    this.storeId = import.meta.env.VITE_STORE_ID || 'womanza'
  }

  // Get user's IP address (simplified for demo)
  private async getClientIP(): Promise<string> {
    try {
      // In a real app, you might use a service like ipapi.co
      return '*************' // Fallback IP
    } catch (error) {
      return '0.0.0.0'
    }
  }

  // Get user agent
  private getUserAgent(): string {
    return navigator.userAgent || 'Unknown'
  }

  // Log an activity
  async logActivity(data: CreateActivityLogData): Promise<void> {
    try {
      const ipAddress = await this.getClientIP()
      const userAgent = this.getUserAgent()

      const activityData = {
        ...data,
        ipAddress,
        userAgent,
        timestamp: serverTimestamp(),
        createdAt: serverTimestamp()
      }

      await addDoc(collection(db, 'stores', data.storeId, 'activity_logs'), activityData)
    } catch (error) {
      console.error('Error logging activity:', error)
    }
  }

  // Subscribe to activity logs with real-time updates
  subscribeToActivityLogs(
    storeId: string,
    callback: (logs: ActivityLog[]) => void,
    options: {
      limitCount?: number
      userId?: string
      action?: string
      resource?: string
      status?: string
    } = {}
  ): () => void {
    try {
      let q = query(
        collection(db, 'stores', storeId, 'activity_logs'),
        orderBy('timestamp', 'desc')
      )

      // Apply filters
      if (options.userId) {
        q = query(q, where('userId', '==', options.userId))
      }
      if (options.action) {
        q = query(q, where('action', '==', options.action))
      }
      if (options.resource) {
        q = query(q, where('resource', '==', options.resource))
      }
      if (options.status) {
        q = query(q, where('status', '==', options.status))
      }

      // Apply limit
      if (options.limitCount) {
        q = query(q, limit(options.limitCount))
      }

      return onSnapshot(q, (snapshot) => {
        const logs: ActivityLog[] = []
        snapshot.forEach((doc) => {
          const data = doc.data()
          logs.push({
            id: doc.id,
            ...data,
            timestamp: data.timestamp instanceof Timestamp 
              ? data.timestamp.toDate().toISOString()
              : new Date().toISOString()
          } as ActivityLog)
        })
        callback(logs)
      }, (error) => {
        console.error('Error fetching activity logs:', error)
        callback([])
      })
    } catch (error) {
      console.error('Error setting up activity logs subscription:', error)
      return () => {}
    }
  }

  // Helper methods for common activities
  async logUserCreated(userId: string, userName: string, userRole: string, storeId: string, createdUserName: string, createdUserRole: string) {
    await this.logActivity({
      userId,
      userName,
      userRole,
      action: 'CREATE',
      resource: 'user',
      details: `Created new user: ${createdUserName} (${createdUserRole})`,
      status: 'success',
      storeId,
      metadata: { createdUserName, createdUserRole }
    })
  }

  async logUserUpdated(userId: string, userName: string, userRole: string, storeId: string, updatedUserId: string, updatedUserName: string) {
    await this.logActivity({
      userId,
      userName,
      userRole,
      action: 'UPDATE',
      resource: 'user',
      resourceId: updatedUserId,
      details: `Updated user: ${updatedUserName}`,
      status: 'success',
      storeId,
      metadata: { updatedUserId, updatedUserName }
    })
  }

  async logUserDeleted(userId: string, userName: string, userRole: string, storeId: string, deletedUserId: string, deletedUserName: string) {
    await this.logActivity({
      userId,
      userName,
      userRole,
      action: 'DELETE',
      resource: 'user',
      resourceId: deletedUserId,
      details: `Deleted user: ${deletedUserName}`,
      status: 'success',
      storeId,
      metadata: { deletedUserId, deletedUserName }
    })
  }

  async logLogin(userId: string, userName: string, userRole: string, storeId: string) {
    await this.logActivity({
      userId,
      userName,
      userRole,
      action: 'LOGIN',
      resource: 'auth',
      details: 'User logged in successfully',
      status: 'success',
      storeId
    })
  }

  async logLoginFailed(email: string, storeId: string) {
    await this.logActivity({
      userId: 'unknown',
      userName: 'Unknown User',
      userRole: 'unknown',
      action: 'LOGIN_FAILED',
      resource: 'auth',
      details: `Failed login attempt with email: ${email}`,
      status: 'error',
      storeId,
      metadata: { email }
    })
  }

  async logProductCreated(userId: string, userName: string, userRole: string, storeId: string, productId: string, productName: string) {
    await this.logActivity({
      userId,
      userName,
      userRole,
      action: 'CREATE',
      resource: 'product',
      resourceId: productId,
      details: `Created new product: ${productName}`,
      status: 'success',
      storeId,
      metadata: { productId, productName }
    })
  }

  async logProductUpdated(userId: string, userName: string, userRole: string, storeId: string, productId: string, productName: string, changes: string) {
    await this.logActivity({
      userId,
      userName,
      userRole,
      action: 'UPDATE',
      resource: 'product',
      resourceId: productId,
      details: `Updated product: ${productName} - ${changes}`,
      status: 'success',
      storeId,
      metadata: { productId, productName, changes }
    })
  }

  async logCategoryCreated(userId: string, userName: string, userRole: string, storeId: string, categoryId: string, categoryName: string) {
    await this.logActivity({
      userId,
      userName,
      userRole,
      action: 'CREATE',
      resource: 'category',
      resourceId: categoryId,
      details: `Created new category: ${categoryName}`,
      status: 'success',
      storeId,
      metadata: { categoryId, categoryName }
    })
  }

  async logCategoryDeleted(userId: string, userName: string, userRole: string, storeId: string, categoryId: string, categoryName: string) {
    await this.logActivity({
      userId,
      userName,
      userRole,
      action: 'DELETE',
      resource: 'category',
      resourceId: categoryId,
      details: `Deleted category: ${categoryName}`,
      status: 'success',
      storeId,
      metadata: { categoryId, categoryName }
    })
  }

  async logSettingsUpdated(userId: string, userName: string, userRole: string, storeId: string, settingType: string, details: string) {
    await this.logActivity({
      userId,
      userName,
      userRole,
      action: 'UPDATE',
      resource: 'settings',
      details: `Updated ${settingType}: ${details}`,
      status: 'success',
      storeId,
      metadata: { settingType }
    })
  }

  async logOrderViewed(userId: string, userName: string, userRole: string, storeId: string, orderId: string, orderNumber: string) {
    await this.logActivity({
      userId,
      userName,
      userRole,
      action: 'VIEW',
      resource: 'order',
      resourceId: orderId,
      details: `Viewed order details: ${orderNumber}`,
      status: 'success',
      storeId,
      metadata: { orderId, orderNumber }
    })
  }
}

// Export singleton instance
export const activityLogger = new ActivityLoggerService()
