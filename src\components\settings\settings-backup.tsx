import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Alert, AlertDescription } from '../ui/alert'
import { settingsService } from '../../lib/settings-service'
import { useStoreManagement } from '../../store/store-management'
import { useFirebaseAuthStore } from '../../store/firebase-auth'
import { toast } from 'react-hot-toast'
import {
  Download,
  Upload,
  Database,
  Clock,
  Shield,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Trash2
} from 'lucide-react'

interface SettingsBackupProps {
  storeId: string
  onRestore?: () => void
}

interface BackupItem {
  id: string
  created_at: string
  backup_type: 'manual' | 'automatic'
  settings: any
}

export function SettingsBackup({ storeId, onRestore }: SettingsBackupProps) {
  const { user } = useFirebaseAuthStore()
  const { currentStore } = useStoreManagement()
  const [backups, setBackups] = useState<BackupItem[]>([])
  const [loading, setLoading] = useState(false)
  const [creating, setCreating] = useState(false)
  const [restoring, setRestoring] = useState<string | null>(null)

  // Load backups
  const loadBackups = async () => {
    try {
      setLoading(true)
      // This would be implemented in the settings service
      // const backupList = await settingsService.getBackups(storeId)
      // setBackups(backupList)
      
      // For now, show placeholder
      setBackups([])
    } catch (error) {
      console.error('Failed to load backups:', error)
      toast.error('Failed to load backups')
    } finally {
      setLoading(false)
    }
  }

  // Create backup
  const createBackup = async () => {
    try {
      setCreating(true)
      const backupId = await settingsService.backupSettings(storeId)
      toast.success('Settings backup created successfully!')
      await loadBackups()
    } catch (error) {
      console.error('Failed to create backup:', error)
      toast.error('Failed to create backup')
    } finally {
      setCreating(false)
    }
  }

  // Restore from backup
  const restoreFromBackup = async (backupId: string) => {
    if (!confirm('Are you sure you want to restore from this backup? This will overwrite your current settings.')) {
      return
    }

    try {
      setRestoring(backupId)
      await settingsService.restoreSettings(storeId, backupId)
      toast.success('Settings restored successfully!')
      if (onRestore) {
        onRestore()
      }
    } catch (error) {
      console.error('Failed to restore backup:', error)
      toast.error('Failed to restore from backup')
    } finally {
      setRestoring(null)
    }
  }

  // Export settings as JSON
  const exportSettings = async () => {
    try {
      const settings = await settingsService.getSettings(storeId)
      const dataStr = JSON.stringify(settings, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = `${currentStore?.name || 'store'}-settings-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      toast.success('Settings exported successfully!')
    } catch (error) {
      console.error('Failed to export settings:', error)
      toast.error('Failed to export settings')
    }
  }

  React.useEffect(() => {
    loadBackups()
  }, [storeId])

  return (
    <div className="space-y-6">
      {/* Backup Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Settings Backup & Restore
          </CardTitle>
          <CardDescription>
            Create backups of your settings and restore from previous versions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button onClick={createBackup} disabled={creating}>
              <Database className="h-4 w-4 mr-2" />
              {creating ? 'Creating...' : 'Create Backup'}
            </Button>
            
            <Button variant="outline" onClick={exportSettings}>
              <Download className="h-4 w-4 mr-2" />
              Export Settings
            </Button>
            
            <Button variant="outline" onClick={loadBackups} disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>

          <Alert className="mt-4">
            <Shield className="h-4 w-4" />
            <AlertDescription>
              Backups are automatically created before major changes. Manual backups help you save specific configurations.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Backup History */}
      <Card>
        <CardHeader>
          <CardTitle>Backup History</CardTitle>
          <CardDescription>
            Previous backups of your store settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-gray-400" />
              <p className="text-gray-500">Loading backups...</p>
            </div>
          ) : backups.length === 0 ? (
            <div className="text-center py-8">
              <Database className="h-12 w-12 text-gray-400 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                No Backups Found
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                Create your first backup to get started
              </p>
              <Button onClick={createBackup} disabled={creating}>
                <Database className="h-4 w-4 mr-2" />
                Create First Backup
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              {backups.map((backup) => (
                <div
                  key={backup.id}
                  className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-medium">
                        Backup {backup.id.slice(0, 8)}
                      </h4>
                      <Badge variant={backup.backup_type === 'manual' ? 'default' : 'secondary'}>
                        {backup.backup_type}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {new Date(backup.created_at).toLocaleString()}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => restoreFromBackup(backup.id)}
                      disabled={restoring === backup.id}
                    >
                      {restoring === backup.id ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Restoring...
                        </>
                      ) : (
                        <>
                          <Upload className="h-4 w-4 mr-2" />
                          Restore
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Import Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Import Settings
          </CardTitle>
          <CardDescription>
            Import settings from a JSON file
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <input
              type="file"
              accept=".json"
              onChange={(e) => {
                const file = e.target.files?.[0]
                if (file) {
                  const reader = new FileReader()
                  reader.onload = (event) => {
                    try {
                      const settings = JSON.parse(event.target?.result as string)
                      // Validate and import settings
                      console.log('Imported settings:', settings)
                      toast.success('Settings imported successfully!')
                    } catch (error) {
                      toast.error('Invalid settings file')
                    }
                  }
                  reader.readAsText(file)
                }
              }}
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            />
            
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Warning:</strong> Importing settings will overwrite your current configuration. 
                Create a backup before importing.
              </AlertDescription>
            </Alert>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
