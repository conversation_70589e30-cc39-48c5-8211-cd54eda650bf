import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Textarea } from '../ui/textarea'
import { settingsService } from '../../lib/settings-service'
import { doc, getDoc } from 'firebase/firestore'
import { db } from '../../lib/firebase'
import { toast } from 'react-hot-toast'
import {
  Database,
  Eye,
  RefreshCw,
  Copy,
  Download,
  AlertCircle,
  CheckCircle
} from 'lucide-react'

interface FirebaseDebugProps {
  storeId: string
}

export function FirebaseDebug({ storeId }: FirebaseDebugProps) {
  const [settingsData, setSettingsData] = useState<any>(null)
  const [storeData, setStoreData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchFirebaseData = async () => {
    try {
      setLoading(true)
      setError(null)

      console.log('🔍 Fetching Firebase data for store:', storeId)

      // Fetch settings subcollection
      const settingsRef = doc(db, 'stores', storeId, 'settings', 'general')
      const settingsDoc = await getDoc(settingsRef)
      
      if (settingsDoc.exists()) {
        setSettingsData(settingsDoc.data())
        console.log('✅ Settings data:', settingsDoc.data())
      } else {
        setSettingsData(null)
        console.log('⚠️ No settings document found')
      }

      // Fetch main store document
      const storeRef = doc(db, 'stores', storeId)
      const storeDoc = await getDoc(storeRef)
      
      if (storeDoc.exists()) {
        setStoreData(storeDoc.data())
        console.log('✅ Store data:', storeDoc.data())
      } else {
        setStoreData(null)
        console.log('⚠️ No store document found')
      }

      toast.success('Firebase data fetched successfully!')
    } catch (error) {
      console.error('❌ Error fetching Firebase data:', error)
      setError(error.message)
      toast.error('Failed to fetch Firebase data')
    } finally {
      setLoading(false)
    }
  }

  const copyToClipboard = (data: any) => {
    navigator.clipboard.writeText(JSON.stringify(data, null, 2))
    toast.success('Data copied to clipboard!')
  }

  const downloadData = (data: any, filename: string) => {
    const dataStr = JSON.stringify(data, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = `${filename}-${storeId}-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    toast.success(`${filename} data downloaded!`)
  }

  return (
    <Card className="border-purple-200 bg-purple-50 dark:border-purple-800 dark:bg-purple-950">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Firebase Debug Panel
        </CardTitle>
        <CardDescription>
          View and debug Firebase document structure for store: {storeId}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Controls */}
        <div className="flex items-center gap-2">
          <Button 
            onClick={fetchFirebaseData} 
            disabled={loading}
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Fetching...' : 'Fetch Data'}
          </Button>
        </div>

        {error && (
          <div className="flex items-center gap-2 p-3 bg-red-100 dark:bg-red-900 rounded-lg">
            <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
            <span className="text-red-700 dark:text-red-300">{error}</span>
          </div>
        )}

        {/* Settings Document */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="font-medium flex items-center gap-2">
              Settings Document (stores/{storeId}/settings/general)
              {settingsData ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600" />
              )}
            </h4>
            {settingsData && (
              <div className="flex gap-1">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => copyToClipboard(settingsData)}
                >
                  <Copy className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => downloadData(settingsData, 'settings')}
                >
                  <Download className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
          
          {settingsData ? (
            <Textarea
              value={JSON.stringify(settingsData, null, 2)}
              readOnly
              className="font-mono text-xs h-40"
            />
          ) : (
            <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-lg text-center text-gray-500">
              No settings document found
            </div>
          )}
        </div>

        {/* Store Document */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="font-medium flex items-center gap-2">
              Store Document (stores/{storeId})
              {storeData ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600" />
              )}
            </h4>
            {storeData && (
              <div className="flex gap-1">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => copyToClipboard(storeData)}
                >
                  <Copy className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => downloadData(storeData, 'store')}
                >
                  <Download className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
          
          {storeData ? (
            <Textarea
              value={JSON.stringify(storeData, null, 2)}
              readOnly
              className="font-mono text-xs h-40"
            />
          ) : (
            <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-lg text-center text-gray-500">
              No store document found
            </div>
          )}
        </div>

        {/* Document Stats */}
        {(settingsData || storeData) && (
          <div className="grid grid-cols-2 gap-4 pt-4 border-t">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {settingsData ? Object.keys(settingsData).length : 0}
              </div>
              <div className="text-sm text-gray-500">Settings Fields</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {storeData ? Object.keys(storeData).length : 0}
              </div>
              <div className="text-sm text-gray-500">Store Fields</div>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="pt-4 border-t">
          <h5 className="font-medium mb-2">Quick Actions</h5>
          <div className="flex flex-wrap gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={async () => {
                try {
                  await settingsService.initializeSettings(storeId, 'Debug Test Store')
                  toast.success('Settings initialized!')
                  fetchFirebaseData()
                } catch (error) {
                  toast.error('Failed to initialize settings')
                }
              }}
            >
              Initialize Settings
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={async () => {
                try {
                  const testUpdate = {
                    name: `Debug Test ${Date.now()}`,
                    description: 'Test from debug panel'
                  }
                  await settingsService.updateSettings(storeId, testUpdate)
                  toast.success('Test update successful!')
                  fetchFirebaseData()
                } catch (error) {
                  toast.error('Test update failed')
                }
              }}
            >
              Test Update
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
