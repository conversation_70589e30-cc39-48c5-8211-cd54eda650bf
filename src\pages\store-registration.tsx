import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { Label } from '../components/ui/label'
import { Textarea } from '../components/ui/textarea'
import { Select } from '../components/ui/select'
import { Alert, AlertDescription } from '../components/ui/alert'
import { storeRegistrationService } from '../lib/store-registration-service'
import { 
  Store, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Building, 
  Globe,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react'

interface StoreRegistrationForm {
  // Store Information
  storeName: string
  storeSlug: string
  description: string
  country: string
  currency: string
  website?: string

  // Owner Information
  ownerName: string
  ownerEmail: string
  ownerPhone: string
  ownerPassword: string
  confirmPassword: string

  // Business Information
  businessAddress: string
  businessType: string
  expectedMonthlyOrders: string
}

const COUNTRIES = [
  { value: 'US', label: 'United States' },
  { value: 'CA', label: 'Canada' },
  { value: 'GB', label: 'United Kingdom' },
  { value: 'AU', label: 'Australia' },
  { value: 'PK', label: 'Pakistan' },
  { value: 'IN', label: 'India' },
  { value: 'AE', label: 'United Arab Emirates' }
]

const CURRENCIES = [
  { value: 'USD', label: 'US Dollar ($)' },
  { value: 'EUR', label: 'Euro (€)' },
  { value: 'GBP', label: 'British Pound (£)' },
  { value: 'CAD', label: 'Canadian Dollar (C$)' },
  { value: 'AUD', label: 'Australian Dollar (A$)' },
  { value: 'PKR', label: 'Pakistani Rupee (₨)' },
  { value: 'INR', label: 'Indian Rupee (₹)' },
  { value: 'AED', label: 'UAE Dirham (د.إ)' }
]

const BUSINESS_TYPES = [
  { value: 'jewelry', label: 'Jewelry & Accessories' },
  { value: 'fashion', label: 'Fashion & Apparel' },
  { value: 'electronics', label: 'Electronics' },
  { value: 'home', label: 'Home & Garden' },
  { value: 'beauty', label: 'Beauty & Cosmetics' },
  { value: 'sports', label: 'Sports & Fitness' },
  { value: 'books', label: 'Books & Media' },
  { value: 'other', label: 'Other' }
]

export function StoreRegistrationPage() {
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)
  const [submitted, setSubmitted] = useState(false)
  const [formData, setFormData] = useState<StoreRegistrationForm>({
    storeName: '',
    storeSlug: '',
    description: '',
    country: '',
    currency: '',
    website: '',
    ownerName: '',
    ownerEmail: '',
    ownerPhone: '',
    ownerPassword: '',
    confirmPassword: '',
    businessAddress: '',
    businessType: '',
    expectedMonthlyOrders: ''
  })

  const handleInputChange = (field: keyof StoreRegistrationForm, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // Auto-generate slug from store name
    if (field === 'storeName') {
      const slug = value.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()
      setFormData(prev => ({
        ...prev,
        storeSlug: slug
      }))
    }
  }

  const validateForm = (): string | null => {
    if (!formData.storeName.trim()) return 'Store name is required'
    if (!formData.storeSlug.trim()) return 'Store slug is required'
    if (!formData.description.trim()) return 'Store description is required'
    if (!formData.businessType) return 'Business type is required'
    if (!formData.country) return 'Country is required'
    if (!formData.currency) return 'Currency is required'

    if (!formData.ownerName.trim()) return 'Owner name is required'
    if (!formData.ownerEmail.trim()) return 'Owner email is required'
    if (!formData.ownerPhone.trim()) return 'Owner phone is required'
    if (!formData.ownerPassword) return 'Password is required'
    if (formData.ownerPassword.length < 8) return 'Password must be at least 8 characters'
    if (formData.ownerPassword !== formData.confirmPassword) return 'Passwords do not match'

    if (!formData.businessAddress.trim()) return 'Business address is required'
    if (!formData.expectedMonthlyOrders) return 'Expected monthly orders is required'

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(formData.ownerEmail)) return 'Please enter a valid email address'

    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    const validationError = validateForm()
    if (validationError) {
      toast.error(validationError)
      return
    }

    setLoading(true)
    
    try {
      await storeRegistrationService.submitRegistration({
        store: {
          name: formData.storeName,
          slug: formData.storeSlug,
          description: formData.description,
          category: formData.businessType,
          country: formData.country,
          currency: formData.currency,
          website: formData.website || undefined,
          businessAddress: formData.businessAddress,
          businessType: formData.businessType,
          expectedMonthlyOrders: formData.expectedMonthlyOrders
        },
        owner: {
          name: formData.ownerName,
          email: formData.ownerEmail,
          phone: formData.ownerPhone,
          password: formData.ownerPassword
        }
      })

      setSubmitted(true)
      toast.success('Registration submitted successfully!')
      
    } catch (error: any) {
      console.error('Registration error:', error)
      toast.error(error.message || 'Failed to submit registration')
    } finally {
      setLoading(false)
    }
  }

  if (submitted) {
    return (
      <div className="min-h-screen bg-primary-50 dark:bg-primary-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                Registration Submitted!
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Your store registration has been submitted for review. You will receive an email notification once your application is processed.
              </p>
              <Button onClick={() => navigate('/login')} className="w-full">
                Go to Login
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-primary-50 dark:bg-primary-900 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Register Your Store
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Join the Womanza platform and start managing your e-commerce business
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Store Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Store className="h-5 w-5" />
                Store Information
              </CardTitle>
              <CardDescription>
                Basic information about your store
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="storeName">Store Name *</Label>
                  <Input
                    id="storeName"
                    value={formData.storeName}
                    onChange={(e) => handleInputChange('storeName', e.target.value)}
                    placeholder="My Awesome Store"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="storeSlug">Store URL Slug *</Label>
                  <Input
                    id="storeSlug"
                    value={formData.storeSlug}
                    onChange={(e) => handleInputChange('storeSlug', e.target.value)}
                    placeholder="my-awesome-store"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    This will be your store's unique identifier
                  </p>
                </div>
              </div>

              <div>
                <Label htmlFor="description">Store Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Describe your store and what you sell..."
                  rows={3}
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="businessType">Business Type *</Label>
                  <Select
                    value={formData.businessType}
                    onValueChange={(value) => handleInputChange('businessType', value)}
                    placeholder="Select business type"
                    options={BUSINESS_TYPES}
                  />
                </div>
                <div>
                  <Label htmlFor="country">Country *</Label>
                  <Select
                    value={formData.country}
                    onValueChange={(value) => handleInputChange('country', value)}
                    placeholder="Select country"
                    options={COUNTRIES}
                  />
                </div>
                <div>
                  <Label htmlFor="currency">Currency *</Label>
                  <Select
                    value={formData.currency}
                    onValueChange={(value) => handleInputChange('currency', value)}
                    placeholder="Select currency"
                    options={CURRENCIES}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="website">Website (Optional)</Label>
                <Input
                  id="website"
                  type="url"
                  value={formData.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  placeholder="https://your-website.com"
                />
              </div>
            </CardContent>
          </Card>

          {/* Owner Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Owner Information
              </CardTitle>
              <CardDescription>
                Information about the store owner (this will be your super admin account)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="ownerName">Full Name *</Label>
                  <Input
                    id="ownerName"
                    value={formData.ownerName}
                    onChange={(e) => handleInputChange('ownerName', e.target.value)}
                    placeholder="John Doe"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="ownerPhone">Phone Number *</Label>
                  <Input
                    id="ownerPhone"
                    type="tel"
                    value={formData.ownerPhone}
                    onChange={(e) => handleInputChange('ownerPhone', e.target.value)}
                    placeholder="+****************"
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="ownerEmail">Email Address *</Label>
                <Input
                  id="ownerEmail"
                  type="email"
                  value={formData.ownerEmail}
                  onChange={(e) => handleInputChange('ownerEmail', e.target.value)}
                  placeholder="<EMAIL>"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  This will be your login email for the admin panel
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="ownerPassword">Password *</Label>
                  <Input
                    id="ownerPassword"
                    type="password"
                    value={formData.ownerPassword}
                    onChange={(e) => handleInputChange('ownerPassword', e.target.value)}
                    placeholder="Enter password"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Minimum 8 characters
                  </p>
                </div>
                <div>
                  <Label htmlFor="confirmPassword">Confirm Password *</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={formData.confirmPassword}
                    onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                    placeholder="Confirm password"
                    required
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Business Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Business Details
              </CardTitle>
              <CardDescription>
                Additional information about your business
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="businessAddress">Business Address *</Label>
                <Textarea
                  id="businessAddress"
                  value={formData.businessAddress}
                  onChange={(e) => handleInputChange('businessAddress', e.target.value)}
                  placeholder="Enter your complete business address..."
                  rows={2}
                  required
                />
              </div>

              <div>
                <Label htmlFor="expectedMonthlyOrders">Expected Monthly Orders *</Label>
                <Select
                  value={formData.expectedMonthlyOrders}
                  onValueChange={(value) => handleInputChange('expectedMonthlyOrders', value)}
                  placeholder="Select expected volume"
                  options={[
                    { value: "1-50", label: "1-50 orders" },
                    { value: "51-200", label: "51-200 orders" },
                    { value: "201-500", label: "201-500 orders" },
                    { value: "501-1000", label: "501-1000 orders" },
                    { value: "1000+", label: "1000+ orders" }
                  ]}
                />
              </div>
            </CardContent>
          </Card>

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Important:</strong> Your registration will be reviewed by our team. 
              You will receive an email notification once your application is approved or if additional information is needed.
            </AlertDescription>
          </Alert>

          <div className="flex gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/login')}
              className="flex-1"
            >
              Back to Login
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="flex-1"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Submitting...
                </>
              ) : (
                'Submit Registration'
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
