import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Alert, AlertDescription } from '../ui/alert'
import {
  CheckCircle,
  AlertTriangle,
  Clock,
  Database,
  Wifi,
  WifiOff
} from 'lucide-react'

interface SettingsStatusProps {
  storeId: string
  settings: any
  loading: boolean
  saving: boolean
  error: string | null
  connected: boolean
}

export function SettingsStatus({
  storeId,
  settings,
  loading,
  saving,
  error,
  connected
}: SettingsStatusProps) {
  const getStatusIcon = () => {
    if (error) return <AlertTriangle className="h-4 w-4 text-red-500" />
    if (saving) return <Clock className="h-4 w-4 text-yellow-500 animate-pulse" />
    if (loading) return <Database className="h-4 w-4 text-blue-500 animate-spin" />
    if (connected && settings) return <CheckCircle className="h-4 w-4 text-green-500" />
    return <WifiOff className="h-4 w-4 text-gray-500" />
  }

  const getStatusText = () => {
    if (error) return 'Connection Error'
    if (saving) return 'Saving Changes...'
    if (loading) return 'Loading Settings...'
    if (connected && settings) return 'Connected & Synced'
    return 'Disconnected'
  }

  const getStatusColor = () => {
    if (error) return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950'
    if (saving) return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950'
    if (loading) return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950'
    if (connected && settings) return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950'
    return 'border-gray-200 bg-gray-50 dark:border-gray-800 dark:bg-gray-950'
  }

  return (
    <Card className={`${getStatusColor()} transition-colors duration-200`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          {getStatusIcon()}
          Settings Status
        </CardTitle>
        <CardDescription className="text-xs">
          Real-time Firebase connection and sync status
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Status Overview */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Status:</span>
          <Badge variant={error ? 'destructive' : connected ? 'default' : 'secondary'}>
            {getStatusText()}
          </Badge>
        </div>

        {/* Store Information */}
        <div className="space-y-2 text-xs">
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Store ID:</span>
            <span className="font-mono">{storeId}</span>
          </div>
          
          {settings && (
            <>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Settings Fields:</span>
                <span>{Object.keys(settings).length}</span>
              </div>
              
              {settings.updated_at && (
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Last Updated:</span>
                  <span>{new Date(settings.updated_at).toLocaleTimeString()}</span>
                </div>
              )}
              
              {settings.name && (
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Store Name:</span>
                  <span className="truncate max-w-32">{settings.name}</span>
                </div>
              )}
            </>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive" className="py-2">
            <AlertTriangle className="h-3 w-3" />
            <AlertDescription className="text-xs">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Connection Indicator */}
        <div className="flex items-center gap-2 pt-2 border-t border-gray-200 dark:border-gray-700">
          {connected ? (
            <Wifi className="h-3 w-3 text-green-500" />
          ) : (
            <WifiOff className="h-3 w-3 text-red-500" />
          )}
          <span className="text-xs text-gray-600 dark:text-gray-400">
            Firebase {connected ? 'Connected' : 'Disconnected'}
          </span>
        </div>

        {/* Data Source Indicator */}
        {settings && (
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Source: {settings.created_at ? 'Firebase Database' : 'Default Values'}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
