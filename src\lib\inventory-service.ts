import { 
  collection, 
  doc, 
  getDocs, 
  getDoc,
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  serverTimestamp,
  increment,
  writeBatch,
  onSnapshot,
  Timestamp
} from 'firebase/firestore'
import { db } from './firebase'
import type { 
  InventoryItem, 
  InventoryLog, 
  InventoryAdjustment, 
  InventoryStats,
  LowStockAlert,
  ProductWithInventory
} from '../types/inventory'

// For now, always use sample data to avoid Firebase configuration issues
const isFirebaseConfigured = false

export class InventoryService {
  private storeId: string

  constructor(storeId: string) {
    this.storeId = storeId
  }

  // Get sample inventory data for demonstration
  private getSampleInventoryData(): {
    items: InventoryItem[]
    stats: InventoryStats
    alerts: LowStockAlert[]
  } {
    // Helper function to get stored stock from localStorage
    const getStoredStock = (productId: string, defaultStock: number) => {
      const storageKey = `sample-inventory-${productId}`
      const stored = localStorage.getItem(storageKey)
      return stored ? JSON.parse(stored).stock || defaultStock : defaultStock
    }

    const sampleItems: InventoryItem[] = [
      {
        id: 'sample-1',
        productId: 'sample-product-1',
        name: 'Gold Earrings',
        sku: 'GOLD-EAR-001',
        currentStock: getStoredStock('sample-product-1', 15),
        lowStockThreshold: 5,
        trackInventory: true,
        isOutOfStock: getStoredStock('sample-product-1', 15) === 0,
        lastUpdated: new Date().toISOString(),
        price: 12000,
        category: 'Jewelry',
        unit: 'pcs',
        status: 'active'
      },
      {
        id: 'sample-2',
        productId: 'sample-product-2',
        name: 'Silver Necklace',
        sku: 'SILVER-NECK-001',
        currentStock: getStoredStock('sample-product-2', 3),
        lowStockThreshold: 5,
        trackInventory: true,
        isOutOfStock: getStoredStock('sample-product-2', 3) === 0,
        lastUpdated: new Date().toISOString(),
        price: 8000,
        category: 'Jewelry',
        unit: 'pcs',
        status: 'active'
      },
      {
        id: 'sample-3',
        productId: 'sample-product-3',
        name: 'Diamond Ring',
        sku: 'DIAMOND-RING-001',
        currentStock: getStoredStock('sample-product-3', 0),
        lowStockThreshold: 2,
        trackInventory: true,
        isOutOfStock: getStoredStock('sample-product-3', 0) === 0,
        lastUpdated: new Date().toISOString(),
        price: 25000,
        category: 'Jewelry',
        unit: 'pcs',
        status: 'active'
      },
      {
        id: 'sample-4',
        productId: 'sample-product-4',
        name: 'Pearl Bracelet',
        sku: 'PEARL-BRAC-001',
        currentStock: getStoredStock('sample-product-4', 8),
        lowStockThreshold: 10,
        trackInventory: true,
        isOutOfStock: getStoredStock('sample-product-4', 8) === 0,
        lastUpdated: new Date().toISOString(),
        price: 6500,
        category: 'Jewelry',
        unit: 'pcs',
        status: 'active'
      }
    ]

    // Calculate dynamic stats based on current stock levels
    const lowStockItems = sampleItems.filter(item =>
      item.currentStock > 0 && item.currentStock <= item.lowStockThreshold
    ).length
    const outOfStockItems = sampleItems.filter(item => item.currentStock === 0).length

    const stats: InventoryStats = {
      totalProducts: 4,
      totalVariants: 0,
      lowStockItems,
      outOfStockItems,
      totalValue: sampleItems.reduce((sum, item) => sum + (item.currentStock * item.price), 0),
      recentAdjustments: 3
    }

    // Generate alerts for low stock items
    const alerts: LowStockAlert[] = sampleItems
      .filter(item => item.currentStock > 0 && item.currentStock <= item.lowStockThreshold)
      .map(item => ({
        id: `alert-${item.productId}`,
        productId: item.productId,
        productName: item.name,
        sku: item.sku,
        currentStock: item.currentStock,
        threshold: item.lowStockThreshold,
        severity: item.currentStock <= item.lowStockThreshold / 2 ? 'critical' : 'warning' as 'warning' | 'critical',
        createdAt: new Date().toISOString(),
        acknowledged: false
      }))

    console.log('📊 Returning sample inventory data:', {
      items: sampleItems.length,
      itemsData: sampleItems,
      stats,
      alerts: alerts.length,
      alertsData: alerts
    })

    return { items: sampleItems, stats, alerts }
  }

  // Get inventory overview with stats
  async getInventoryOverview(): Promise<{
    items: InventoryItem[]
    stats: InventoryStats
    alerts: LowStockAlert[]
  }> {
    if (!isFirebaseConfigured) {
      // Return sample data when Firebase is not configured
      return this.getSampleInventoryData()
    }

    try {
      console.log('📊 Fetching inventory overview for store:', this.storeId)

      // Get all products with simple query to avoid index issues
      const productsRef = collection(db, 'stores', this.storeId, 'products')
      let productsSnapshot

      try {
        const productsQuery = query(productsRef, orderBy('updatedAt', 'desc'))
        productsSnapshot = await getDocs(productsQuery)
      } catch (queryError) {
        console.warn('Complex query failed, using simple query:', queryError)
        // Fallback to simple query without ordering
        productsSnapshot = await getDocs(productsRef)
      }
      const items: InventoryItem[] = []
      let totalValue = 0
      let lowStockCount = 0
      let outOfStockCount = 0

      for (const productDoc of productsSnapshot.docs) {
        const productData = productDoc.data()
        const product = {
          id: productDoc.id,
          ...productData,
          // Set default inventory values if not present
          trackInventory: productData.trackInventory ?? true,
          inventoryQuantity: productData.inventoryQuantity ?? 0,
          lowStockThreshold: productData.lowStockThreshold ?? 5,
          isOutOfStock: productData.isOutOfStock ?? false,
          hasVariants: productData.hasVariants ?? false,
          totalStock: productData.totalStock ?? productData.inventoryQuantity ?? 0,
          lastStockUpdate: productData.lastStockUpdate ?? productData.updatedAt
        } as ProductWithInventory

        // Skip products that don't track inventory
        if (!product.trackInventory) continue

        if (product.hasVariants && product.variants) {
          // Handle variants
          for (const variant of product.variants) {
            // Set default values for variant inventory fields
            const variantWithDefaults = {
              ...variant,
              stock: variant.stock ?? 0,
              lowStockThreshold: variant.lowStockThreshold ?? 5,
              trackInventory: variant.trackInventory ?? true,
              isOutOfStock: variant.isOutOfStock ?? (variant.stock === 0),
              price: variant.price ?? 0,
              sku: variant.sku ?? `${product.sku || product.id}-${variant.id}`
            }

            // Skip variants that don't track inventory
            if (!variantWithDefaults.trackInventory) continue

            const item: InventoryItem = {
              id: `${product.id}-${variant.id}`,
              productId: product.id,
              variantId: variant.id,
              name: `${product.name} - ${variant.name || 'Variant'}`,
              sku: variantWithDefaults.sku,
              currentStock: variantWithDefaults.stock,
              lowStockThreshold: variantWithDefaults.lowStockThreshold,
              trackInventory: variantWithDefaults.trackInventory,
              isOutOfStock: variantWithDefaults.isOutOfStock,
              lastUpdated: product.lastStockUpdate || product.updatedAt || new Date().toISOString(),
              price: variantWithDefaults.price,
              image: product.images?.[0],
              category: product.categoryName,
              unit: 'pcs',
              status: product.status === 'active' ? 'active' : 'inactive'
            }

            items.push(item)
            totalValue += variantWithDefaults.stock * variantWithDefaults.price

            if (variantWithDefaults.isOutOfStock) outOfStockCount++
            else if (variantWithDefaults.stock <= variantWithDefaults.lowStockThreshold) lowStockCount++
          }
        } else {
          // Handle simple products
          const item: InventoryItem = {
            id: product.id,
            productId: product.id,
            name: product.name || 'Unnamed Product',
            sku: product.sku || product.id,
            currentStock: product.inventoryQuantity,
            lowStockThreshold: product.lowStockThreshold,
            trackInventory: product.trackInventory,
            isOutOfStock: product.isOutOfStock,
            lastUpdated: product.lastStockUpdate || product.updatedAt || new Date().toISOString(),
            price: product.price || 0,
            image: product.images?.[0],
            category: product.categoryName,
            unit: 'pcs',
            status: product.status === 'active' ? 'active' : 'inactive'
          }

          items.push(item)
          totalValue += product.inventoryQuantity * (product.price || 0)

          if (product.isOutOfStock) outOfStockCount++
          else if (product.inventoryQuantity <= product.lowStockThreshold) lowStockCount++
        }
      }

      // Get recent adjustments count (last 7 days) - simplified to avoid index issues
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

      const logsRef = collection(db, 'stores', this.storeId, 'inventoryLogs')
      let recentLogsSnapshot

      try {
        // Try to get recent logs, but don't fail if collection doesn't exist
        const recentLogsQuery = query(
          logsRef,
          orderBy('timestamp', 'desc'),
          limit(100) // Get recent logs and filter in memory
        )
        recentLogsSnapshot = await getDocs(recentLogsQuery)
      } catch (error) {
        console.warn('Could not fetch inventory logs, using default count:', error)
        recentLogsSnapshot = { size: 0 }
      }

      // Count recent adjustments from the logs we fetched
      let recentAdjustmentsCount = 0
      if (recentLogsSnapshot.docs) {
        const sevenDaysAgoTimestamp = Timestamp.fromDate(sevenDaysAgo)
        recentAdjustmentsCount = recentLogsSnapshot.docs.filter(doc => {
          const logData = doc.data()
          const logTimestamp = logData.timestamp
          return logTimestamp &&
                 logData.action === 'adjustment' &&
                 logTimestamp.seconds >= sevenDaysAgoTimestamp.seconds
        }).length
      }

      const stats: InventoryStats = {
        totalProducts: items.filter(item => !item.variantId).length,
        totalVariants: items.filter(item => item.variantId).length,
        lowStockItems: lowStockCount,
        outOfStockItems: outOfStockCount,
        totalValue: Math.round(totalValue),
        recentAdjustments: recentAdjustmentsCount
      }

      // Generate low stock alerts
      const alerts: LowStockAlert[] = items
        .filter(item => item.currentStock <= item.lowStockThreshold && !item.isOutOfStock)
        .map(item => ({
          id: `alert-${item.id}`,
          productId: item.productId,
          variantId: item.variantId,
          productName: item.name,
          sku: item.sku,
          currentStock: item.currentStock,
          threshold: item.lowStockThreshold,
          severity: item.currentStock === 0 ? 'critical' : 'warning',
          createdAt: new Date().toISOString(),
          acknowledged: false
        }))

      console.log('✅ Inventory overview fetched:', {
        items: items.length,
        itemsData: items,
        stats,
        alerts: alerts.length,
        alertsData: alerts
      })

      return { items, stats, alerts }
    } catch (error) {
      console.error('❌ Error fetching inventory overview:', error)
      console.log('🔄 Falling back to sample data')

      // Return sample data as fallback
      return this.getSampleInventoryData()
    }
  }

  // Adjust inventory for a product or variant
  async adjustInventory(adjustment: InventoryAdjustment, userId: string, userName: string): Promise<void> {
    console.log('📝 Processing inventory adjustment:', adjustment)

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // For sample products, update in-memory data and simulate Firebase storage
    if (adjustment.productId.startsWith('sample-product-')) {
      await this.adjustSampleInventory(adjustment, userId, userName)
      return
    }

    // For real products, use Firebase
    if (!isFirebaseConfigured) {
      console.warn('⚠️ Firebase not configured, creating sample inventory entry')
      // Create a sample inventory entry for demo
      await this.createSampleInventoryEntry(adjustment, userId, userName)
      return
    }

    try {
      const batch = writeBatch(db)

      // Get current product data
      const productRef = doc(db, 'stores', this.storeId, 'products', adjustment.productId)
      const productDoc = await getDoc(productRef)

      if (!productDoc.exists()) {
        throw new Error('Product not found')
      }

      const product = { id: productDoc.id, ...productDoc.data() } as ProductWithInventory
      let previousStock = 0
      let newStock = 0

      if (adjustment.variantId && product.variants) {
        // Handle variant adjustment
        const variantIndex = product.variants.findIndex(v => v.id === adjustment.variantId)
        if (variantIndex === -1) {
          throw new Error('Variant not found')
        }

        const variant = product.variants[variantIndex]
        previousStock = variant.stock

        switch (adjustment.action) {
          case 'increase':
            newStock = previousStock + adjustment.quantity
            break
          case 'decrease':
            newStock = Math.max(0, previousStock - adjustment.quantity)
            break
          case 'set':
            newStock = adjustment.quantity
            break
        }

        // Update variant
        const updatedVariants = [...product.variants]
        updatedVariants[variantIndex] = {
          ...variant,
          stock: newStock,
          isOutOfStock: newStock === 0
        }

        // Calculate total stock
        const totalStock = updatedVariants.reduce((sum, v) => sum + v.stock, 0)

        batch.update(productRef, {
          variants: updatedVariants,
          totalStock,
          isOutOfStock: totalStock === 0,
          lastStockUpdate: serverTimestamp(),
          updatedAt: serverTimestamp()
        })
      } else {
        // Handle simple product adjustment
        previousStock = product.inventoryQuantity

        switch (adjustment.action) {
          case 'increase':
            newStock = previousStock + adjustment.quantity
            break
          case 'decrease':
            newStock = Math.max(0, previousStock - adjustment.quantity)
            break
          case 'set':
            newStock = adjustment.quantity
            break
        }

        batch.update(productRef, {
          inventoryQuantity: newStock,
          isOutOfStock: newStock === 0,
          lastStockUpdate: serverTimestamp(),
          updatedAt: serverTimestamp()
        })
      }

      // Create inventory log
      const logRef = collection(db, 'stores', this.storeId, 'inventoryLogs')
      const logData: Omit<InventoryLog, 'id'> = {
        productId: adjustment.productId,
        variantId: adjustment.variantId,
        action: 'adjustment',
        quantity: adjustment.action === 'set' ? newStock - previousStock : 
                 adjustment.action === 'increase' ? adjustment.quantity : -adjustment.quantity,
        previousStock,
        newStock,
        userId,
        userName,
        timestamp: new Date().toISOString(),
        note: adjustment.note,
        reason: adjustment.reason
      }

      batch.set(doc(logRef), {
        ...logData,
        timestamp: serverTimestamp()
      })

      await batch.commit()

      console.log('✅ Inventory adjusted successfully:', {
        productId: adjustment.productId,
        variantId: adjustment.variantId,
        previousStock,
        newStock
      })
    } catch (error) {
      console.error('❌ Error adjusting inventory:', error)
      throw new Error('Failed to adjust inventory')
    }
  }

  // Get inventory logs for a product
  async getInventoryLogs(productId: string, variantId?: string, limitCount = 50): Promise<InventoryLog[]> {
    // For sample products, return sample logs
    if (productId.startsWith('sample-product-')) {
      return this.getSampleLogs(productId)
    }

    if (!isFirebaseConfigured) {
      console.warn('⚠️ Firebase not configured, returning empty logs')
      return []
    }

    try {
      const logsRef = collection(db, 'stores', this.storeId, 'inventoryLogs')
      let logsQuery = query(
        logsRef,
        where('productId', '==', productId),
        orderBy('timestamp', 'desc'),
        limit(limitCount)
      )

      if (variantId) {
        logsQuery = query(
          logsRef,
          where('productId', '==', productId),
          where('variantId', '==', variantId),
          orderBy('timestamp', 'desc'),
          limit(limitCount)
        )
      }

      const snapshot = await getDocs(logsQuery)
      const logs: InventoryLog[] = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate?.()?.toISOString() || doc.data().timestamp
      })) as InventoryLog[]

      return logs
    } catch (error) {
      console.error('❌ Error fetching inventory logs:', error)
      return [] // Return empty array instead of throwing
    }
  }

  // Get sample logs for demo
  private getSampleLogs(productId: string): InventoryLog[] {
    // Try to get stored logs from localStorage
    const storageKey = `sample-inventory-${productId}`
    const stored = localStorage.getItem(storageKey)

    if (stored) {
      const data = JSON.parse(stored)
      if (data.logs && data.logs.length > 0) {
        return data.logs
      }
    }

    // Return default logs if no stored data
    return [
      {
        id: `log-${productId}-1`,
        productId,
        action: 'adjustment',
        quantity: 5,
        previousStock: 10,
        newStock: 15,
        reason: 'restock',
        note: 'Weekly restock from supplier',
        timestamp: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        userId: 'admin',
        userName: 'Admin User'
      },
      {
        id: `log-${productId}-2`,
        productId,
        action: 'adjustment',
        quantity: -2,
        previousStock: 15,
        newStock: 13,
        reason: 'sale',
        note: 'Sold to customer',
        timestamp: new Date(Date.now() - 43200000).toISOString(), // 12 hours ago
        userId: 'admin',
        userName: 'Admin User'
      }
    ]
  }

  // Adjust sample inventory (for demo purposes)
  private async adjustSampleInventory(adjustment: InventoryAdjustment, userId: string, userName: string): Promise<void> {
    console.log('📝 Adjusting sample inventory:', adjustment)

    // Store in localStorage for persistence across page reloads
    const storageKey = `sample-inventory-${adjustment.productId}`
    const currentData = localStorage.getItem(storageKey)
    let inventoryData = currentData ? JSON.parse(currentData) : { stock: 0, logs: [] }

    const previousStock = inventoryData.stock || 0
    let newStock = previousStock

    switch (adjustment.action) {
      case 'increase':
        newStock = previousStock + adjustment.quantity
        break
      case 'decrease':
        newStock = Math.max(0, previousStock - adjustment.quantity)
        break
      case 'set':
        newStock = adjustment.quantity
        break
    }

    // Create log entry
    const logEntry = {
      id: `log-${Date.now()}`,
      productId: adjustment.productId,
      variantId: adjustment.variantId,
      action: 'adjustment',
      quantity: adjustment.action === 'set' ? newStock - previousStock :
               adjustment.action === 'increase' ? adjustment.quantity : -adjustment.quantity,
      previousStock,
      newStock,
      reason: adjustment.reason,
      note: adjustment.note || '',
      timestamp: new Date().toISOString(),
      userId,
      userName
    }

    // Update inventory data
    inventoryData.stock = newStock
    inventoryData.logs = inventoryData.logs || []
    inventoryData.logs.unshift(logEntry) // Add to beginning
    inventoryData.logs = inventoryData.logs.slice(0, 50) // Keep only last 50 logs

    // Save to localStorage
    localStorage.setItem(storageKey, JSON.stringify(inventoryData))

    console.log('✅ Sample inventory adjustment completed:', {
      productId: adjustment.productId,
      previousStock,
      newStock,
      action: adjustment.action,
      quantity: adjustment.quantity
    })
  }

  // Create sample inventory entry for real products when Firebase is not configured
  private async createSampleInventoryEntry(adjustment: InventoryAdjustment, userId: string, userName: string): Promise<void> {
    console.log('📝 Creating sample inventory entry for real product:', adjustment.productId)

    // Store in localStorage with a different key pattern for real products
    const storageKey = `inventory-${adjustment.productId}`
    const currentData = localStorage.getItem(storageKey)
    let inventoryData = currentData ? JSON.parse(currentData) : {
      productId: adjustment.productId,
      stock: 0,
      logs: [],
      createdAt: new Date().toISOString()
    }

    const previousStock = inventoryData.stock || 0
    let newStock = previousStock

    switch (adjustment.action) {
      case 'increase':
        newStock = previousStock + adjustment.quantity
        break
      case 'decrease':
        newStock = Math.max(0, previousStock - adjustment.quantity)
        break
      case 'set':
        newStock = adjustment.quantity
        break
    }

    // Create log entry
    const logEntry = {
      id: `log-${Date.now()}`,
      productId: adjustment.productId,
      variantId: adjustment.variantId,
      action: 'adjustment',
      quantity: adjustment.action === 'set' ? newStock - previousStock :
               adjustment.action === 'increase' ? adjustment.quantity : -adjustment.quantity,
      previousStock,
      newStock,
      reason: adjustment.reason,
      note: adjustment.note || '',
      timestamp: new Date().toISOString(),
      userId,
      userName
    }

    // Update inventory data
    inventoryData.stock = newStock
    inventoryData.logs = inventoryData.logs || []
    inventoryData.logs.unshift(logEntry)
    inventoryData.logs = inventoryData.logs.slice(0, 50)
    inventoryData.updatedAt = new Date().toISOString()

    // Save to localStorage
    localStorage.setItem(storageKey, JSON.stringify(inventoryData))

    console.log('✅ Sample inventory entry created:', {
      productId: adjustment.productId,
      previousStock,
      newStock,
      action: adjustment.action,
      quantity: adjustment.quantity
    })
  }

  // Update low stock threshold
  async updateLowStockThreshold(productId: string, threshold: number, variantId?: string): Promise<void> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      const productRef = doc(db, 'stores', this.storeId, 'products', productId)
      
      if (variantId) {
        const productDoc = await getDoc(productRef)
        if (!productDoc.exists()) {
          throw new Error('Product not found')
        }

        const product = { id: productDoc.id, ...productDoc.data() } as ProductWithInventory
        if (!product.variants) {
          throw new Error('Product has no variants')
        }

        const updatedVariants = product.variants.map(variant =>
          variant.id === variantId
            ? { ...variant, lowStockThreshold: threshold }
            : variant
        )

        await updateDoc(productRef, {
          variants: updatedVariants,
          updatedAt: serverTimestamp()
        })
      } else {
        await updateDoc(productRef, {
          lowStockThreshold: threshold,
          updatedAt: serverTimestamp()
        })
      }

      console.log('✅ Low stock threshold updated:', { productId, variantId, threshold })
    } catch (error) {
      console.error('❌ Error updating low stock threshold:', error)
      throw new Error('Failed to update low stock threshold')
    }
  }

  // Toggle inventory tracking
  async toggleInventoryTracking(productId: string, trackInventory: boolean, variantId?: string): Promise<void> {
    if (!isFirebaseConfigured) {
      throw new Error('Firebase not configured')
    }

    try {
      const productRef = doc(db, 'stores', this.storeId, 'products', productId)
      
      if (variantId) {
        const productDoc = await getDoc(productRef)
        if (!productDoc.exists()) {
          throw new Error('Product not found')
        }

        const product = { id: productDoc.id, ...productDoc.data() } as ProductWithInventory
        if (!product.variants) {
          throw new Error('Product has no variants')
        }

        const updatedVariants = product.variants.map(variant =>
          variant.id === variantId
            ? { ...variant, trackInventory }
            : variant
        )

        await updateDoc(productRef, {
          variants: updatedVariants,
          updatedAt: serverTimestamp()
        })
      } else {
        await updateDoc(productRef, {
          trackInventory,
          updatedAt: serverTimestamp()
        })
      }

      console.log('✅ Inventory tracking toggled:', { productId, variantId, trackInventory })
    } catch (error) {
      console.error('❌ Error toggling inventory tracking:', error)
      throw new Error('Failed to toggle inventory tracking')
    }
  }
}
