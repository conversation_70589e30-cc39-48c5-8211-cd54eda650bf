import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'
import {
  Users, Plus, Search, Download, RefreshCw,
  CheckSquare, X, Trash2, Database, Edit
} from 'lucide-react'

import { Button } from '../components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { DataTable } from '../components/ui/data-table'
import { useCustomers } from '../hooks/use-customers'
import { formatCurrency, formatDateTime } from '../lib/utils'
import type { Customer } from '../lib/firebase'

interface Column<T> {
  key: string
  title: string
  sortable?: boolean
  render?: (value: any, item: T) => React.ReactNode
}

export default function CustomersPage() {
  const navigate = useNavigate()

  // Permissions (simplified for now)
  const canView = true

  // Customer data
  const {
    customers,
    loading,
    refreshing,
    refreshCustomers,
    createCustomer,
    deleteCustomer
  } = useCustomers({ realtime: true })

  // Local state
  const [initializingData, setInitializingData] = useState(false)

  // Initialize sample data
  const handleInitializeSampleData = async () => {

    setInitializingData(true)
    try {
      const sampleCustomers = [
        {
          fullName: 'Ayesha Khan',
          email: '<EMAIL>',
          phone: '+92 300 1234567',
          isRegistered: true,
          group: 'VIP',
          lifetimeValue: 25000,
          orderCount: 12,
          status: 'active' as const,
          notes: 'VIP customer, prefers gold jewelry',
          tags: ['high-value', 'gold-lover'],
          address: {
            shipping: { line1: 'House 123, Block A', city: 'Karachi', zip: '75500', country: 'Pakistan' }
          }
        }
      ]

      for (const customer of sampleCustomers) {
        await createCustomer(customer)
      }

      toast.success('Sample customer data initialized!')
    } catch (error) {
      toast.error('Failed to initialize sample data')
    } finally {
      setInitializingData(false)
    }
  }

  // Status badge component
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: {
        bg: 'bg-green-100 dark:bg-green-900/30',
        text: 'text-green-800 dark:text-green-300',
        border: 'border-green-200 dark:border-green-700'
      },
      inactive: {
        bg: 'bg-gray-100 dark:bg-gray-800',
        text: 'text-gray-700 dark:text-gray-300',
        border: 'border-gray-200 dark:border-gray-600'
      },
      blocked: {
        bg: 'bg-red-100 dark:bg-red-900/30',
        text: 'text-red-800 dark:text-red-300',
        border: 'border-red-200 dark:border-red-700'
      }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active

    return (
      <span className={`px-3 py-1 text-xs font-medium rounded-full border ${config.bg} ${config.text} ${config.border}`}>
        {status || 'active'}
      </span>
    )
  }

  // Group badge component
  const getGroupBadge = (group: string) => {
    if (!group) return (
      <span className="px-3 py-1 text-xs font-medium rounded-full bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-600">
        No Group
      </span>
    )

    const groupConfig = {
      VIP: {
        bg: 'bg-yellow-100 dark:bg-yellow-900/30',
        text: 'text-yellow-800 dark:text-yellow-300',
        border: 'border-yellow-200 dark:border-yellow-700'
      },
      Loyal: {
        bg: 'bg-blue-100 dark:bg-blue-900/30',
        text: 'text-blue-800 dark:text-blue-300',
        border: 'border-blue-200 dark:border-blue-700'
      },
      Wholesale: {
        bg: 'bg-purple-100 dark:bg-purple-900/30',
        text: 'text-purple-800 dark:text-purple-300',
        border: 'border-purple-200 dark:border-purple-700'
      },
      Retail: {
        bg: 'bg-green-100 dark:bg-green-900/30',
        text: 'text-green-800 dark:text-green-300',
        border: 'border-green-200 dark:border-green-700'
      },
      New: {
        bg: 'bg-indigo-100 dark:bg-indigo-900/30',
        text: 'text-indigo-800 dark:text-indigo-300',
        border: 'border-indigo-200 dark:border-indigo-700'
      },
      'At Risk': {
        bg: 'bg-red-100 dark:bg-red-900/30',
        text: 'text-red-800 dark:text-red-300',
        border: 'border-red-200 dark:border-red-700'
      }
    }

    const config = groupConfig[group as keyof typeof groupConfig] || groupConfig.Loyal

    return (
      <span className={`px-3 py-1 text-xs font-medium rounded-full border ${config.bg} ${config.text} ${config.border}`}>
        {group}
      </span>
    )
  }

  // Table columns
  const columns: Column<Customer>[] = [
    {
      key: 'name',
      title: 'Customer',
      sortable: true,
      render: (_, customer) => (
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-medium">
            {customer.fullName?.charAt(0)?.toUpperCase() || customer.email?.charAt(0)?.toUpperCase() || '?'}
          </div>
          <div>
            <div className="font-medium">{customer.fullName || 'Unnamed Customer'}</div>
            <div className="text-sm text-gray-500">{customer.email}</div>
            {customer.phone && <div className="text-sm text-gray-500">{customer.phone}</div>}
          </div>
        </div>
      )
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      render: (_, customer) => getStatusBadge(customer.status || 'active')
    },
    {
      key: 'group',
      title: 'Group',
      render: (_, customer) => getGroupBadge(customer.group || '')
    },
    {
      key: 'totalSpent',
      title: 'Total Spent',
      sortable: true,
      render: (_, customer) => (
        <div className="text-right">
          <div className="font-medium">
            {formatCurrency(customer.lifetimeValue || customer.totalSpent || 0)}
          </div>
          <div className="text-sm text-gray-500">
            {customer.orderCount || 0} orders
          </div>
        </div>
      )
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (_, customer) => (
        <div className="flex items-center gap-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => navigate(`/admin/customers/${customer.id}`)}
            className="h-8 w-8 p-0"
          >
            <Search className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => navigate(`/admin/customers/${customer.id}/edit`)}
            className="h-8 w-8 p-0"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => {
              if (confirm('Delete this customer? This action cannot be undone.')) {
                deleteCustomer(customer.id)
              }
            }}
            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ]

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">Customers</h1>
          <p className="text-gray-500 dark:text-gray-400">Manage your customer relationships and analytics</p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={refreshCustomers}
            variant="outline"
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          {customers.length === 0 && (
            <Button
              onClick={handleInitializeSampleData}
              variant="outline"
              disabled={initializingData}
            >
              <Database className={`h-4 w-4 mr-2 ${initializingData ? 'animate-spin' : ''}`} />
              Add Sample Data
            </Button>
          )}
          <Button
            onClick={() => navigate('/admin/customers/new')}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Customer
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-900 dark:text-blue-100">Total Customers</CardTitle>
            <div className="p-2 bg-blue-100 dark:bg-blue-800/50 rounded-lg">
              <Users className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">{customers.length}</div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-900 dark:text-green-100">Active</CardTitle>
            <div className="p-2 bg-green-100 dark:bg-green-800/50 rounded-lg">
              <CheckSquare className="h-4 w-4 text-green-600 dark:text-green-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900 dark:text-green-100">
              {customers.filter(c => c.status === 'active').length}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 border-yellow-200 dark:border-yellow-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-yellow-900 dark:text-yellow-100">VIP</CardTitle>
            <div className="p-2 bg-yellow-100 dark:bg-yellow-800/50 rounded-lg">
              <Database className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
              {customers.filter(c => c.group === 'VIP').length}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border-purple-200 dark:border-purple-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-900 dark:text-purple-100">New This Month</CardTitle>
            <div className="p-2 bg-purple-100 dark:bg-purple-800/50 rounded-lg">
              <Plus className="h-4 w-4 text-purple-600 dark:text-purple-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">
              {customers.filter(c => {
                const createdAt = new Date(c.createdAt)
                const now = new Date()
                const monthAgo = new Date(now.getFullYear(), now.getMonth(), 1)
                return createdAt >= monthAgo
              }).length}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 border-orange-200 dark:border-orange-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-900 dark:text-orange-100">At Risk</CardTitle>
            <div className="p-2 bg-orange-100 dark:bg-orange-800/50 rounded-lg">
              <X className="h-4 w-4 text-orange-600 dark:text-orange-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-900 dark:text-orange-100">
              {customers.filter(c => c.group === 'At Risk').length}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20 border-indigo-200 dark:border-indigo-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-indigo-900 dark:text-indigo-100">Total Revenue</CardTitle>
            <div className="p-2 bg-indigo-100 dark:bg-indigo-800/50 rounded-lg">
              <span className="text-indigo-600 dark:text-indigo-400 font-bold">₨</span>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-indigo-900 dark:text-indigo-100">
              {formatCurrency(customers.reduce((sum, c) => sum + (c.lifetimeValue || 0), 0))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Customers Table */}
      <DataTable
        data={customers}
        columns={columns}
        loading={loading}
        searchable={true}
        filterable={true}
        exportable={true}
        emptyState={{
          title: 'No customers found',
          description: 'Get started by adding your first customer.',
          icon: <Users className="h-6 w-6 text-gray-400" />
        }}
      />
    </div>
  )
}